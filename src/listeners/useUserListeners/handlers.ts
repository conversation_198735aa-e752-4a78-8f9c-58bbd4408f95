const InAppNotificationHandler = (showInAppToast: Function, data: any, helperFuncs: any) => {
  printDebug("data", data);

  showInAppToast({
    visible: true,
    title: data.title,
    description: data?.body ?? '',
    imageUrl: data?.imageUrl ?? '',
    onAccept: helperFuncs?.onAccept ?? null,
    onReject: helperFuncs?.onReject ?? null,
    onClose: helperFuncs?.onClose ?? null,
  });
};

export { InAppNotificationHandler };
