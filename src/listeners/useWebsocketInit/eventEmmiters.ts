import { EventManagerType } from '@/src/core/event';
import { CHANNEL_TYPES } from './constants';
import { CHANNEL_TYPE } from './types';

const parseData = (data: any) => {
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (error) {
      return data;
    }
  }
  return data;
};

const handleEventEmmiter = (
  channel: CHANNEL_TYPE,
  emmiter: EventManagerType,
  data: any,
) => {
  switch (channel) {
    case CHANNEL_TYPES.USER_CHANNEL:
      const eventData = parseData(data?.event);
      if (typeof eventData === 'object') {
        eventData.opponentUser =
          eventData?.user ?? eventData?.opponent ?? EMPTY_OBJECT;
      }
      emmiter.emit(data?.type, eventData);
      break;
    // TODO @Rishav add other channels
    // case CHANNEL_TYPES.GAME_CHANNEL:
    //     emmiter.emit(data?.type, data?.data);
    //     break;
    // case CHANNEL_TYPES.SHOWDOWN_CHANNEL:
    //     emmiter.emit(data?.type, data?.data);
    //     break;
    case CHANNEL_TYPES.NON_CHANNEL:
      break;
    default:
      break;
  }
};

export default handleEventEmmiter;
