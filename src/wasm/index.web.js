let wasm;

const heap = new Array(128).fill(undefined);

heap.push(undefined, null, true, false);

function getObject(idx) {
  return heap[idx];
}

let heap_next = heap.length;

function addHeapObject(obj) {
  if (heap_next === heap.length) heap.push(heap.length + 1);
  const idx = heap_next;
  heap_next = heap[idx];

  heap[idx] = obj;
  return idx;
}

function handleError(f, args) {
  try {
    return f.apply(this, args);
  } catch (e) {
    wasm.__wbindgen_export_0(addHeapObject(e));
  }
}

const cachedTextDecoder =
  typeof TextDecoder !== 'undefined'
    ? new TextDecoder('utf-8', { ignoreBOM: true, fatal: true })
    : {
        decode: () => {
          throw Error('TextDecoder not available');
        },
      };

if (typeof TextDecoder !== 'undefined') {
  cachedTextDecoder.decode();
}

let cachedUint8ArrayMemory0 = null;

function getUint8ArrayMemory0() {
  if (
    cachedUint8ArrayMemory0 === null ||
    cachedUint8ArrayMemory0.byteLength === 0
  ) {
    cachedUint8ArrayMemory0 = new Uint8Array(wasm.memory.buffer);
  }
  return cachedUint8ArrayMemory0;
}

function getStringFromWasm0(ptr, len) {
  ptr = ptr >>> 0;
  return cachedTextDecoder.decode(
    getUint8ArrayMemory0().subarray(ptr, ptr + len),
  );
}

function isLikeNone(x) {
  return x === undefined || x === null;
}

let cachedUint8ClampedArrayMemory0 = null;

function getUint8ClampedArrayMemory0() {
  if (
    cachedUint8ClampedArrayMemory0 === null ||
    cachedUint8ClampedArrayMemory0.byteLength === 0
  ) {
    cachedUint8ClampedArrayMemory0 = new Uint8ClampedArray(wasm.memory.buffer);
  }
  return cachedUint8ClampedArrayMemory0;
}

function getClampedArrayU8FromWasm0(ptr, len) {
  ptr = ptr >>> 0;
  return getUint8ClampedArrayMemory0().subarray(ptr / 1, ptr / 1 + len);
}

function dropObject(idx) {
  if (idx < 132) return;
  heap[idx] = heap_next;
  heap_next = idx;
}

function takeObject(idx) {
  const ret = getObject(idx);
  dropObject(idx);
  return ret;
}

let WASM_VECTOR_LEN = 0;

const cachedTextEncoder =
  typeof TextEncoder !== 'undefined'
    ? new TextEncoder('utf-8')
    : {
        encode: () => {
          throw Error('TextEncoder not available');
        },
      };

const encodeString =
  typeof cachedTextEncoder.encodeInto === 'function'
    ? function (arg, view) {
        return cachedTextEncoder.encodeInto(arg, view);
      }
    : function (arg, view) {
        const buf = cachedTextEncoder.encode(arg);
        view.set(buf);
        return {
          read: arg.length,
          written: buf.length,
        };
      };

function passStringToWasm0(arg, malloc, realloc) {
  if (realloc === undefined) {
    const buf = cachedTextEncoder.encode(arg);
    const ptr = malloc(buf.length, 1) >>> 0;
    getUint8ArrayMemory0()
      .subarray(ptr, ptr + buf.length)
      .set(buf);
    WASM_VECTOR_LEN = buf.length;
    return ptr;
  }

  let len = arg.length;
  let ptr = malloc(len, 1) >>> 0;

  const mem = getUint8ArrayMemory0();

  let offset = 0;

  for (; offset < len; offset++) {
    const code = arg.charCodeAt(offset);
    if (code > 0x7f) break;
    mem[ptr + offset] = code;
  }

  if (offset !== len) {
    if (offset !== 0) {
      arg = arg.slice(offset);
    }
    ptr = realloc(ptr, len, (len = offset + arg.length * 3), 1) >>> 0;
    const view = getUint8ArrayMemory0().subarray(ptr + offset, ptr + len);
    const ret = encodeString(arg, view);

    offset += ret.written;
    ptr = realloc(ptr, len, offset, 1) >>> 0;
  }

  WASM_VECTOR_LEN = offset;
  return ptr;
}

let cachedDataViewMemory0 = null;

function getDataViewMemory0() {
  if (
    cachedDataViewMemory0 === null ||
    cachedDataViewMemory0.buffer.detached === true ||
    (cachedDataViewMemory0.buffer.detached === undefined &&
      cachedDataViewMemory0.buffer !== wasm.memory.buffer)
  ) {
    cachedDataViewMemory0 = new DataView(wasm.memory.buffer);
  }
  return cachedDataViewMemory0;
}

function _assertClass(instance, klass) {
  if (!(instance instanceof klass)) {
    throw new Error(`expected instance of ${klass.name}`);
  }
}

function passArrayJsValueToWasm0(array, malloc) {
  const ptr = malloc(array.length * 4, 4) >>> 0;
  const mem = getDataViewMemory0();
  for (let i = 0; i < array.length; i++) {
    mem.setUint32(ptr + 4 * i, addHeapObject(array[i]), true);
  }
  WASM_VECTOR_LEN = array.length;
  return ptr;
}

function getArrayJsValueFromWasm0(ptr, len) {
  ptr = ptr >>> 0;
  const mem = getDataViewMemory0();
  const result = [];
  for (let i = ptr; i < ptr + 4 * len; i += 4) {
    result.push(takeObject(mem.getUint32(i, true)));
  }
  return result;
}
/**
 * @param {any[]} encrypted_questions
 * @returns {Array<any>}
 */
export function save_questions(encrypted_questions) {
  try {
    const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
    const ptr0 = passArrayJsValueToWasm0(
      encrypted_questions,
      wasm.__wbindgen_export_1,
    );
    const len0 = WASM_VECTOR_LEN;
    wasm.save_questions(retptr, ptr0, len0);
    var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
    var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
    var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);
    if (r2) {
      throw takeObject(r1);
    }
    return takeObject(r0);
  } finally {
    wasm.__wbindgen_add_to_stack_pointer(16);
  }
}

/**
 * @param {string} question_id
 * @param {string} answer
 * @returns {boolean}
 */
export function check_answer(question_id, answer) {
  const ptr0 = passStringToWasm0(
    question_id,
    wasm.__wbindgen_export_1,
    wasm.__wbindgen_export_2,
  );
  const len0 = WASM_VECTOR_LEN;
  const ptr1 = passStringToWasm0(
    answer,
    wasm.__wbindgen_export_1,
    wasm.__wbindgen_export_2,
  );
  const len1 = WASM_VECTOR_LEN;
  const ret = wasm.check_answer(ptr0, len0, ptr1, len1);
  return ret !== 0;
}

/**
 * @param {string} question_id
 * @param {HTMLCanvasElement} canvas
 * @param {CanvasRenderingContext2D} ctx
 * @returns {QuestionResult}
 */
export function draw_question(question_id, canvas, ctx) {
  try {
    const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
    const ptr0 = passStringToWasm0(
      question_id,
      wasm.__wbindgen_export_1,
      wasm.__wbindgen_export_2,
    );
    const len0 = WASM_VECTOR_LEN;
    wasm.draw_question(
      retptr,
      ptr0,
      len0,
      addHeapObject(canvas),
      addHeapObject(ctx),
    );
    var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
    var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
    var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);
    if (r2) {
      throw takeObject(r1);
    }
    return QuestionResult.__wrap(r0);
  } finally {
    wasm.__wbindgen_add_to_stack_pointer(16);
  }
}

const QuestionOutputFinalization =
  typeof FinalizationRegistry === 'undefined'
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry((ptr) =>
        wasm.__wbg_questionoutput_free(ptr >>> 0, 1),
      );

export class QuestionOutput {
  static __wrap(ptr) {
    ptr = ptr >>> 0;
    const obj = Object.create(QuestionOutput.prototype);
    obj.__wbg_ptr = ptr;
    QuestionOutputFinalization.register(obj, obj.__wbg_ptr, obj);
    return obj;
  }

  __destroy_into_raw() {
    const ptr = this.__wbg_ptr;
    this.__wbg_ptr = 0;
    QuestionOutputFinalization.unregister(this);
    return ptr;
  }

  free() {
    const ptr = this.__destroy_into_raw();
    wasm.__wbg_questionoutput_free(ptr, 0);
  }
  /**
   * @param {string} id
   * @param {string} preset_identifier
   * @param {string[]} tags
   * @param {number} answer_length
   */
  constructor(id, preset_identifier, tags, answer_length) {
    const ptr0 = passStringToWasm0(
      id,
      wasm.__wbindgen_export_1,
      wasm.__wbindgen_export_2,
    );
    const len0 = WASM_VECTOR_LEN;
    const ptr1 = passStringToWasm0(
      preset_identifier,
      wasm.__wbindgen_export_1,
      wasm.__wbindgen_export_2,
    );
    const len1 = WASM_VECTOR_LEN;
    const ptr2 = passArrayJsValueToWasm0(tags, wasm.__wbindgen_export_1);
    const len2 = WASM_VECTOR_LEN;
    const ret = wasm.questionoutput_new(
      ptr0,
      len0,
      ptr1,
      len1,
      ptr2,
      len2,
      answer_length,
    );
    this.__wbg_ptr = ret >>> 0;
    QuestionOutputFinalization.register(this, this.__wbg_ptr, this);
    return this;
  }
  /**
   * @returns {string}
   */
  get id() {
    let deferred1_0;
    let deferred1_1;
    try {
      const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
      wasm.questionoutput_id(retptr, this.__wbg_ptr);
      var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
      var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
      deferred1_0 = r0;
      deferred1_1 = r1;
      return getStringFromWasm0(r0, r1);
    } finally {
      wasm.__wbindgen_add_to_stack_pointer(16);
      wasm.__wbindgen_export_3(deferred1_0, deferred1_1, 1);
    }
  }
  /**
   * @returns {string}
   */
  get presetIdentifier() {
    let deferred1_0;
    let deferred1_1;
    try {
      const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
      wasm.questionoutput_presetIdentifier(retptr, this.__wbg_ptr);
      var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
      var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
      deferred1_0 = r0;
      deferred1_1 = r1;
      return getStringFromWasm0(r0, r1);
    } finally {
      wasm.__wbindgen_add_to_stack_pointer(16);
      wasm.__wbindgen_export_3(deferred1_0, deferred1_1, 1);
    }
  }
  /**
   * @returns {string[]}
   */
  get tags() {
    try {
      const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
      wasm.questionoutput_tags(retptr, this.__wbg_ptr);
      var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
      var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
      var v1 = getArrayJsValueFromWasm0(r0, r1).slice();
      wasm.__wbindgen_export_3(r0, r1 * 4, 4);
      return v1;
    } finally {
      wasm.__wbindgen_add_to_stack_pointer(16);
    }
  }
  /**
   * @returns {number}
   */
  get answerLength() {
    const ret = wasm.questionoutput_answerLength(this.__wbg_ptr);
    return ret;
  }
}

const QuestionOutputWrapperFinalization =
  typeof FinalizationRegistry === 'undefined'
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry((ptr) =>
        wasm.__wbg_questionoutputwrapper_free(ptr >>> 0, 1),
      );

export class QuestionOutputWrapper {
  static __wrap(ptr) {
    ptr = ptr >>> 0;
    const obj = Object.create(QuestionOutputWrapper.prototype);
    obj.__wbg_ptr = ptr;
    QuestionOutputWrapperFinalization.register(obj, obj.__wbg_ptr, obj);
    return obj;
  }

  __destroy_into_raw() {
    const ptr = this.__wbg_ptr;
    this.__wbg_ptr = 0;
    QuestionOutputWrapperFinalization.unregister(this);
    return ptr;
  }

  free() {
    const ptr = this.__destroy_into_raw();
    wasm.__wbg_questionoutputwrapper_free(ptr, 0);
  }
  /**
   * @param {QuestionOutput} question
   * @param {SubmissionOutput[] | null} [submissions]
   */
  constructor(question, submissions) {
    _assertClass(question, QuestionOutput);
    var ptr0 = question.__destroy_into_raw();
    var ptr1 = isLikeNone(submissions)
      ? 0
      : passArrayJsValueToWasm0(submissions, wasm.__wbindgen_export_1);
    var len1 = WASM_VECTOR_LEN;
    const ret = wasm.questionoutputwrapper_new(ptr0, ptr1, len1);
    this.__wbg_ptr = ret >>> 0;
    QuestionOutputWrapperFinalization.register(this, this.__wbg_ptr, this);
    return this;
  }
  /**
   * @returns {QuestionOutput}
   */
  get question() {
    const ret = wasm.questionoutputwrapper_question(this.__wbg_ptr);
    return QuestionOutput.__wrap(ret);
  }
  /**
   * @returns {SubmissionOutput[] | undefined}
   */
  get submissions() {
    try {
      const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
      wasm.questionoutputwrapper_submissions(retptr, this.__wbg_ptr);
      var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
      var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
      let v1;
      if (r0 !== 0) {
        v1 = getArrayJsValueFromWasm0(r0, r1).slice();
        wasm.__wbindgen_export_3(r0, r1 * 4, 4);
      }
      return v1;
    } finally {
      wasm.__wbindgen_add_to_stack_pointer(16);
    }
  }
}

const QuestionResultFinalization =
  typeof FinalizationRegistry === 'undefined'
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry((ptr) =>
        wasm.__wbg_questionresult_free(ptr >>> 0, 1),
      );

export class QuestionResult {
  static __wrap(ptr) {
    ptr = ptr >>> 0;
    const obj = Object.create(QuestionResult.prototype);
    obj.__wbg_ptr = ptr;
    QuestionResultFinalization.register(obj, obj.__wbg_ptr, obj);
    return obj;
  }

  __destroy_into_raw() {
    const ptr = this.__wbg_ptr;
    this.__wbg_ptr = 0;
    QuestionResultFinalization.unregister(this);
    return ptr;
  }

  free() {
    const ptr = this.__destroy_into_raw();
    wasm.__wbg_questionresult_free(ptr, 0);
  }
  /**
   * @returns {string}
   */
  get id() {
    let deferred1_0;
    let deferred1_1;
    try {
      const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
      wasm.questionresult_id(retptr, this.__wbg_ptr);
      var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
      var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
      deferred1_0 = r0;
      deferred1_1 = r1;
      return getStringFromWasm0(r0, r1);
    } finally {
      wasm.__wbindgen_add_to_stack_pointer(16);
      wasm.__wbindgen_export_3(deferred1_0, deferred1_1, 1);
    }
  }
  /**
   * @returns {string}
   */
  get question() {
    let deferred1_0;
    let deferred1_1;
    try {
      const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
      wasm.questionresult_question(retptr, this.__wbg_ptr);
      var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
      var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
      deferred1_0 = r0;
      deferred1_1 = r1;
      return getStringFromWasm0(r0, r1);
    } finally {
      wasm.__wbindgen_add_to_stack_pointer(16);
      wasm.__wbindgen_export_3(deferred1_0, deferred1_1, 1);
    }
  }
}

const SubmissionOutputFinalization =
  typeof FinalizationRegistry === 'undefined'
    ? { register: () => {}, unregister: () => {} }
    : new FinalizationRegistry((ptr) =>
        wasm.__wbg_submissionoutput_free(ptr >>> 0, 1),
      );

export class SubmissionOutput {
  static __wrap(ptr) {
    ptr = ptr >>> 0;
    const obj = Object.create(SubmissionOutput.prototype);
    obj.__wbg_ptr = ptr;
    SubmissionOutputFinalization.register(obj, obj.__wbg_ptr, obj);
    return obj;
  }

  static __unwrap(jsValue) {
    if (!(jsValue instanceof SubmissionOutput)) {
      return 0;
    }
    return jsValue.__destroy_into_raw();
  }

  __destroy_into_raw() {
    const ptr = this.__wbg_ptr;
    this.__wbg_ptr = 0;
    SubmissionOutputFinalization.unregister(this);
    return ptr;
  }

  free() {
    const ptr = this.__destroy_into_raw();
    wasm.__wbg_submissionoutput_free(ptr, 0);
  }
  /**
   * @param {string | null} [userId]
   */
  constructor(userId) {
    var ptr0 = isLikeNone(userId)
      ? 0
      : passStringToWasm0(
          userId,
          wasm.__wbindgen_export_1,
          wasm.__wbindgen_export_2,
        );
    var len0 = WASM_VECTOR_LEN;
    const ret = wasm.submissionoutput_new(ptr0, len0);
    this.__wbg_ptr = ret >>> 0;
    SubmissionOutputFinalization.register(this, this.__wbg_ptr, this);
    return this;
  }
  /**
   * @returns {string | undefined}
   */
  get userId() {
    try {
      const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
      wasm.submissionoutput_userId(retptr, this.__wbg_ptr);
      var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);
      var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);
      let v1;
      if (r0 !== 0) {
        v1 = getStringFromWasm0(r0, r1).slice();
        wasm.__wbindgen_export_3(r0, r1 * 1, 1);
      }
      return v1;
    } finally {
      wasm.__wbindgen_add_to_stack_pointer(16);
    }
  }
}

async function __wbg_load(module, imports) {
  if (typeof Response === 'function' && module instanceof Response) {
    if (typeof WebAssembly.instantiateStreaming === 'function') {
      try {
        return await WebAssembly.instantiateStreaming(module, imports);
      } catch (e) {
        if (module.headers.get('Content-Type') != 'application/wasm') {
          console.warn(
            '`WebAssembly.instantiateStreaming` failed because your server does not serve Wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n',
            e,
          );
        } else {
          throw e;
        }
      }
    }

    const bytes = await module.arrayBuffer();
    return await WebAssembly.instantiate(bytes, imports);
  } else {
    const instance = await WebAssembly.instantiate(module, imports);

    if (instance instanceof WebAssembly.Instance) {
      return { instance, module };
    } else {
      return instance;
    }
  }
}

function __wbg_get_imports() {
  const imports = {};
  imports.wbg = {};
  imports.wbg.__wbg_call_672a4d21634d4a24 = function () {
    return handleError(function (arg0, arg1) {
      const ret = getObject(arg0).call(getObject(arg1));
      return addHeapObject(ret);
    }, arguments);
  };
  imports.wbg.__wbg_createElement_8c9931a732ee2fea = function () {
    return handleError(function (arg0, arg1, arg2) {
      const ret = getObject(arg0).createElement(getStringFromWasm0(arg1, arg2));
      return addHeapObject(ret);
    }, arguments);
  };
  imports.wbg.__wbg_document_d249400bd7bd996d = function (arg0) {
    const ret = getObject(arg0).document;
    return isLikeNone(ret) ? 0 : addHeapObject(ret);
  };
  imports.wbg.__wbg_drawImage_d7db50c62926931d = function () {
    return handleError(function (arg0, arg1, arg2, arg3, arg4, arg5) {
      getObject(arg0).drawImage(getObject(arg1), arg2, arg3, arg4, arg5);
    }, arguments);
  };
  imports.wbg.__wbg_getContext_e9cf379449413580 = function () {
    return handleError(function (arg0, arg1, arg2) {
      const ret = getObject(arg0).getContext(getStringFromWasm0(arg1, arg2));
      return isLikeNone(ret) ? 0 : addHeapObject(ret);
    }, arguments);
  };
  imports.wbg.__wbg_height_838cee19ba8597db = function (arg0) {
    const ret = getObject(arg0).height;
    return ret;
  };
  imports.wbg.__wbg_instanceof_CanvasRenderingContext2d_df82a4d3437bf1cc =
    function (arg0) {
      let result;
      try {
        result = getObject(arg0) instanceof CanvasRenderingContext2D;
      } catch (_) {
        result = false;
      }
      const ret = result;
      return ret;
    };
  imports.wbg.__wbg_instanceof_HtmlCanvasElement_2ea67072a7624ac5 = function (
    arg0,
  ) {
    let result;
    try {
      result = getObject(arg0) instanceof HTMLCanvasElement;
    } catch (_) {
      result = false;
    }
    const ret = result;
    return ret;
  };
  imports.wbg.__wbg_instanceof_Window_def73ea0955fc569 = function (arg0) {
    let result;
    try {
      result = getObject(arg0) instanceof Window;
    } catch (_) {
      result = false;
    }
    const ret = result;
    return ret;
  };
  imports.wbg.__wbg_new_78feb108b6472713 = function () {
    const ret = new Array();
    return addHeapObject(ret);
  };
  imports.wbg.__wbg_newnoargs_105ed471475aaf50 = function (arg0, arg1) {
    const ret = new Function(getStringFromWasm0(arg0, arg1));
    return addHeapObject(ret);
  };
  imports.wbg.__wbg_newwithu8clampedarrayandsh_7ea6ee082a25bc85 = function () {
    return handleError(function (arg0, arg1, arg2, arg3) {
      const ret = new ImageData(
        getClampedArrayU8FromWasm0(arg0, arg1),
        arg2 >>> 0,
        arg3 >>> 0,
      );
      return addHeapObject(ret);
    }, arguments);
  };
  imports.wbg.__wbg_push_737cfc8c1432c2c6 = function (arg0, arg1) {
    const ret = getObject(arg0).push(getObject(arg1));
    return ret;
  };
  imports.wbg.__wbg_putImageData_4c5aa10f3b3e4924 = function () {
    return handleError(function (arg0, arg1, arg2, arg3) {
      getObject(arg0).putImageData(getObject(arg1), arg2, arg3);
    }, arguments);
  };
  imports.wbg.__wbg_questionoutputwrapper_new = function (arg0) {
    const ret = QuestionOutputWrapper.__wrap(arg0);
    return addHeapObject(ret);
  };
  imports.wbg.__wbg_setheight_da683a33fa99843c = function (arg0, arg1) {
    getObject(arg0).height = arg1 >>> 0;
  };
  imports.wbg.__wbg_setwidth_c5fed9f5e7f0b406 = function (arg0, arg1) {
    getObject(arg0).width = arg1 >>> 0;
  };
  imports.wbg.__wbg_static_accessor_GLOBAL_88a902d13a557d07 = function () {
    const ret = typeof global === 'undefined' ? null : global;
    return isLikeNone(ret) ? 0 : addHeapObject(ret);
  };
  imports.wbg.__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0 = function () {
    const ret = typeof globalThis === 'undefined' ? null : globalThis;
    return isLikeNone(ret) ? 0 : addHeapObject(ret);
  };
  imports.wbg.__wbg_static_accessor_SELF_37c5d418e4bf5819 = function () {
    const ret = typeof self === 'undefined' ? null : self;
    return isLikeNone(ret) ? 0 : addHeapObject(ret);
  };
  imports.wbg.__wbg_static_accessor_WINDOW_5de37043a91a9c40 = function () {
    const ret = typeof window === 'undefined' ? null : window;
    return isLikeNone(ret) ? 0 : addHeapObject(ret);
  };
  imports.wbg.__wbg_submissionoutput_new = function (arg0) {
    const ret = SubmissionOutput.__wrap(arg0);
    return addHeapObject(ret);
  };
  imports.wbg.__wbg_submissionoutput_unwrap = function (arg0) {
    const ret = SubmissionOutput.__unwrap(takeObject(arg0));
    return ret;
  };
  imports.wbg.__wbg_width_5dde457d606ba683 = function (arg0) {
    const ret = getObject(arg0).width;
    return ret;
  };
  imports.wbg.__wbindgen_is_undefined = function (arg0) {
    const ret = getObject(arg0) === undefined;
    return ret;
  };
  imports.wbg.__wbindgen_object_clone_ref = function (arg0) {
    const ret = getObject(arg0);
    return addHeapObject(ret);
  };
  imports.wbg.__wbindgen_object_drop_ref = function (arg0) {
    takeObject(arg0);
  };
  imports.wbg.__wbindgen_string_get = function (arg0, arg1) {
    const obj = getObject(arg1);
    const ret = typeof obj === 'string' ? obj : undefined;
    var ptr1 = isLikeNone(ret)
      ? 0
      : passStringToWasm0(
          ret,
          wasm.__wbindgen_export_1,
          wasm.__wbindgen_export_2,
        );
    var len1 = WASM_VECTOR_LEN;
    getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);
    getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);
  };
  imports.wbg.__wbindgen_string_new = function (arg0, arg1) {
    const ret = getStringFromWasm0(arg0, arg1);
    return addHeapObject(ret);
  };
  imports.wbg.__wbindgen_throw = function (arg0, arg1) {
    throw new Error(getStringFromWasm0(arg0, arg1));
  };

  return imports;
}

function __wbg_init_memory(imports, memory) {}

function __wbg_finalize_init(instance, module) {
  wasm = instance.exports;
  __wbg_init.__wbindgen_wasm_module = module;
  cachedDataViewMemory0 = null;
  cachedUint8ArrayMemory0 = null;
  cachedUint8ClampedArrayMemory0 = null;

  return wasm;
}

function initSync(module) {
  if (wasm !== undefined) return wasm;

  if (typeof module !== 'undefined') {
    if (Object.getPrototypeOf(module) === Object.prototype) {
      ({ module } = module);
    } else {
      console.warn(
        'using deprecated parameters for `initSync()`; pass a single object instead',
      );
    }
  }

  const imports = __wbg_get_imports();

  __wbg_init_memory(imports);

  if (!(module instanceof WebAssembly.Module)) {
    module = new WebAssembly.Module(module);
  }

  const instance = new WebAssembly.Instance(module, imports);

  return __wbg_finalize_init(instance, module);
}

async function __wbg_init(module_or_path) {
  if (wasm !== undefined) return wasm;

  if (typeof module_or_path !== 'undefined') {
    if (Object.getPrototypeOf(module_or_path) === Object.prototype) {
      ({ module_or_path } = module_or_path);
    } else {
      console.warn(
        'using deprecated parameters for the initialization function; pass a single object instead',
      );
    }
  }

  if (typeof module_or_path === 'undefined') {
    module_or_path = '/matiks.wasm';
  }
  const imports = __wbg_get_imports();

  if (
    typeof module_or_path === 'string' ||
    (typeof Request === 'function' && module_or_path instanceof Request) ||
    (typeof URL === 'function' && module_or_path instanceof URL)
  ) {
    module_or_path = fetch(module_or_path);
  }

  __wbg_init_memory(imports);

  const { instance, module } = await __wbg_load(await module_or_path, imports);

  return __wbg_finalize_init(instance, module);
}

export { initSync };
export default __wbg_init;
