import React, { useCallback } from 'react';
import useGoBack from 'navigator/hooks/useGoBack';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import { BackButtonProps } from './types';

const defaultProps = {
  height: 36,
  width: 36,
} as const;

const BackButton = ({
  onBackPress,
  height = defaultProps.height,
  width = defaultProps.width,
}: BackButtonProps) => {
  const { goBack } = useGoBack();

  const handleBackPress = useCallback(() => {
    if (onBackPress) {
      onBackPress();
    } else {
      goBack();
    }
  }, [onBackPress, goBack]);

  return (
    <InteractiveSecondaryButton
      testID="backButtonTest"
      onPress={handleBackPress}
      iconConfig={{
        name: 'arrow-back',
        type: ICON_TYPES.IONICON,
        color: dark.colors.textDark,
        size: 20,
      }}
      buttonContainerStyle={{ width, height }}
      buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
    />
  );
};

export default React.memo(BackButton);
