import React from 'react';
import { View, StyleSheet} from 'react-native';
import Svg, { Defs, LinearGradient, Stop, Rect } from 'react-native-svg';

interface VerticalGradientCardProps {
  children: React.ReactNode;
  gradientColor: string;
  borderColor?: string;
  shadowColor?: string;
  shadowWidth?: number;
  shadowOffsetX?: number;
  shadowOffsetY?: number;
}

const VerticalGradientCard = ({
  children,
  gradientColor,
  borderColor = '#000',
  shadowColor = 'black',
  shadowWidth = 0,
  shadowOffsetX = 0,
  shadowOffsetY = 0,
}: VerticalGradientCardProps) => {
  return (
    <View style={styles.mainContainer}>
      {shadowWidth > 0 && (
        <View
          style={[
            {
              backgroundColor: shadowColor,
              right: shadowOffsetX,
              bottom: shadowOffsetY,
              width: `${shadowWidth + 100}%`,
              height: `${shadowWidth + 100}%`,
            },
          ]}
        />
      )}
      <View style={[styles.container, { borderColor }]}>
        <Svg height="100%" width="100%" style={StyleSheet.absoluteFill}>
          <Defs>
            <LinearGradient id="verticalGrad" x1="0%" y1="0%" x2="0%" y2="100%">
              <Stop offset="0" stopColor={gradientColor} stopOpacity="0.15" />
              <Stop offset="0.3" stopColor={gradientColor} stopOpacity="0.08" />
              <Stop offset="0.5" stopColor={gradientColor} stopOpacity="0.05" />
              <Stop offset="0.7" stopColor={gradientColor} stopOpacity="0.02" />
              <Stop offset="1" stopColor={gradientColor} stopOpacity="0.0" />
            </LinearGradient>
          </Defs>
          <Rect x="0" y="0" width="100%" height="100%" fill="url(#verticalGrad)" />
        </Svg>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  container: {
    width: '100%',
    position: 'relative',
  },
});

export default React.memo(VerticalGradientCard);
