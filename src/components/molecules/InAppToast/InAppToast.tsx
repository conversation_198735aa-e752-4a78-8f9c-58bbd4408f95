import React, { useEffect, useMemo, useRef } from 'react';
import { View, Text, TouchableOpacity, Animated, Image } from 'react-native';
import styles from './InAppToast.style';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { usePathname } from 'expo-router';
import useUserStore from '@/src/store/useUserStore';

const pathNameRegex = /^\/home/<USER>

const InAppToast = () => {
  const { showToastValues, showInAppToast } = useUserStore((state) => ({
    showToastValues: state.showToastValues,
    showInAppToast: state.showInAppToast,
  }));
  const translateY = useRef(new Animated.Value(-100)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const pathName = usePathname();

  const { isMobile } = useMediaQuery();

  useEffect(() => {
    if (!showToastValues.visible) return;
    const timer = setTimeout(() => {
      showInAppToast({
        title: null,
        description: null,
        imageUrl: null,
        onAccept: null,
        onReject: null,
        onClose: null,
        visible: false,
        timeout: 15000,
      });
    }, 15000);
    return () => clearTimeout(timer);
  }, [showToastValues.visible]);

  const isMatch = useMemo(() => pathNameRegex.test(pathName), [pathName]);

  useEffect(() => {
    if (showToastValues.visible) {
      Animated.parallel([
        Animated.spring(translateY, {
          toValue: 0,
          tension: 70,
          friction: 10,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: -100,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start(() => showToastValues.onClose?.());
    }

    return () => {
      translateY.stopAnimation();
      fadeAnim.stopAnimation();
    };
  }, [showToastValues.visible]);

  if (!showToastValues.visible || !isMatch) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY }],
          top: isMobile ? 20 : 50,
          opacity: fadeAnim,
          alignSelf: isMobile ? 'center' : 'flex-end',
        },
      ]}
    >
      <View style={styles.gradientBackground}>
        <View style={styles.contentContainer}>
          {showToastValues.imageUrl ? (
            <Image
              source={{ uri: showToastValues.imageUrl }}
              style={styles.avatar}
            />
          ) : null}

          <View style={styles.textContainer}>
            <Text style={styles.title}>{showToastValues.title}</Text>
            <Text style={styles.message}>{showToastValues.description}</Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          {showToastValues.onReject ? (
            <TouchableOpacity
              style={styles.rejectButton}
              onPress={showToastValues.onReject}
              activeOpacity={0.7}
            >
              <Text style={styles.rejectText}>✕ Reject</Text>
            </TouchableOpacity>
          ) : null}

          {showToastValues.onAccept ? (
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={showToastValues.onAccept}
              activeOpacity={0.7}
            >
              <Text style={styles.acceptText}>✓ Accept</Text>
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
    </Animated.View>
  );
};

// const InAppToastContainer = React.memo(() => {
//   const { isMobile } = useMediaQuery();
//   return (
//     <View
//       style={{
//         width: '100%',
//         height: '100%',
//         position: 'absolute',
//         zIndex: 10,
//         alignItems: isMobile ? 'center' : 'flex-end',
//         paddingHorizontal: isMobile ? 16 : 32,
//       }}
//     >
//       <InAppToast />
//     </View>
//   );
// });

export default React.memo(InAppToast);
