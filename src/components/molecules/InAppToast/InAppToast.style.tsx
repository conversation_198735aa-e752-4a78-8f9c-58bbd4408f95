import dark from "@/src/core/constants/themes/dark";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 40,
    alignSelf: 'center',
    // width: 'auto',
    flex: 1,
    height: 'auto',
    width: "100%",
    maxWidth: 380,
    borderRadius: 16,
    overflow: 'hidden',
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
  },
  gradientBackground: {
    padding: 12,
    borderRadius: 16,
    backgroundColor: dark.colors.tertiary,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 6,
    marginRight: 12,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textDark,
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 4,
  },
  rejectButton: {
    paddingHorizontal: 8,
    borderRadius: 12,
    marginRight: 8,
  },
  rejectText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    textAlign: 'center',
    color:dark.colors.defeatColor,
  },
  acceptButton: {
    paddingHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },

  acceptText: {
    fontSize: 14,
    lineHeight: 20,
    color: dark.colors.secondary,
    textAlign: 'center',
    fontFamily: 'Montserrat-600',
  },
});

export default styles;
