import React, { useEffect, useRef } from 'react';
import {
  View, 
  Text, 
  TouchableOpacity, 
  Animated, 
  Image,
} from 'react-native';
import LinearGradient from 'atoms/LinearGradient';
import styles from './InAppToast2.style';

const InAppToast = ({ 
  visible = true, 
  userName = "Sudhanshu", 
  userAvatar = "https://randomuser.me/api/portraits/men/32.jpg",
  onAccept = () => {}, 
  onReject = () => {},
  onClose = () => {}
}) => {
  const translateY = useRef(new Animated.Value(-100)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(translateY, {
          toValue: 0,
          tension: 70,
          friction: 10,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: -100,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        })
      ]).start(() => onClose());
    }
  }, [visible]);

//   if (!visible) {
//     return null;
//   }

  return (
    <Animated.View 
      style={[
        styles.container, 
        { 
          transform: [{ translateY }],
          opacity: fadeAnim
        }
      ]}
    >
      <LinearGradient
        colors={['rgba(37, 38, 43, 0.9)', 'rgba(24, 24, 28, 0.95)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBackground}
      >
        <View style={styles.contentContainer}>
          <Image 
            source={{ uri: userAvatar }} 
            style={styles.avatar} 
          />
          
          <View style={styles.textContainer}>
            <Text style={styles.userName}>{userName}</Text>
            <Text style={styles.message}>has requested you for a rematch.</Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={styles.rejectButton} 
            onPress={onReject}
            activeOpacity={0.7}
          >
            <Text style={styles.rejectText}>✕ Reject</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.acceptButton} 
            onPress={onAccept}
            activeOpacity={0.7}
          >
            <LinearGradient
              colors={['#4ade80', '#22c55e']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.acceptGradient}
            >
              <Text style={styles.acceptText}>✓ Accept</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animated.View>
  );
}

export default React.memo(InAppToast);