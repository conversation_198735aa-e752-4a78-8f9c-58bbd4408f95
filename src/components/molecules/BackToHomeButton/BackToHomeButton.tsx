import React, { useCallback } from 'react';
import useGoBack from 'navigator/hooks/useGoBack';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import { BackToHomeButtonProps } from 'molecules/BackToHomeButton/types';

const defaultProps = {
  height: 36,
  width: 36,
  iconConfig: EMPTY_OBJECT,
} as const;

const BackToHomeButton = ({
  onBackPress,
  height = defaultProps.height,
  width = defaultProps.width,
  iconConfig = defaultProps.iconConfig,
}: BackToHomeButtonProps) => {
  const { goBackToHome } = useGoBack();

  const handleBackPress = useCallback(() => {
    if (onBackPress) {
      onBackPress();
    } else {
      goBackToHome();
    }
  }, [onBackPress, goBackToHome]);

  return (
    <InteractiveSecondaryButton
      testID="backToHomeButtonTest"
      onPress={handleBackPress}
      iconConfig={{
        name: 'home',
        type: ICON_TYPES.ENTYPO,
        color: dark.colors.secondaryButtonBorder,
        size: 20,
        ...iconConfig,
      }}
      buttonContainerStyle={{ width, height }}
      buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
    />
  );
};

export default React.memo(BackToHomeButton);
