import { ReactNode } from "react";
import { ViewStyle } from "react-native";
import { YStackProps, SheetProps } from "tamagui";

export interface BottomSheetProps {
  isOpen?: boolean;
  content?: React.ReactNode;
  containerPadding?: number;
  renderFrameComponent?: React.ReactNode;
  renderCloseButton?: () => React.ReactNode;
  snapPoints?: number[];
  styles?: any;
}

export interface BottomSheetState {
  isOpen?: boolean;
  content: ReactNode | ((props: { closeBottomSheet: () => void }) => ReactNode) | null;
  renderFrameComponent: React.ComponentType<{ children: ReactNode }> | null;
  styles: Partial<{
    frame?: object;
    contentContainer?: YStackProps;
    closeButton?: ViewStyle;
  }>;
  containerPadding?: number;
  renderCloseButton?: (() => ReactNode) | null;
  snapPoints?: number[] | null;
  sheetPosition?: number;
  animation: SheetProps['animation'];
  dismissOnOverlayPress?: boolean; 
}

export interface OpenBottomSheetProps extends Partial<BottomSheetState> {
  dismissOnOverlayPress?: boolean;
}