import React from 'react';
import { View } from 'react-native';
import Svg, { Defs, RadialGradient, Rect, Stop } from 'react-native-svg';
import styles from './GradientCard.style';
import { GradientCardProps } from './types';

const GradientCard = ({
  children,
  gradientColor,
  borderColor,
  shadowColor = 'black',
  shadowWidth = 0,
  shadowOffsetX = 0,
  shadowOffsetY = 0,
}: GradientCardProps) => (
  <View style={styles.mainContainer} testID="gradientCardContainer">
    {shadowWidth > 0 && (
      <View
        testID="gradientCardShadow"
        style={[
          styles.shadowCard,
          {
            backgroundColor: shadowColor,
            right: shadowOffsetX,
            bottom: shadowOffsetY,
            width: `${shadowWidth + 97}%`,
            height: `${shadowWidth + 78}%`,
          },
        ]}
      />
    )}
    <View style={[styles.container, { borderColor }]}>
      <View style={styles.topGradientContainer}>
        <Svg height={200} width={200}>
          <Defs>
            <RadialGradient
              id="topGrad"
              cx="50%"
              cy="50%"
              r="50%"
              gradientUnits="userSpaceOnUse"
            >
              <Stop offset="0" stopColor={gradientColor} stopOpacity="0.2" />
              <Stop offset="0.3" stopColor={gradientColor} stopOpacity="0.1" />
              <Stop offset="0.5" stopColor={gradientColor} stopOpacity="0.05" />
              <Stop offset="0.6" stopColor={gradientColor} stopOpacity="0.03" />
              <Stop offset="0.7" stopColor={gradientColor} stopOpacity="0.01" />
              <Stop offset="0.8" stopColor={gradientColor} stopOpacity="0.0" />
            </RadialGradient>
          </Defs>
          <Rect x="0" y="0" width="100%" height="100%" fill="url(#topGrad)" />
        </Svg>
      </View>
      <View style={styles.bottomGradientContainer}>
        <Svg height={350} width={350}>
          <Defs>
            <RadialGradient
              id="bottomGrad"
              cx="50%"
              cy="50%"
              r="50%"
              gradientUnits="userSpaceOnUse"
            >
              <Stop offset="0" stopColor={gradientColor} stopOpacity="0.1" />
              <Stop offset="0.3" stopColor={gradientColor} stopOpacity="0.07" />
              <Stop offset="0.5" stopColor={gradientColor} stopOpacity="0.05" />
              <Stop offset="0.7" stopColor={gradientColor} stopOpacity="0.01" />
              <Stop offset="0.8" stopColor={gradientColor} stopOpacity="0.00" />
            </RadialGradient>
          </Defs>
          <Rect
            x="0"
            y="0"
            width="100%"
            height="100%"
            fill="url(#bottomGrad)"
          />
        </Svg>
      </View>
      {children}
    </View>
  </View>
);

export default React.memo(GradientCard);
