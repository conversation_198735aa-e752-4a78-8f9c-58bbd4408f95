import React, { useCallback, useState } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';

import { QuestionType } from 'shared/MCQWordProblemQuestion/types/types';
import MCQWordProblemQuestionText from 'shared/MCQWordProblemQuestion/components/MCQWordProblemQuestionText';
import MCQWordProblemOptions from 'shared/MCQWordProblemQuestion/components/MCQWordProblemOptions';
import MCQWordProblemActions from 'shared/MCQWordProblemQuestion/components/MCQWordProblemActions';

export interface GenericQuestionType {
  id?: string;
  expression: string[];
  description?: string;
  options: string[];
  answers: string[];
  questionType?: QuestionType;
  variables?: Record<string, string | number>;
}

export interface MCQWordProblemQuestionProps {
  question: GenericQuestionType;
  onCorrect: (selectedAnswers: string[]) => void;
  onWrong: (selectedAnswers: string[]) => void;
  children: React.ReactNode;
}

const MCQWordProblemQuestionRoot: React.FC<MCQWordProblemQuestionProps> = ({
  question,
  onCorrect,
  onWrong,
  children,
}) => <View>{children}</View>;

export const MCQWordProblemQuestion = Object.assign(
  MCQWordProblemQuestionRoot,
  {
    QuestionText: MCQWordProblemQuestionText,
    Options: MCQWordProblemOptions,
    Actions: MCQWordProblemActions,
  },
);

const MCQWordProblemRenderer: React.FC<{
  question: GenericQuestionType;
  style?: StyleProp<ViewStyle>;
}> = ({ question, style }) => {
  const [selectedOptionIndex, setSelectedOptionIndex] = useState<number | null>(
    null,
  );
  const [showResult, setShowResult] = useState<boolean>(false);

  const handleCorrect = useCallback((selectedAnswers: string[]) => {
    // console.info('RITESH : selectedAnswers', selectedAnswers);
  }, []);

  const handleWrong = useCallback((selectedAnswers: string[]) => {
    // console.log('RITESH : selectedAnswers', selectedAnswers);
  }, []);

  const handleSelectOption = useCallback(
    (index: number) => {
      setSelectedOptionIndex(index);
      setShowResult(true);

      const selectedAnswerText = question.options[index];

      if (
        question.answers &&
        question.answers.length > 0 &&
        selectedAnswerText === question.answers[0]
      ) {
        handleCorrect([selectedAnswerText]);
      } else {
        handleWrong([selectedAnswerText]);
      }
    },
    [question.options, question.answers, handleCorrect, handleWrong],
  );

  return (
    <View style={style}>
      <MCQWordProblemQuestion
        question={question}
        onCorrect={handleCorrect}
        onWrong={handleWrong}
      >
        <MCQWordProblemQuestion.QuestionText
          expression={question.expression}
          description={question.description}
          variables={question.variables}
        />
        <MCQWordProblemQuestion.Options
          options={question.options}
          answers={question.answers}
          questionType={question.questionType}
          selectedOptionIndex={selectedOptionIndex}
          onSelectOption={handleSelectOption}
          showResult={showResult}
        />
      </MCQWordProblemQuestion>
    </View>
  );
};

export default React.memo(MCQWordProblemRenderer);
