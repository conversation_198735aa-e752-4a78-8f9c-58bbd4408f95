import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: dark.colors.gradientBackground,
    paddingHorizontal: 16,
    marginTop: 46,
    flex: 1,
    marginBottom: 120,
    paddingVertical: 20,
  },
  container: {
    width: '70%',
    gap: 20,
  },
  header: {
    position: 'relative',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Montserrat-800',
    color: dark.colors.textLight,
  },
  infoText: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    lineHeight: 16,
    color: withOpacity(dark.colors.textLight, 0.4),
  },
  bottomButtonLabel: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: 'white',
    alignSelf: 'center',
    opacity: 0.9,
  },
  bottomButtonStyle: {
    flex: 1,
    backgroundColor: dark.colors.card,
    paddingHorizontal: 8,
    minWidth: 150,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: dark.colors.secondary,
    borderWidth: 0.5,
    maxWidth: 160,
    borderRadius: 8,
  },
  bottomButtonBackgroundStyle: {
    flex: 1,
    minWidth: 140,
    backgroundColor: dark.colors.secondary,
    opacity: 0.2,
    borderRadius: 10,
  },
  riveAnimation: {
    position: 'absolute',
    right: -5,
    bottom: 25,
  },
});

export default styles;
