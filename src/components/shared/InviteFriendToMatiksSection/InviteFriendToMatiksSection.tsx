import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import TextWithShadow from 'shared/TextWithShadow';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import userReader from 'core/readers/userReader';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useInviteFriendOnMatiks from 'core/hooks/useInviteFriendOnMatiks';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import styles from './InviteFriendToMatiksSection.style';

const InviteFriendToMatiksSection = () => {
  const { user } = useSession();
  const isGuest = userReader.isGuest(user);

  const { handleNormalShare, shareThroughWhatsapp } = useInviteFriendOnMatiks();

  const handleShare = useCallback(() => {
    handleNormalShare({
      eventToBeTracked:
        ANALYTICS_EVENTS.PROFILE.REFER_FRIEND_HOME_SCREEN_COMPONENT_SHARE_OTHER,
    });
  }, [handleNormalShare]);

  const inviteFriendThroughWA = useCallback(() => {
    shareThroughWhatsapp({
      eventToBeTracked:
        ANALYTICS_EVENTS.PROFILE.REFER_FRIEND_HOME_SCREEN_COMPONENT_SHARE_WA,
    });
  }, [shareThroughWhatsapp]);

  if (isGuest) {
    return <View style={{ height: 120 }} />;
  }

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={{ gap: 8 }}>
          <TextWithShadow
            text="SHARE THE CHALLENGE"
            textStyle={styles.headerTitle}
            containerStyle={styles.header}
            shadowColor={dark.colors.tertiary}
            shadowWidth={12}
            shadowOffsetX={-1}
            shadowOffsetY={-3}
          />
          <Text style={styles.infoText}>
            Matiks just feels better with your friends
          </Text>
        </View>
        <View style={{ flexDirection: 'row', gap: 12, alignItems: 'center' }}>
          <InteractiveSecondaryButton
            label="INVITE FRIEND"
            iconConfig={{
              type: ICON_TYPES.FONT_AWESOME,
              name: 'whatsapp',
              size: 16,
              color: dark.colors.secondary,
            }}
            onPress={inviteFriendThroughWA}
            labelStyle={styles.bottomButtonLabel}
            buttonStyle={styles.bottomButtonStyle}
            borderColor={dark.colors.secondary}
          />
          <InteractiveSecondaryButton
            onPress={handleShare}
            iconConfig={{
              type: ICON_TYPES.ENTYPO,
              name: 'share',
              size: 16,
              color: dark.colors.textLight,
            }}
            buttonContainerStyle={{ width: 48, borderRadius: 20 }}
            buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
          />
        </View>
      </View>

      <View style={styles.riveAnimation}>
        <Rive
          url={RIVE_ANIMATIONS.INVITE_FRIEND_ANIMATION}
          autoPlay
          style={{ width: 110, height: 200 }}
        />
      </View>
    </View>
  );
};

export default React.memo(InviteFriendToMatiksSection);
