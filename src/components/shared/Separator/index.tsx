import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import _values from 'lodash/values';
import dark from '../../../core/constants/themes/dark';
import { SeparatorProps } from './types';

export const SEPARATOR_DIRECTION = {
  HORIZONTAL: 'HORIZONTAL',
  VERTICAL: 'VERTICAL',
};

const Separator: React.FC<SeparatorProps> = ({
  direction = SEPARATOR_DIRECTION.HORIZONTAL,
  thickness = 0.5,
  color = dark.colors.textDark,
}) => {
  const separatorStyle: StyleProp<ViewStyle> =
    direction === SEPARATOR_DIRECTION.HORIZONTAL
      ? { height: thickness, backgroundColor: color, width: '100%' }
      : { width: thickness, backgroundColor: color, height: '100%' };

  return <View style={separatorStyle} />;
};

export default React.memo(Separator);
