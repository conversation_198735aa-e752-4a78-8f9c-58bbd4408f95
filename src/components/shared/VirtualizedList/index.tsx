/* eslint-disable react/function-component-definition */

// VirtualizedList.tsx
import { Platform } from 'react-native';
import React from 'react';
import VirtualizedListNative from './VirtualizedList.native';
import VirtualizedListWeb from './VirtualizedList.web';
import { VirtualizedListProps } from './types';

function VirtualizedList<T>(props: VirtualizedListProps<T>): React.ReactNode {
  const List = Platform.select({
    web: VirtualizedListWeb,
    android: VirtualizedListNative,
    ios: VirtualizedListNative,
  });

  if (!List) return null;

  return <List {...props} />;
}

export default React.memo(VirtualizedList) as typeof VirtualizedList;