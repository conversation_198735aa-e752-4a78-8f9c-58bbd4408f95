import { REMATCH_REQUEST_STATUS } from '@/src/overlays/constants/rematchRequests';

export interface User {
  id: string | number;

  [key: string]: any;
}

interface RematchPayload {
  status: (typeof REMATCH_REQUEST_STATUS)[keyof typeof REMATCH_REQUEST_STATUS];
  requestedBy: string | number;
  opponentUser?: User | {};
  waitingTime: number;
  gameId?: string;
}

export interface RematchRequestOverlayProps {
  payload: RematchPayload | null;
  showRematchRequestOverlay: boolean;
  showCancelOverlay: boolean;
  onCancelRematch: (gameId: string) => void;
  setShowCancelOverlay: Function;
}
