import React, { useCallback } from 'react';
import _toString from 'lodash/toString';
import RequestCard from 'modules/game/pages/Rematch/RequestCard/RequestCard';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { REMATCH_REQUEST_STATUS } from '../../../overlays/constants/rematchRequests';
import WithNotificationBannerAnimationWeb from '../WithNotificationBannerAnimation';
import { User } from './types';
import CancelRematchOverlay from '../CancelRematchOverlay';
import useIsPlaying from '@/src/overlays/hooks/useIsPlaying';
import useUserRematchEvents from '@/src/overlays/hooks/useUserRematchEvents';
import useOverlayEventHandler from '@/src/overlays/hooks/useOverlayEventHandler';
import { events, listenersNamespace } from '@/src/core/event/constants';

const RematchRequestOverlay = () => {
  const { userId: currentUserId } = useSession();

  const isPlayingGame = useIsPlaying();
  const {
    showRematchRequestOverlay,
    handleRematchRequestEvents,
    showCancelOverlay,
    setShowCancelOverlay,
    handleCancelRematch: onCancelRematch,
  } = useUserRematchEvents();

  const cancelOverlayOnTimerEnd = useCallback(() => {
    setShowCancelOverlay?.(false);
  }, [setShowCancelOverlay]);

  const payload = useOverlayEventHandler(
    events.RematchRequestedEvent,
    listenersNamespace.RematchRequestedEvent,
    handleRematchRequestEvents,
  );

  if (!payload) {
    return null;
  }

  const {
    status,
    requestedBy,
    opponentUser = {},
    waitingTime,
    gameId,
  } = payload;

  if (showCancelOverlay) {
    return (
      <CancelRematchOverlay
        opponentUser={opponentUser as User}
        gameId={gameId}
        onCancel={onCancelRematch}
        waitingTime={waitingTime}
        onTimerEnd={cancelOverlayOnTimerEnd}
      />
    );
  }

  const isReceiver = _toString(currentUserId) !== _toString(requestedBy);

  if (
    showRematchRequestOverlay &&
    status === REMATCH_REQUEST_STATUS.REMATCH_REQUESTED &&
    isReceiver &&
    !isPlayingGame
  ) {
    return (
      <WithNotificationBannerAnimationWeb animationDuration={waitingTime}>
        <RequestCard user={opponentUser as User} payload={payload} />
      </WithNotificationBannerAnimationWeb>
    );
  }

  return null;
};

export default React.memo(RematchRequestOverlay);
