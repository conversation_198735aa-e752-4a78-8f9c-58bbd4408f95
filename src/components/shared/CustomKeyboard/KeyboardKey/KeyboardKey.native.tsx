import React, { useCallback, useMemo, useState } from 'react';
import { Platform, Text, View, ViewProps } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { runOnJS } from 'react-native-reanimated';
import styles from './KeyboardKey.style';
import { KeyboardKeyProps } from './types';
import useHaptics from '@/src/core/hooks/useHaptics';

const KeyboardKey: React.FC<KeyboardKeyProps> = (props) => {
  const { onPress, label, renderLabel: renderLabelFromProps, keyWidth } = props;
  const [isPressed, setIsPressed] = useState(false);
  const { triggerHaptic } = useHaptics();

  const onTouchStart = useCallback(() => {
    triggerHaptic();
    setIsPressed(true);
    onPress?.();
  }, [onPress, triggerHaptic]);

  const onTouchEnd = useCallback(() => {
    setIsPressed(false);
  }, []);

  const renderLabel = useCallback(() => {
    if (renderLabelFromProps) {
      return renderLabelFromProps();
    }
    return <Text style={styles.keyText}>{label}</Text>;
  }, [label, renderLabelFromProps]);

  const tapGesture = useMemo(
    () =>
      Gesture.Tap()
        .maxDuration(500)
        .onBegin(() => {
          'worklet';

          runOnJS(onTouchStart)();
        })
        .onEnd(() => {
          'worklet';

          runOnJS(setIsPressed)(false);
        })
        .onTouchesCancelled(() => {
          'worklet';

          runOnJS(setIsPressed)(false);
        }),
    [onTouchStart, setIsPressed],
  );

  const containerStyle = useMemo(
    () => [
      styles.keyContainer,
      isPressed && styles.keyContainerPressed,
      { width: keyWidth },
    ],
    [isPressed, keyWidth],
  );

  const innerViewStyle = useMemo(
    () => [
      styles.key,
      { flex: 1, alignItems: 'center', justifyContent: 'center' },
    ],
    [],
  );

  const renderContent = useCallback(
    (contentProps?: ViewProps) => (
      <View {...contentProps} style={containerStyle}>
        <View style={innerViewStyle}>{renderLabel()}</View>
      </View>
    ),
    [containerStyle, innerViewStyle, renderLabel],
  );

  return useMemo(() => {
    if (Platform.OS === 'android') {
      return (
        <GestureDetector gesture={tapGesture}>
          {renderContent()}
        </GestureDetector>
      );
    }
    return renderContent({ onTouchStart, onTouchEnd });
  }, [tapGesture, renderContent, onTouchStart, onTouchEnd]);
};

export default React.memo(KeyboardKey);
