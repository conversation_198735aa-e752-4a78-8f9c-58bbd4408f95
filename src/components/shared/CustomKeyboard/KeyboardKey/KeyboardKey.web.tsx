import React, { useCallback, useMemo, useState } from 'react';
import { Text } from 'react-native';
import styles from './KeyboardKey.style';
import './CustomKeyboard.css';
import { KeyboardKeyProps } from './types';

const KeyboardKey: React.FC<KeyboardKeyProps> = (props) => {
  const { onPress, label, renderLabel: renderLabelFromProps, keyWidth } = props;
  const [isPressed, setIsPressed] = useState(false);

  const handlePressStart = useCallback(() => {
    setIsPressed(true);
    onPress?.();
  }, [onPress]);

  const handlePressEnd = useCallback(() => {
    setIsPressed(false);
  }, []);

  const renderLabel = useCallback(() => {
    if (renderLabelFromProps) {
      return renderLabelFromProps();
    }
    return <Text style={styles.keyText}>{label}</Text>;
  }, [label, renderLabelFromProps]);

  const containerStyle = useMemo(
    () => ({
      touchAction: 'manipulation',
      WebkitUserSelect: 'none',
      userSelect: 'none',
      WebkitTouchCallout: 'none',
      cursor: 'default',
      WebkitTapHighlightColor: 'transparent',
      width: keyWidth,
    }),
    [keyWidth],
  );

  const innerDivStyle = useMemo(
    () => ({
      WebkitUserSelect: 'none',
      userSelect: 'none',
      WebkitTouchCallout: 'none',
    }),
    [],
  );

  const containerClassName = useMemo(
    () => `key-container ${isPressed ? 'key-container-pressed' : ''}`,
    [isPressed],
  );

  const renderedLabel = useMemo(() => renderLabel(), [renderLabel]);

  return useMemo(
    () => (
      <div
        className={containerClassName}
        onTouchStart={handlePressStart}
        onTouchEnd={handlePressEnd}
        role="button"
        style={containerStyle}
      >
        <div className="key" style={innerDivStyle}>
          {renderedLabel}
        </div>
      </div>
    ),
    [
      containerClassName,
      handlePressStart,
      handlePressEnd,
      containerStyle,
      innerDivStyle,
      renderedLabel,
    ],
  );
};

export default React.memo(KeyboardKey);
