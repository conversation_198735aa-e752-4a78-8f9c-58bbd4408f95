import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import _map from 'lodash/map';
import { KEYBOARD_TYPES } from './constants';
import styles from './CustomKeyboard.style';
import KeyboardKey from './KeyboardKey';
import { CustomKeyboardProps } from './types';

const ABILITY_KEYS = [
  '1',
  '2',
  '3',
  '-',
  '4',
  '5',
  '6',
  '/',
  '7',
  '8',
  '9',
  'clr',
  '.',
  '0',
  'space',
];
const KEYS = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '0'];

const CustomKeyboard: React.FC<CustomKeyboardProps> = (props) => {
  const {
    onKeyPress,
    onDelete,
    customKeyboardType = KEYBOARD_TYPES.NUMBERS,
  } = props;

  const { keys, keyWidth } = useMemo(() => {
    const keysArray =
      customKeyboardType === KEYBOARD_TYPES.NUMBERS_AND_OPERATORS
        ? ABILITY_KEYS
        : KEYS;
    const width =
      customKeyboardType === KEYBOARD_TYPES.NUMBERS_AND_OPERATORS
        ? '24%'
        : '32%';
    return { keys: keysArray, keyWidth: width };
  }, [customKeyboardType]);

  const renderDeleteIcon = useCallback(
    () => <FontAwesome6 name="delete-left" size={20} color="white" />,
    [],
  );

  const getKeyPressHandler = useCallback(
    (key: string) => () => onKeyPress(key),
    [onKeyPress],
  );

  return (
    <View style={styles.container}>
      <View style={styles.keyRow}>
        {_map(keys, (key) => (
          <KeyboardKey
            key={key}
            onPress={getKeyPressHandler(key)}
            label={key}
            keyWidth={keyWidth}
          />
        ))}
        <KeyboardKey
          key="delete"
          onPress={onDelete}
          renderLabel={renderDeleteIcon}
          keyWidth={keyWidth}
        />
      </View>
    </View>
  );
};

export default React.memo(CustomKeyboard);
