import React from 'react';
import { Text, View } from 'react-native';
import styles from './TextWithShadow.style';

const TextWithShadow = ({
  text = '',
  shadowColor = 'black',
  shadowWidth = 2,
  shadowOffsetX = 2,
  shadowOffsetY = 2,
  textStyle = {},
  containerStyle = {},
  adjustsFontSizeToFit = false,
  numberOfLines = 1,
  strokeColor = 'white',
  strokeWidth = 2,
  preventStrokeClipping = true,
  ...otherProps
}) => { 
  const adjustedStrokeWidth = Math.max(strokeWidth - 2, 0);
  const strokeTextStyle = adjustedStrokeWidth > 0 ? {
    textShadow: `
      -${adjustedStrokeWidth}px -${adjustedStrokeWidth}px 0 ${strokeColor},
      ${adjustedStrokeWidth}px -${adjustedStrokeWidth}px 0 ${strokeColor},
      -${adjustedStrokeWidth}px ${adjustedStrokeWidth}px 0 ${strokeColor},
      ${adjustedStrokeWidth}px ${adjustedStrokeWidth}px 0 ${strokeColor},
      -${adjustedStrokeWidth}px 0 0 ${strokeColor},
      ${adjustedStrokeWidth}px 0 0 ${strokeColor},
      0 -${adjustedStrokeWidth}px 0 ${strokeColor},
      0 ${adjustedStrokeWidth}px 0 ${strokeColor}
    `,
  } : {};

  const containerPadding = adjustedStrokeWidth > 0 && preventStrokeClipping ? 
    { padding: adjustedStrokeWidth } : {};

  return (
    <View style={[styles.container, containerStyle, containerPadding]}>
      {shadowWidth > 0 && (
        <Text
          style={[
            styles.shadowText,
            {
              right: shadowOffsetX,
              bottom: shadowOffsetY,
              ...textStyle,
              color: shadowColor,
            },
          ]}
          adjustsFontSizeToFit={adjustsFontSizeToFit}
          numberOfLines={numberOfLines}
          {...otherProps}
        >
          {text}
        </Text>
      )}
      <Text
        style={[
          styles.mainText, 
          textStyle, 
          strokeTextStyle,
          adjustedStrokeWidth > 0 && preventStrokeClipping ? {
            paddingHorizontal: adjustedStrokeWidth,
            marginHorizontal: adjustedStrokeWidth
          } : {}
        ]}
        adjustsFontSizeToFit={adjustsFontSizeToFit}
        numberOfLines={numberOfLines}
        {...otherProps}
      >
        {text}
      </Text>
    </View>
  );
};

export default React.memo(TextWithShadow);