import React from 'react';
import { Dimensions, View } from 'react-native';
import { useCrossMathPuzzleQuestion } from '../../context';
import styles from './CrossMathPuzzleGrid.style';
import CrossMathPuzzleGridCell from './CrossMathPuzzleGridCell';

const CrossMathPuzzleGrid = () => {
  const { state } = useCrossMathPuzzleQuestion();
  const { gridItems, dimension } = state;

  const puzzleWidth = Math.min(300, Dimensions.get('window').width - 32);

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.gridContainer,
          {
            width: puzzleWidth,
            height: 300,
          },
        ]}
      >
        <View>
          {Array.from({ length: dimension.rows }, (_, i) => i).map((row) => (
            <View key={row} style={styles.row}>
              {Array.from({ length: dimension.columns }, (_, i) => i).map(
                (col) => {
                  const index = row * dimension.columns + col;
                  return (
                    <CrossMathPuzzleGridCell
                      testId={`grid-item-${index}`}
                      key={index}
                      index={index}
                      item={gridItems[index]}
                    />
                  );
                },
              )}
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

export default React.memo(CrossMathPuzzleGrid);
