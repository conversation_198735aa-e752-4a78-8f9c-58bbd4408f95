import { View } from 'react-native';
import React, { useCallback } from 'react';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';
import _size from 'lodash/size';
import NumberToSvg from 'atoms/NumberToSvg';
import { cellType } from '../../types/crossMathCellType';
import { CROSS_MATH_PUZZLE_ACTIONS } from '../../constants/puzzleConstants';
import { useCrossMathPuzzleQuestion } from '../../context';
import styles from './CrossMathPuzzleGrid.style';
import Haptics from '@/src/core/container/Haptics';
import Pressable from '@/src/components/atoms/Pressable';

interface GridCellProps {
  index: number;
  item: cellType;
  testId?: string;
}

const getBackgroundColor = (item: cellType, isIncorrectSolution: boolean) => {
  if (item?.type === 'EmptyBlock') {
    return dark.colors.background;
  }
  if (item?.editable) {
    if (isIncorrectSolution) {
      return dark.colors.puzzle.error;
    }
    if (item?.value !== '') {
      return dark.colors.puzzle.secondary;
    }
    return withOpacity(dark.colors.puzzle.primary, 0.4);
  }
  return dark.colors.puzzle.primary;
};

const CrossMathPuzzleGridCell = ({ index, item , testId}: GridCellProps) => {
  const { state, onAction } = useCrossMathPuzzleQuestion();

  const { selectedGridCell, isIncorrectSolution } = state;

  const isSelected = selectedGridCell === index;

  const onPressGridCell = useCallback(() => {
    if (!item?.editable) {
      onAction({
        type: CROSS_MATH_PUZZLE_ACTIONS.TAP_GRID_ITEM,
        payload: null,
      });
      return;
    }
    onAction({
      type: CROSS_MATH_PUZZLE_ACTIONS.TAP_GRID_ITEM,
      payload: index,
    });
  }, [onAction, index, item?.editable]);

  const backgroundColorsOfCell = getBackgroundColor(item, isIncorrectSolution);

  const fontSize = _size(item?.value) > 2 ? 12 : 16;

  return (
    <Pressable
      testID={testId}
      onPress={onPressGridCell}
      style={({ pressed }) => [
        styles.cellContainer,
        {
          backgroundColor: backgroundColorsOfCell,
          opacity: pressed ? 0.7 : 1,
        },
      ]}
      impactFeedbackStyle={Haptics.ImpactFeedbackStyle.Soft}
    >
      {item && (
        <View
          style={[styles.borderView, isSelected && styles.selectedBorderView]}
        >
          <View
            style={{
              flex: 1,
              borderRadius: 3,
              backgroundColor: dark.colors.background,
            }}
          >
            <View
              style={{
                flex: 1,
                borderRadius: 3,
                backgroundColor: backgroundColorsOfCell,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <NumberToSvg
                number={item?.value}
                fontSize={fontSize}
                color="black"
              />
            </View>
          </View>
        </View>
      )}
    </Pressable>
  );
};

export default React.memo(CrossMathPuzzleGridCell);
