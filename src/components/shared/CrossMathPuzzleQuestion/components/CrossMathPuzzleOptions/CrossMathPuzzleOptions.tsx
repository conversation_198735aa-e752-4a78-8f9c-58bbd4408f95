import React from 'react';
import { View } from 'react-native';
import { footerType } from 'shared/CrossMathPuzzleQuestion/types/crossMathCellType';
import _map from 'lodash/map'; // Update import to match new name
import { useCrossMathPuzzleQuestion } from 'shared/CrossMathPuzzleQuestion/context';
import styles from './CrossMathPuzzleOptions.style';
import CrossMathPuzzleOption from './CrossMathPuzzleOption';

const CrossMathPuzzleOptions = () => {
  const { state } = useCrossMathPuzzleQuestion();

  const { footerItems } = state;

  const firstRow = footerItems.slice(0, 5);
  const secondRow = footerItems.slice(5, 10);

  const renderFooterItem = (item: footerType, index: number, rowKey: string) => (
    <CrossMathPuzzleOption testId={`footer-item-${rowKey}-${index}-${item.value}`} key={index} item={item} />
  );

  return (
    <View style={styles.container}>
      <View style={styles.optionsRow}>{_map(firstRow, (item, idx) => renderFooterItem(item, idx, 'firstRow'))}</View>
      <View style={styles.optionsRow}>{_map(secondRow, (item, idx) => renderFooterItem(item, idx, 'secondRow'))}</View>
    </View>
  );
};

export default React.memo(CrossMathPuzzleOptions);
