import React, { useCallback } from 'react';
import { View } from 'react-native';
import { footerType } from 'shared/CrossMathPuzzleQuestion/types/crossMathCellType';
import { useCrossMathPuzzleQuestion } from 'shared/CrossMathPuzzleQuestion/context';
import { CROSS_MATH_PUZZLE_ACTIONS } from 'shared/CrossMathPuzzleQuestion/constants/puzzleConstants';
import _size from 'lodash/size';
import NumberToSvg from 'atoms/NumberToSvg';
import styles from './CrossMathPuzzleOptions.style';
import Pressable from '@/src/components/atoms/Pressable';

interface FooterItemProps {
  item: footerType;
  testId?: string;
}

const CrossMathPuzzleOption = ({ item, testId }: FooterItemProps) => {
  const { state, onAction } = useCrossMathPuzzleQuestion();
  const { selectedFooterCell } = state;

  const isSelected = item?.id === selectedFooterCell?.id;

  const onPressFooterCell = useCallback(() => {
    if (isSelected) {
      onAction({
        type: CROSS_MATH_PUZZLE_ACTIONS.TAP_FOOTER_ITEM,
        payload: null,
      });
      return;
    }
    onAction({
      type: CROSS_MATH_PUZZLE_ACTIONS.TAP_FOOTER_ITEM,
      payload: item,
    });
  }, [item, onAction, isSelected]);

  if (item.isFilled) {
    return <View style={[styles.optionContainer, styles.filledContainer]} />;
  }

  const fontSize = _size(item?.value) > 2 ? 12 : 16;

  return (
    <Pressable
      testID={testId}
      onPress={onPressFooterCell}
      style={({ pressed }) => [
        styles.optionContainer,
        {
          opacity: pressed ? 0.7 : 1,
        },
        isSelected && styles.selectedContainer,
      ]}
    >
      <View>
        <NumberToSvg number={item.value} fontSize={fontSize} color="black" />
      </View>
    </Pressable>
  );
};

export default React.memo(CrossMathPuzzleOption);
