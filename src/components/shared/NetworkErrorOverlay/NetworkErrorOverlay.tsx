import React, { useEffect, useRef, useState } from 'react';
import { Animated, Text, View } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import styles from './NetworkErrorOverlay.style';
import useWebsocketStore from '@/src/store/useWebSocketStore';
import { NetworkErrorOverlayProps } from './types';

const NetworkErrorOverlay = ({ showLabel = true }) => {
  const opacity = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animate = () => {
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.9,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0.3,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => animate());
    };

    animate();

    return () => opacity.stopAnimation();
  }, []);

  return (
    <View style={styles.networkErrorOverlayContainer}>
      {showLabel && (
        <Text style={styles.lowConnectivityText}>Low Connectivity</Text>
      )}
      <Animated.View
        style={[
          styles.networkErrorOverlay,
          {
            opacity,
          },
        ]}
      >
        <MaterialCommunityIcons
          name="network-strength-1-alert"
          size={20}
          color="red"
        />
      </Animated.View>
    </View>
  );
};

const NetworkErrorOverlayContainer: React.FC<NetworkErrorOverlayProps> = (
  props,
) => {
  const { isConnected } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
  }));

  if (isConnected) {
    return null;
  }

  return <NetworkErrorOverlay {...props} />;
};

export const NetworkErrorOverlayPlaceholder = () => (
  <View
    style={[
      styles.networkErrorOverlayContainer,
      { backgroundColor: 'transparent', borderColor: 'transparent' },
    ]}
  />
);

export default React.memo(NetworkErrorOverlayContainer);
