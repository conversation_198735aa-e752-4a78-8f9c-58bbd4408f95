import { StyleSheet } from "react-native";
import dark from "../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    card: {
        borderColor: dark.colors.tertiary,
        borderWidth: 2,
        // paddingHorizontal: 20,
        borderRadius: 16,
        // paddingVertical: 16,
        maxWidth: '100%',
        overflow:"hidden",
        flexDirection: 'row',
        alignItems: 'center',
    },
    cardIcon: {
        fontSize: 32,
        marginRight: 20,
        color: 'white',
    },
    cardText: {
        fontFamily: 'Montserrat-500',
        flex: 1,
        paddingLeft:16
    },
    cardTitle: {
        color: dark.colors.textDark,
        fontSize: 11,
        fontFamily: 'Montserrat-600',
    },
    cardDescription: {
        lineHeight: 18,
        color: 'white',
        fontSize: 13,
        fontFamily: 'Montserrat-500',
        marginTop: 5,
    },
    infoImage: {
        height: 80,
        width: 80,
        resizeMode: "cover"
    }

})

export default styles