import { useCallback, useEffect } from 'react';
import useUnreadAnnouncements from 'shared/Announcement/hooks/useUnreadAnnouncements';
import announcementReader from 'core/readers/announcementReader';
import _uniq from 'lodash/uniq';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import useMarkAnnouncementAsRead from './useMarkAnnouncementAsRead';

interface UseAnnouncementProps {
  limit?: number;
  offset?: number;
  autoMarkAsRead?: boolean;
}

let announcementReadStatus: string[] = [];

const useAnnouncement = ({
  limit = 1,
  offset = 0,
  autoMarkAsRead = true,
}: UseAnnouncementProps = {}) => {
  const {
    announcements,
    loading: loadingAnnouncements,
    error: queryError,
  } = useUnreadAnnouncements({ limit, offset });

  // Mutation for marking announcements as read
  const { markAnnouncementAsRead } = useMarkAnnouncementAsRead();

  // Handle marking announcement as read
  const handleMarkAsRead = useCallback(
    async (announcementId: string) => {
      if (_includes(announcementReadStatus, announcementId)) {
        return false;
      }
      try {
        announcementReadStatus = _uniq([
          ...announcementReadStatus,
          announcementId,
        ]);
        const success = await markAnnouncementAsRead(announcementId);
        if (!success) {
          announcementReadStatus = _filter(
            announcementReadStatus,
            (id) => id !== announcementId,
          );
        }
        return success;
      } catch (error) {
        announcementReadStatus = _filter(
          announcementReadStatus,
          (id) => id !== announcementId,
        );
        console.error('Error marking announcement as read:', error);
        return false;
      }
    },
    [markAnnouncementAsRead],
  );

  // Auto mark as read when announcements are viewed
  useEffect(() => {
    if (autoMarkAsRead && announcements.length > 0 && !loadingAnnouncements) {
      announcements.forEach((announcement) => {
        handleMarkAsRead(announcement.id);
      });
    }
  }, [autoMarkAsRead, announcements, handleMarkAsRead, loadingAnnouncements]);

  const unreadAnnouncements = _filter(
    announcements,
    (announcement) =>
      !_includes(announcementReadStatus, announcementReader.id(announcement)),
  );

  return {
    announcements: unreadAnnouncements,
    loading: loadingAnnouncements,
    error: queryError,
    markAsRead: handleMarkAsRead,
  };
};

export default useAnnouncement;
