import { StyleSheet } from 'react-native';
import dark from '../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  paginationContainer: {
    backgroundColor: dark.colors.background,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    bottom: 0,
    paddingVertical: 10,
  },
  pageNumbersContainer: {
    flexDirection: 'row',
    maxWidth: '75%',
    // overflow: 'scroll',
  },
  pageButton: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    marginHorizontal: 4,
    borderRadius: 4,
  },
  selectedPage: {
    backgroundColor: dark.colors.primary,
  },
  pageText: {
    color: '#fff',
  },
  selectedPageText: {
    color: dark.colors.secondary,
  },
  noDataContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  addMoreFriendsText: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    color: 'white',
  },
  addFriendsButton: {
    marginTop: 20,
    width: 140,
    height: 32,
    borderWidth: 1,
    borderColor: dark.colors.primary,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 4,
  },
  addFriendsButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
  },
  emptyStateText: {
    color: dark.colors.textLight,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    textAlign: 'center',
  },
  centeredContainerStyle: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  refreshIndicator: {
    color: dark.colors.secondary,
  },
  refreshContainer: {
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default styles;
