import { ViewStyle } from 'react-native';

export interface PagesComponentProps {
  setPage: any;
  totalPages: number;
  page: number;
}

interface FetchDataParams {
  pageNumber: number;
}

interface FetchDataResponse<T> {
  data: T[];
  totalItems: number;
  itemsList?: any[];
}

interface RenderItemParams<T> {
  item: T;
  index: number;
  onRemove: () => void;
}

interface RenderHeaderParams<T> {
  page: number;
  data: T[];
  itemsList?: any[];
}

interface UpdateCacheParams {
  removedItemIds: string[];
  pageNumber: number;
}

export interface PaginatedListProps<T extends { _id?: string }> {
  fetchData: (params: FetchDataParams) => Promise<FetchDataResponse<T>>;
  renderItem: (params: RenderItemParams<T>) => React.ReactNode;
  hidePaginaton?: boolean;
  renderHeader?: (params: RenderHeaderParams<T>) => React.ReactNode;
  pageSize?: number;
  keyExtractor: (item: T, index?: number) => string;
  contentContainerStyle?: ViewStyle;
  placeholderComponent?: () => React.ReactNode;
  listFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
  makeHeaderScrollable?: boolean;
  emptyListComponent?: React.ComponentType<any> | React.ReactElement | null;
  updateCacheFunction?: (params: UpdateCacheParams) => Promise<void>;
  dataKey?: string;
  pullToRefreshEnabled?: boolean;
  showNoDataStateForAllPages?: boolean;
}
