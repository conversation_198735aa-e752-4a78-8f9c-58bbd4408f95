import { StyleProp, ViewStyle } from 'react-native';
import { IconType } from 'atoms/Icon';

export interface TagProps {
  title: string;
  iconName: string;
  iconType: IconType;
}

export interface GameTypeCardProps {
  title: string;
  subtitle?: string;
  tags?: TagProps[];
  gradientColor: string;
  onPress: () => void;
  buttonStyle?: StyleProp<ViewStyle>;
  buttonBorderBackgroundStyle?: StyleProp<ViewStyle>;
  buttonContentStyles?: StyleProp<ViewStyle>;
  isLocked?: boolean;
}
