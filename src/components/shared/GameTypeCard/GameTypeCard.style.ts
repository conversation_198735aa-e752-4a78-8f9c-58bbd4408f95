import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  lockOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
    zIndex: 10,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  lockIconContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gameTypeContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    minWidth: 300,
  },
  contentContainer: {
    width: '80%',
  },
  buttonContainer: {
    width: '20%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 14,
    fontFamily: 'Montserrat-800',
    color: dark.colors.textLight,
  },
  subtitle: {
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textLight,
    opacity: 0.4,
    marginTop: 2,
  },
  tagsContainer: {
    flex: 1,
    flexDirection: 'row',
    marginTop: 15,
  },
  container: {
    paddingHorizontal: 8,
    backgroundColor: dark.colors.primary,
    marginRight: 4,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 4,
    paddingVertical: 2,
  },
  text: {
    color: 'white',
    opacity: 0.4,
    fontFamily: 'Montserrat-500',
    fontSize: 10,
  },

  buttonStyle: {
    backgroundColor: dark.colors.primary,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255, 0.2);',
    height: 36,
    width: 36,
    padding: 0,
    borderRadius: 4,
  },
  buttonContainerStyle: {
    backgroundColor: dark.colors.tertiary,
    width: 36,
    borderRadius: 4,
  },
  buttonContentStyles: {
    paddingVertical: 0,
  },
});

export default styles;
