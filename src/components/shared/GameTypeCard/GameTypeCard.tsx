import React, { useCallback } from 'react';
import { Platform, TouchableOpacity, View } from 'react-native';
import { Text } from '@rneui/themed';
import _map from 'lodash/map';
import GradientCard from '@/src/components/molecules/GradientCard';
import Icon, { ICON_TYPES } from '@/src/components/atoms/Icon';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import dark from '@/src/core/constants/themes/dark';
import styles from './GameTypeCard.style';
import { GameTypeCardProps, TagProps } from './types';
import useHaptics from '@/src/core/hooks/useHaptics';

const Tags: React.FC<TagProps> = ({ title, iconName, iconType }) => (
  <View style={styles.container}>
    <Icon name={iconName} type={iconType} color="white" size={8} />
    <Text style={styles.text}>{title}</Text>
  </View>
);

const GameTypeCard: React.FC<GameTypeCardProps> = ({
  title,
  subtitle,
  tags,
  gradientColor,
  onPress,
  buttonStyle,
  buttonBorderBackgroundStyle,
  buttonContentStyles,
  isLocked = false,
}) => {
  const isWeb = Platform.OS === 'web';
  const { triggerHaptic } = useHaptics();

  const renderTags = useCallback(
    () => (
      <View style={styles.tagsContainer}>
        {_map(tags, (tag, index) => (
          <Tags
            key={index}
            title={tag.title!}
            iconName={tag.iconName!}
            iconType={tag.iconType!}
          />
        ))}
      </View>
    ),
    [tags],
  );

  const onPressGameCard = useCallback(() => {
    if (!isLocked) {
      onPress?.();
      triggerHaptic();
    }
  }, [onPress, isLocked, triggerHaptic]);

  return (
    <GradientCard
      gradientColor={gradientColor}
      borderColor={dark.colors.primary}
    >
      <TouchableOpacity
        onPress={onPressGameCard}
        style={{ position: 'relative' }}
      >
        {isLocked && <View style={styles.lockOverlay} />}
        <View
          style={[
            styles.gameTypeContainer,
            isWeb && { gap: 14 },
            isLocked && { opacity: 0.5 },
          ]}
        >
          <View style={styles.contentContainer}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.subtitle}>{subtitle}</Text>
            {renderTags()}
          </View>
          <View style={styles.buttonContainer}>
            <InteractivePrimaryButton
              iconConfig={{
                type: ICON_TYPES.FONT_AWESOME_5,
                name: isLocked ? 'lock' : 'play',
                size: 14,
                color: isLocked ? dark.colors.searchPlaceholder : gradientColor,
              }}
              buttonStyle={[
                styles.buttonStyle,
                buttonStyle,
                isWeb && { borderColor: dark.colors.tertiary },
              ]}
              radius={4}
              buttonBorderBackgroundStyle={[
                styles.buttonContainerStyle,
                buttonBorderBackgroundStyle,
              ]}
              buttonContentStyles={[
                styles.buttonContentStyles,
                buttonContentStyles,
              ]}
              onPress={onPressGameCard}
            />
          </View>
        </View>
      </TouchableOpacity>
    </GradientCard>
  );
};

export default React.memo(GameTypeCard);
