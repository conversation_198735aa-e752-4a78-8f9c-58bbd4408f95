import React, { useState, useEffect } from 'react';
import { View, TextInput } from 'react-native';
import { Text } from '@rneui/themed';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import styles from './ModalComponents.style';

const ModalText = [
  {
    title: 'ARITHMETIC QUESTIONS',
    description:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Atque, temporibus assumenda velit hic perferendis deleniti nam!',
  },
  {
    title: 'ENTER YOUR ANSWER',
    description:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Atque, temporibus assumenda velit hic perferendis deleniti nam!',
  },
  {
    title: 'CORRECT ANSWER',
    description:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Atque, temporibus assumenda velit hic perferendis deleniti nam!',
  },
];

const renderRows = (rows: number[]) => {
  return rows.map((number, index) => (
    <View key={index} style={styles.row}>
      <Text key={index} style={styles.number}>
        {number}
      </Text>
    </View>
  ));
};

const NumberColumn = () => {
  return <View style={styles.container}>{renderRows([24, 80, -35])}</View>;
};

const InputBox = () => {
  const [text, setText] = useState('');
  const [phase, setPhase] = useState(0);
  const initialDelay = 7000;

  useEffect(() => {
    let timeout: NodeJS.Timeout | null = null;
    if (phase === 0) {
      timeout = setTimeout(() => {
        setPhase(1);
      }, initialDelay);
    } else if (phase === 1) {
      if (text.length < 2) {
        timeout = setTimeout(() => {
          setText((prev) => prev + '68'[text.length]);
        }, 1500);
      } else {
        timeout = setTimeout(() => {
          setPhase(2);
        }, 1000);
      }
    } else if (phase === 2) {
      if (text === '68') {
        timeout = setTimeout(() => {
          setText('6');
        }, 1500);
      } else if (text === '6') {
        timeout = setTimeout(() => {
          setText('69');
        }, 1500);
      }
    }
    return () => clearTimeout(timeout!);
  }, [text, phase]);

  return (
    <View style={styles.container}>
      <TextInput style={styles.inputStyle} value={text} />
      {text === '69' && (
        <MaterialIcons
          name="check-circle"
          size={18}
          color="green"
          style={styles.iconStyle}
        />
      )}
      {text === '68' && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>x</Text>
        </View>
      )}
    </View>
  );
};

export { NumberColumn, ModalText, InputBox };
