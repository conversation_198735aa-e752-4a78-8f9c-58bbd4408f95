import React from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';
import Dark from '@/src/core/constants/themes/dark';
import useGoBack from 'navigator/hooks/useGoBack';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _size from 'lodash/size';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import styles from './WebBackButton.style';
import { WebBackButtonProps } from './types';

const WebBackButton: React.FC<WebBackButtonProps> = ({
  title,
  containerStyle,
  isTransparentBg = false,
  goBack: goBackFromProps,
  renderTrailingComponent,
}) => {
  const { isMobile } = useMediaQuery();
  const { goBack } = useGoBack();

  if (isMobile) {
    return null;
  }

  return (
    <View
      style={[
        styles.header,
        {
          backgroundColor: isTransparentBg
            ? 'transparent'
            : Dark.colors.background,
        },
        containerStyle,
      ]}
    >
      <View style={styles.headerLeft}>
        <InteractiveSecondaryButton
          testID="web-back-button"
          onPress={goBackFromProps ?? goBack}
          iconConfig={{
            name: 'chevron-left',
            type: ICON_TYPES.FONT_AWESOME_5,
            color: dark.colors.secondaryButtonBorder,
            size: 20,
          }}
          buttonContainerStyle={{ width: 36, height: 36 }}
          buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
        />
        {_size(title) > 0 && <Text style={styles.headerTitle}>{title}</Text>}
      </View>
      {renderTrailingComponent?.()}
    </View>
  );
};

export default React.memo(WebBackButton);
