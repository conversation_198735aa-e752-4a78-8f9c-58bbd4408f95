import { Dimensions, StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      width: Dimensions.get('window').width - 32,
      marginHorizontal: 16,
      backgroundColor: dark.colors.gradientBackground,
      paddingVertical: 13,
      paddingHorizontal: 16,
      borderRadius: 12,
      gap: 8,
      justifyContent: 'space-between',
      // alignItems: "flex-end",
      borderWidth: 1,
      borderColor: dark.colors.tertiary,
    },
    daysCountText: {
      fontSize: 20,
      lineHeight: 22,
      fontFamily: 'Montserrat-800',
      color: 'white',
    },
    daysText: {
      fontFamily: 'Montserrat-600',
      fontSize: 12,
      lineHeight: 14,
      textAlign: 'center',
      color: 'white',
      paddingBottom: 4,
    },

    // CARD
    streakDaysContainer: {
      width: '100%',
      maxWidth: 320,
      gap: 10,
      justifyContent: 'space-around',
      flexDirection: 'row',
    },
    dayText: {
      fontFamily: 'Montserrat-700',
      fontSize: 10,
      lineHeight: 12,
      color: dark.colors.textDark,
    },
    streakDay: {
      gap: 4,
      justifyContent: 'center',
      alignItems: 'center',
    },
    streakUnactiveBox: {
      height: 20,
      width: 20,
      borderColor: dark.colors.tertiary,
      borderWidth: 2,
      borderRadius: 4,
    },
    streakActiveBox: {
      borderColor: dark.colors.secondary,
      backgroundColor: dark.colors.secondary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    streakIcon: {
      height: 20,
      width: 20,
    },
  });

const useStreakMaintainedOverlayStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useStreakMaintainedOverlayStyles;
