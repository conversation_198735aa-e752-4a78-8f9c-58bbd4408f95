import React, { useCallback, useEffect, useRef } from 'react';
import { Animated, Image, Text, TouchableOpacity, View } from 'react-native';
import AntDesign from '@expo/vector-icons/AntDesign';
import Entypo from '@expo/vector-icons/Entypo';
import _map from 'lodash/map';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import StreakIcon from '@/assets/images/icons/Streak.png';
import useHandleStreakMaintainedEvent from '@/src/overlays/hooks/useStreakMaintainedEvent';
import useOverlayEventHandler from '@/src/overlays/hooks/useOverlayEventHandler';
import { events, listenersNamespace } from '@/src/core/event/constants';
import useIsPlaying from '@/src/overlays/hooks/useIsPlaying';
import useStreakAnalytics from 'modules/profile/hooks/query/useStreakAnalytics';
import { ANIMATION_DIRECTION } from '../WithNotificationBannerAnimation/animationConfig';
import useStreakMaintainedOverlayStyles from './StreakMaintainedOverlay.style';
import WithNotificationBannerAnimationWeb from '../WithNotificationBannerAnimation';

const StreakMaintainedOverlay = () => {
  const styles = useStreakMaintainedOverlayStyles();
  const streakMaintainedTimeoutRef = useRef<any>();
  const isPlayingGame = useIsPlaying();
  const {
    handleCloseOverlay: handleCloseStreakMaintainedOverlay,
    handleStreakMaintainedEvent,
    showStreakMaintainedOverlay,
  } = useHandleStreakMaintainedEvent();

  const payload = useOverlayEventHandler(
    events.StreakMaintainedEvent,
    listenersNamespace.StreakMaintainedEvent,
    handleStreakMaintainedEvent,
  );

  const handleCloseOverlay = useCallback(() => {
    handleCloseStreakMaintainedOverlay(payload);
  }, [handleCloseStreakMaintainedOverlay, payload]);

  const waitingTime = 5;
  const { isMobile: isCompactMode } = useMediaQuery();
  const currentDayIndex = new Date(getCurrentTime()).getDay();
  const { weekDays, streakStatus, currentStreakCount } = useStreakAnalytics();
  const scaleAnimation = useRef(new Animated.Value(0)).current;

  const streakCountRef = useRef(currentStreakCount);
  streakCountRef.current = currentStreakCount;

  useEffect(() => {
    if (streakStatus[currentDayIndex]) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        friction: 4,
        useNativeDriver: true,
      }).start();
    }
    return () => {
      scaleAnimation.stopAnimation();
    };
  }, [streakStatus, currentDayIndex]);

  useEffect(() => {
    if (showStreakMaintainedOverlay && !isPlayingGame) {
      if (streakMaintainedTimeoutRef.current) {
        clearTimeout(streakMaintainedTimeoutRef.current);
      }
      streakMaintainedTimeoutRef.current = setTimeout(() => {
        handleCloseOverlay();
      }, 5 * 1000);
    }
    return () => {
      if (streakMaintainedTimeoutRef.current) {
        clearTimeout(streakMaintainedTimeoutRef.current);
      }
    };
  }, [showStreakMaintainedOverlay, isPlayingGame]);

  if (!showStreakMaintainedOverlay || isPlayingGame) {
    return null;
  }

  return (
    <WithNotificationBannerAnimationWeb
      animationDuration={waitingTime}
      animationDirection={ANIMATION_DIRECTION.TTB}
      bannerStyle={{ top: 20, right: 0, width: '100%' }}
    >
      <View style={[styles.container, !isCompactMode && { maxWidth: 350 }]}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Image source={StreakIcon} style={styles.streakIcon} />
            <View
              style={{
                flexDirection: 'row',
                gap: 2,
                alignItems: 'flex-end',
                paddingHorizontal: 4,
              }}
            >
              <Text style={styles.daysCountText}>{currentStreakCount}</Text>
              <Text style={styles.daysText}>
                {currentStreakCount > 1 ? 'days' : 'day'} streak!
              </Text>
            </View>
          </View>
          <TouchableOpacity onPress={handleCloseOverlay}>
            <Entypo
              name="cross"
              size={18}
              color={dark.colors.textDark}
              onPress={handleCloseOverlay}
            />
          </TouchableOpacity>
        </View>
        <View style={styles.streakDaysContainer}>
          {_map(weekDays, (day, index) => (
            <View key={index} style={styles.streakDay}>
              {!streakStatus[index] ? (
                <View style={styles.streakUnactiveBox} />
              ) : index === currentDayIndex ? (
                <Animated.View
                  style={[
                    styles.streakUnactiveBox,
                    styles.streakActiveBox,
                    { transform: [{ scale: scaleAnimation }] },
                  ]}
                >
                  <AntDesign
                    name="check"
                    size={10}
                    color="black"
                    style={{ fontWeight: '800' }}
                  />
                </Animated.View>
              ) : (
                <View
                  style={[styles.streakUnactiveBox, styles.streakActiveBox]}
                >
                  <AntDesign
                    name="check"
                    size={10}
                    color="black"
                    style={{ fontWeight: '800' }}
                  />
                </View>
              )}
              <Text
                style={[
                  styles.dayText,
                  currentDayIndex === index && { color: dark.colors.secondary },
                ]}
              >
                {day}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </WithNotificationBannerAnimationWeb>
  );
};

export default React.memo(StreakMaintainedOverlay);
