import React, { useCallback } from 'react';
import { Image, TouchableOpacity, View } from 'react-native';
// import MatiksLogo from '@/src/components/svg/MatiksLogo'
import matiksLogo from 'assets/images/LinearGradientIcons/matiksBolt.png';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import UserStatikCoinsCard from 'modules/profile/components/UserStatikCoinsCard';
import StreakCard from 'modules/profile/components/StreakCard';
import useMediaQuery from 'core/hooks/useMediaQuery';
import FeedIcon from 'shared/Navbar/components/FeedIcon/FeedIcon';
import useGoBack from '../../../navigator/hooks/useGoBack';
import UserProfileInfo from './components/UserProfileInfo';
import GlobalStats from './components/GlobalStats';
import GoogleLoginForGuest from './components/GoogleLoginForGuest';
import styles from './Navbar.Style';
import useFeedStore from '@/src/store/useFeedStore';

const NavBar = () => {
  const { goBackToHome } = useGoBack();

  const { isRead } = useFeedStore((state) => ({
    isRead: state.isRead,
  }));

  const navigateToHome = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CLICKED_ON_MATIKS_LOGO_IN_NAV_HEADER);
    goBackToHome();
  }, [goBackToHome]);

  const { isTablet } = useMediaQuery();

  return (
    <View style={styles.navbarContainer}>
      <View style={styles.navbar}>
        <TouchableOpacity onPress={navigateToHome}>
          <Image source={matiksLogo} style={styles.logo} />
        </TouchableOpacity>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: 20,
            justifyContent: 'space-between',
            flex: 1,
            marginLeft: 118,
          }}
        >
          {!isTablet && <GlobalStats />}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-end',
              gap: 20,
              flex: 1,
            }}
          >
            <GoogleLoginForGuest />
            <FeedIcon shouldShowBadge={!isRead} />
            <StreakCard />
            <UserStatikCoinsCard />
            <UserProfileInfo />
          </View>
        </View>
      </View>
    </View>
  );
};

export default React.memo(NavBar);
