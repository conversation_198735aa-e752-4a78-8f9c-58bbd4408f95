import React from 'react';
import { <PERSON>ton, Tooltip, TooltipGroup } from 'tamagui';
import { Image, View } from 'react-native';
import { Text } from '@rneui/themed';
import { Entypo } from '@expo/vector-icons';
import _toUpper from 'lodash/toUpper';
import _isEmpty from 'lodash/isEmpty';
import UserImage from '@/src/components/atoms/UserImage';
import { BADGES_DETAILS } from 'modules/profile/constants/badges';
import { useSession } from '../../../../../modules/auth/containers/AuthProvider';
import userReader from '../../../../../core/readers/userReader';
import styles from '../../Navbar.Style';
import NavBarPopupOptions from '../NavBarPopupOptions';
import dark from '../../../../../core/constants/themes/dark';

const UserProfileInfo = () => (
  <TooltipGroup delay={100} timeoutMs={0} preventAnimation>
    <Tooltip placement="bottom" delay={{ open: 0, close: 100 }}>
      <Tooltip.Trigger asChild>
        <Button backgroundColor={dark.colors.background} chromeless>
          <UserProfilePopOver />
        </Button>
      </Tooltip.Trigger>
      <Tooltip.Content
        enterStyle={{ x: 0, y: -5, opacity: 0, scale: 0.9 }}
        exitStyle={{ x: 0, y: -5, opacity: 0, scale: 0.9 }}
        backgroundColor={dark.colors.tertiary}
        scale={1}
        x={0}
        y={0}
        padding={8}
        opacity={1}
        animation={[
          'quick',
          {
            opacity: {
              overshootClamping: true,
            },
          },
        ]}
      >
        <Tooltip.Arrow size="$4" backgroundColor={dark.colors.tertiary} />
        <NavBarPopupOptions />
      </Tooltip.Content>
    </Tooltip>
  </TooltipGroup>
);

const UserProfilePopOver = () => {
  const { user } = useSession();

  const badge = BADGES_DETAILS[_toUpper(user?.badge)];
  const shouldShowBadge = !_isEmpty(badge);

  return (
    <View testID="user-profile-tooltip" style={styles.userProfileTabContainer}>
      <UserImage user={user} size={35} />
      <View>
        <Text style={styles.userName}>{userReader.username(user)}</Text>
        <Text style={styles.userRating}>{userReader.rating(user)}</Text>
      </View>
      {shouldShowBadge && <Image source={badge.image} style={styles.badge} />}
      <Entypo name="chevron-down" size={20} color="white" />
    </View>
  );
};

UserProfileInfo.propTypes = {};

export default React.memo(UserProfileInfo);
