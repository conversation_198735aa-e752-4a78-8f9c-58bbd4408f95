import React from 'react';
import { Pressable, View } from 'react-native';
import { useRouter } from 'expo-router';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import styles from './FeedIcon.style';

interface FeedIconProps {
  shouldShowBadge?: boolean;
  size?: number;
}

const FeedIcon: React.FC<FeedIconProps> = ({ shouldShowBadge = false, size = 18 }) => {
  const router = useRouter();

  const handlePress = () => {
    router.navigate('/feed');
  };

  return (
    <Pressable onPress={handlePress} style={styles.container}>
      <View style={styles.iconWrapper}>
        <Icon
          type={ICON_TYPES.IONICON}
          name="notifications-sharp"
          size={size}
          color={dark.colors.textLight}
        />
        {shouldShowBadge && (
          <View style={styles.badge}>
            <View style={styles.badgeInner} />
          </View>
        )}
      </View>
    </Pressable>
  );
};

export default React.memo(FeedIcon);
