import { Image, ImageSourcePropType, Text, View } from 'react-native';
import React from 'react';
import TotalMathletesIcon from '@/assets/images/icons/total_mathletes.png';
import TotalGamesIcon from '@/assets/images/icons/total_games.png';
import numeral from 'numeral';
import usePlatformStats from 'core/graphql/queries/usePlatformStats';
import _toNumber from 'lodash/toNumber';
import styles from './GlobalStats.style';

const renderStats = (
  title: string,
  value: number,
  icon: ImageSourcePropType,
) => (
  <View style={styles.statsContainer}>
    <Image source={icon} style={styles.icon} />
    <View style={styles.statsTextContainer}>
      <Text style={styles.statsTitle}>{title}</Text>
      <Text style={styles.statsValue}>{numeral(value).format('0,0')}</Text>
    </View>
  </View>
);

const GlobalStats = () => {
  const { totalUsers, totalGames } = usePlatformStats();

  return (
    <View style={styles.container}>
      {_toNumber(totalUsers) > 0 &&
        renderStats('Mathletes', totalUsers, TotalMathletesIcon)}
      {_toNumber(totalGames) > 0 &&
        renderStats('Games Played', totalGames * 2, TotalGamesIcon)}
    </View>
  );
};

GlobalStats.propTypes = {};

export default React.memo(GlobalStats);
