import React from 'react';
import { Platform, StyleSheet, View, ImageBackground } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import KatexView from '../../../KatexView';
import dark from '../../../../../core/constants/themes/dark';
import questionBackgroundImage from 'assets/images/questionBackground.png';
import styles from './ExponentQuestion.style';

const ExponentQuestion = (props) => {
  const { question } = props;

  const { isMobile: isCompactMode } = useMediaQuery();

  const { base, exponent } = question;

  const fontSize = Platform.OS !== 'web' ? 64 : isCompactMode ? 28 : 40;

  return (
    <ImageBackground
      source={questionBackgroundImage}
      style={styles.expressionContainer}
    >
      <KatexView expression={`${base}^{${exponent}}`} fontSize={fontSize} />
    </ImageBackground>
  );
};

export default React.memo(ExponentQuestion);
