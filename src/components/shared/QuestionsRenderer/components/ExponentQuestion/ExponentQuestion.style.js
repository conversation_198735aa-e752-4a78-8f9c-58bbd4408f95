import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  expressionContainer: {
    gap: 20,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    maxWidth: 400,
    maxHeight: 400,
    height: '100%',
  },
  tagContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
    backgroundColor: dark.colors.gradientBackground,
    flexShrink: 1,
  },
  tagText: {
    color: dark.colors.textDark,
  },
});

export default styles;
