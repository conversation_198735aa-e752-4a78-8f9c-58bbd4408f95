import React, { useMemo } from 'react';
import { Platform, Text, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import NumberToSvg from 'atoms/NumberToSvg';
import dark from 'core/constants/themes/dark';
import styles from './FractionQuestion.style';
import getTextStyleAccToConfig from '../../utils/getFontSizeAccToConfig';
import { DMASQuestionType } from '../../types';
import ScrollView from '@/src/components/atoms/Scrollview';

interface DMASQuestionProps {
  question: DMASQuestionType;
  style?: React.CSSProperties;
  renderQuestionOverlay?: () => null;
}

interface FractionParts {
  numerator: string;
  denominator: string;
}

const FractionQuestion: React.FC<DMASQuestionProps> = (props) => {
  const isNativeDevice = Platform.OS !== 'web';
  const { question, renderQuestionOverlay } = props;

  const { rows, expression } = question ?? EMPTY_OBJECT;

  const { fontSize } = useMemo(
    () => getTextStyleAccToConfig({ noOfRows: rows }),
    [rows],
  );

  const digitHeight = fontSize + 6;

  const parseFraction = (fractionStr: string): FractionParts => {
    const [numerator, denominator] = fractionStr.split('/');
    return { numerator, denominator };
  };

  const renderFraction = (fractionStr: string, index: number) => {
    const { numerator, denominator } = parseFraction(fractionStr);

    return (
      <View key={`fraction_${index}`} style={styles.fractionContainer}>
        <Text
          style={[styles.questionExpression, { fontSize, textAlign: 'center' }]}
          allowFontScaling={false}
        >
          <NumberToSvg number={numerator} fontSize={fontSize} />
        </Text>
        <View style={[styles.fractionLine]} />
        <Text
          style={[styles.questionExpression, { fontSize, textAlign: 'center' }]}
          allowFontScaling={false}
        >
          <NumberToSvg number={denominator} fontSize={fontSize} />
        </Text>
      </View>
    );
  };

  const renderOperator = (operator: string, index: number) => (
    <View
      key={`operator_${index}`}
      style={[
        styles.operatorContainer,
        {
          justifyContent: 'center',
          paddingHorizontal: 10,
        },
      ]}
    >
      <Text
        style={[styles.questionExpression, { fontSize, textAlign: 'center' }]}
        allowFontScaling={false}
      >
        {operator}
      </Text>
    </View>
  );

  if (_isEmpty(question)) return null;

  if (!Array.isArray(expression)) {
    return (
      <Text style={styles.questionExpression} allowFontScaling={false}>
        {expression}
      </Text>
    );
  }

  return (
    <ScrollView contentContainerStyle={{ flex: 1, justifyContent: 'center' }}>
      <View
        style={[
          styles.expressionContainer,
          {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          },
        ]}
      >
        {expression.map((item, index) => {
          if (index % 2 === 0) {
            return renderFraction(item, index);
          }
          return renderOperator(item, index);
        })}
        {renderOperator('=', 4)}
        {renderOperator('?', 5)}
      </View>
      <View
        style={{
          alignItems: 'center',
          marginTop: 16,
          justifyContent: 'center',
        }}
      >
        <Text
          style={{
            color: dark.colors.textDark,
            fontFamily: 'Montserrat-600',
            fontSize: 12,
          }}
        >
          Answer in the format a/b
        </Text>
      </View>
      {renderQuestionOverlay?.()}
    </ScrollView>
  );
};

export default React.memo(FractionQuestion);
