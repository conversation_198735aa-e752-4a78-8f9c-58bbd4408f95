import React, { useCallback, useMemo, useRef } from 'react';
import { ImageBackground, Platform, Text, View } from 'react-native';

import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _isString from 'lodash/isString';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import _split from 'lodash/split';
import NumberToSvg from 'atoms/NumberToSvg';
import questionBackgroundImage from 'assets/images/questionBackground.png';
import usePracticeQuestionStyles from './DMASQuestion.style';
import getTextStyleAccToConfig from '../../utils/getFontSizeAccToConfig';
import { DMASQuestionType } from '../../types';
import ScrollView from '@/src/components/atoms/Scrollview';

const ALL_OPERATORS = ['+', '-', '×', '*', '÷', '%'];

interface DMASQuestionProps {
  question: DMASQuestionType;
  style?: React.CSSProperties;
  renderQuestionOverlay?: () => null;
}

const DMASQuestion: React.FC<DMASQuestionProps> = (props) => {
  const styles = usePracticeQuestionStyles();
  const isNativeDevice = Platform.OS !== 'web';
  const { question, renderQuestionOverlay } = props;

  const maxDigitsRef = useRef(0);

  const { rows = 2, expression } = question ?? EMPTY_OBJECT;

  const { fontSize } = useMemo(
    () => getTextStyleAccToConfig({ noOfRows: rows }),
    [rows],
  );

  const digitHeight = fontSize + 6;

  const renderDigit = (digit, digitIndex) => (
    <View
      key={`digit_${digitIndex}_${digit}`}
      style={[styles.digitBox, { height: digitHeight }]}
    >
      <Text
        style={[
          styles.questionExpression,
          { fontSize, lineHeight: fontSize + 4, height: digitHeight },
        ]}
        allowFontScaling={false}
      >
        {digit === ' ' ? (
          ' '
        ) : (
          <NumberToSvg number={digit} fontSize={fontSize} />
        )}
      </Text>
    </View>
  );

  const renderExpressionRow = useCallback(
    ({ operator = '', number }, index) => {
      const emptySpaces = new Array(
        Math.max(0, maxDigitsRef.current - _size(_split(number, ''))),
      ).fill(' ');

      return (
        <View
          key={`${operator}_${number}_${index}`}
          style={styles.expressionRow}
        >
          <View style={styles.digitContainer}>
            {_map(emptySpaces, renderDigit)}
            <View
              style={[
                styles.digitBox,
                {
                  height: digitHeight,
                  alignItems: 'center',
                  justifyContent: 'center',
                },
                isNativeDevice && { width: fontSize + 2 },
              ]}
            >
              <Text
                style={[
                  styles.questionExpression,
                  {
                    fontSize,
                    textAlign: 'center',
                  },
                ]}
                allowFontScaling={false}
              >
                {operator}
              </Text>
            </View>
            {_map(_split(number, ''), renderDigit)}
          </View>
        </View>
      );
    },
    [
      styles.expressionRow,
      styles.digitContainer,
      styles.digitBox,
      styles.questionExpression,
      renderDigit,
      digitHeight,
      isNativeDevice,
      fontSize,
    ],
  );

  const groupedExpression = useMemo(() => {
    const groupedExpressionArray = [];
    for (let i = 0; i < _size(expression); ) {
      const obj = { operator: '', number: '' };
      if (_includes(ALL_OPERATORS, expression[i])) {
        obj.operator = expression[i];
        obj.number = expression[i + 1];
        i += 2;
      } else {
        obj.number = expression[i];
        i += 1;
      }

      maxDigitsRef.current = Math.max(
        maxDigitsRef.current,
        _size(_split(obj.number, '')),
      );
      groupedExpressionArray.push(obj);
    }
    return groupedExpressionArray;
  }, [expression]);

  if (_isEmpty(question)) return null;

  if (_isString(expression)) {
    return (
      <ImageBackground
        source={questionBackgroundImage}
        style={[styles.expressionContainer, { gap: 4 }]}
      >
        <Text
          style={[styles.questionExpression, { fontSize }]}
          allowFontScaling={false}
        >
          {expression}
        </Text>
      </ImageBackground>
    );
  }

  return (
    <ScrollView contentContainerStyle={{ flex: 1, justifyContent: 'center' }}>
      <ImageBackground
        source={questionBackgroundImage}
        style={[styles.expressionContainer, { gap: 4 }]}
      >
        {_map(groupedExpression, renderExpressionRow)}
        {renderQuestionOverlay?.()}
      </ImageBackground>
    </ScrollView>
  );
};

export default React.memo(DMASQuestion);
