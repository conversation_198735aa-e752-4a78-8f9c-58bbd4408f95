import React from 'react';
import { Text, View } from 'react-native';
import useStyles from './SumOfSquares.style';

const SumOfSquares = ({ question }) => {
  const { expression } = question;

  const styles = useStyles();

  const tag = `Enter numbers whose squares adds up to ${expression[0]}`;

  return (
    <View style={{ width: '100%' }}>
      <View style={[styles.expressionContainer, { paddingTop: 16 }]}>
        <Text style={styles.primeText}>Sum of Squares of</Text>
        <Text style={styles.expressionText}>{expression[0]}</Text>
      </View>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          marginTop: 16,
        }}
      >
        <View style={styles.tagContainer}>
          <Text style={styles.tagText}>{tag}</Text>
          <Text style={styles.tagText}>separated with spaces</Text>
        </View>
      </View>
    </View>
  );
};

export default React.memo(SumOfSquares);
