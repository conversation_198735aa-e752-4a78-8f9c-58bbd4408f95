import { Pressable, Text, View } from 'react-native';
import React, { useCallback, useMemo } from 'react';
import useUserStore from 'store/useUserStore';
import useNetworkStatus from '@/src/core/hooks/useNetworkStatus';
import { usePathname, useRouter } from 'expo-router';
import style from './OfflineHeader.style';
import useWebsocketStore from '@/src/store/useWebSocketStore';

const regexToMatch = /^\/$|^\/nets\/.*$/;

const OfflineHeader = () => {
  const { toggleMode, isOnline } = useUserStore((state) => ({
    toggleMode: state.toggleMode,
    isOnline: state.isOnline,
  }));

  const { isConnected, ws } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
    ws: state.ws,
  }));

  const isOpen = useMemo(() => ws?.readyState === WebSocket.OPEN, [ws]);

  const router = useRouter();
  const pathname = usePathname();

  const isPathnameMatched = useMemo(
    () => regexToMatch.test(pathname),
    [pathname],
  );

  const { isMatiksReachable } = useNetworkStatus();

  const onPress = useCallback(() => {
    if (!isMatiksReachable) {
      router.replace('/?showNets=true');
    } else {
      router.replace('/');
    }
    toggleMode?.(isMatiksReachable);
  }, [toggleMode, isMatiksReachable]);

  if (isConnected && isOpen) {
    return null;
  }

  if (isOnline && isMatiksReachable) {
    return null;
  }

  if (!isOnline && !isMatiksReachable && isPathnameMatched) {
    return null;
  }

  return (
    <View style={style.container}>
      <Text style={style.title}>
        {!isOnline && isPathnameMatched
          ? 'Tap to Switch back Online'
          : 'You are offline. Please turn on your internet connection or switch to offline mode.'}
      </Text>
      <Pressable style={style.buttonContainer} onPress={onPress}>
        <View style={style.buttonInnerContainer}>
          <Text style={style.buttonText}>
            {!isOnline && isPathnameMatched ? 'Go Online' : 'Go Offline'}
          </Text>
        </View>
      </Pressable>
    </View>
  );
};

export default React.memo(OfflineHeader);
