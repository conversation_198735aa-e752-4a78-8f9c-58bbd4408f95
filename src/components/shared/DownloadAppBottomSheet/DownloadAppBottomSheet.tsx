import React, { useCallback } from 'react';
import { Dimensions, Image, Linking, Text, View } from 'react-native';
import PrimaryButton from 'atoms/PrimaryButton';
import Ionicons from '@expo/vector-icons/Ionicons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import DownloadAppImage from '@/assets/images/banners/Download_App.png';
import styles from './DownloadAppBottomSheet.style';
import Analytics from '../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../core/analytics/const';
import {
  APPSTORE_LINK,
  PLAYSRORE_LINK,
} from '../../../core/constants/appConstants';
import ScrollView from '../../atoms/Scrollview';

const DownloadAppBottomSheet = () => {
  const getAppStoreUrl = useCallback(() => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    if (/iPad|iPhone|iPod/.test(userAgent!) && !(window as any).MSStream) {
      return APPSTORE_LINK;
    }
    return PLAYSRORE_LINK;
  }, []);

  const onDownloadAppPressed = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.CLICKED_ON_DOWNLOAD_APP_NOW_BUTTON_FROM_BOTTOM_SHEET,
    );
    const url = getAppStoreUrl();
    Linking.openURL(url);
  }, [getAppStoreUrl]);

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Image
        source={DownloadAppImage}
        style={styles.image}
        resizeMode="cover"
      />
      <View style={{ gap: 17 }}>
        <Text style={styles.leadingText}>Level up your experience!</Text>
        <View style={[styles.infoDetailRow, { marginTop: 3 }]}>
          <View style={styles.infoIconBox}>
            <Ionicons name="flash-outline" size={15} color="white" />
          </View>
          <Text style={styles.infoText}>
            Enjoy <Text style={styles.infoTextBold}>smoother performance</Text>{' '}
            and quicker matchmaking.
          </Text>
        </View>
        <View style={styles.infoDetailRow}>
          <View style={styles.infoIconBox}>
            <Ionicons name="notifications-outline" size={15} color="white" />
          </View>
          <Text style={styles.infoText}>
            <Text style={styles.infoTextBold}>Stay updated</Text> with live
            challenges and new contests.
          </Text>
        </View>
        <View style={styles.infoDetailRow}>
          <View style={styles.infoIconBox}>
            <MaterialIcons name="mobile-friendly" size={15} color="white" />
          </View>
          <Text style={styles.infoText}>
            Experience a{' '}
            <Text style={styles.infoTextBold}>clean, intuitive interface</Text>{' '}
            built for mobile.
          </Text>
        </View>
      </View>

      <View>
        <PrimaryButton
          onPress={onDownloadAppPressed}
          label="Download App"
          radius={20}
          buttonStyle={{
            height: 40,
            width: Dimensions.get('window').width * 0.9,
          }}
        />
      </View>
    </ScrollView>
  );
};

export default React.memo(DownloadAppBottomSheet);
