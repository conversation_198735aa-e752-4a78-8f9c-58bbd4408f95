import React from 'react';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import { useHectocPuzzleQuestion } from '../../context';
import styles from './HectocClearButton.style';

const HectocClearButton = () => {
  const { clearExpression } = useHectocPuzzleQuestion();

  return (
    <InteractiveSecondaryButton
      label="C"
      onPress={clearExpression}
      buttonStyle={{ height: 48, width: 52 }}
      labelStyle={styles.text}
      borderColor={dark.colors.warning}
    />
  );
};

export default React.memo(HectocClearButton);
