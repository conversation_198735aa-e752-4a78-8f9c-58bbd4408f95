import React, { useMemo } from 'react';
import _toString from 'lodash/toString';
import { CHALLENGE_USER_STATUS } from '@/src/overlays/constants/challengeUserStatus';
import { usePathname } from 'expo-router';
import { useSession } from '../../../modules/auth/containers/AuthProvider';
import WithNotificationBannerAnimationWeb from '../WithNotificationBannerAnimation';
import ChallengeUserStatus from '../../../modules/game/pages/ChallengeUser/ChallengeUserStatus';
import ChallengeRequestCard from '../../../modules/game/pages/ChallengeUser/ChallengeRequestCard';
import { getUserCurrentActivity } from '../../../core/utils/getUserCurrentActivity';
import USER_ACTIVITY from '../../../core/constants/userActivityConstants';
import useChallengeUserEvents from '@/src/overlays/hooks/useChallengeUserEvents';
import useIsPlaying from '@/src/overlays/hooks/useIsPlaying';
import useChallengeUserEventsTrigger from '@/src/overlays/hooks/useChallengeUserEventsTrigger';

const waitingTime = 20;

// TODO: waitTime should be configurable
const ChallengeUserOverlay = () => {
  const isPlayingGame = useIsPlaying();

  const { userId: currentUserId } = useSession();
  const { showChallengeUserOverlay, handleChallengeUserEvents } =
    useChallengeUserEvents();
  const payload = useChallengeUserEventsTrigger(handleChallengeUserEvents);

  const {
    status,
    challengedBy,
    opponentUser = EMPTY_OBJECT,
  } = payload ?? EMPTY_OBJECT;

  const isWaitingForOpponent = useMemo(
    () => _toString(currentUserId) === _toString(challengedBy),
    [currentUserId, challengedBy],
  );
  const currentUrl = usePathname();

  const currActivity = useMemo(
    () => getUserCurrentActivity({ currentUrl }),
    [currentUrl],
  );

  if (currActivity !== USER_ACTIVITY.EXPLORING) {
    return null;
  }

  if (
    !showChallengeUserOverlay ||
    status !== CHALLENGE_USER_STATUS.CHALLENGE_SENT ||
    isPlayingGame
  ) {
    return null;
  }

  if (isWaitingForOpponent) {
    return (
      <WithNotificationBannerAnimationWeb animationDuration={waitingTime}>
        <ChallengeUserStatus />
      </WithNotificationBannerAnimationWeb>
    );
  }
  return (
    <WithNotificationBannerAnimationWeb animationDuration={waitingTime}>
      <ChallengeRequestCard user={opponentUser} payload={payload} />
    </WithNotificationBannerAnimationWeb>
  );
};

export default React.memo(ChallengeUserOverlay);
