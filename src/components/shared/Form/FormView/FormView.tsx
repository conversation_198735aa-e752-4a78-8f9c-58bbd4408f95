import React, { useCallback } from 'react';
import { View } from 'react-native';

import _isFunction from 'lodash/isFunction';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';
import PrimaryButton from '@/src/components/atoms/PrimaryButton';
import Header from 'shared/Header';
import _get from 'lodash/get';
import { FORM_INPUT_TYPES } from '@/src/core/constants/forms';
import styles from './FormView.style';
import { FormViewProps } from '../types/formTypes';
import FormField from '../FormField/FormField';
import useFormFields from '../hooks/useFormFields';
import RangeTextInput from '../components/RangeTextInput';
import ScrollView from '@/src/components/atoms/Scrollview';

const FormView = (props: FormViewProps) => {
  const {
    fields,
    onSubmit,
    renderHeader,
    renderSubmitButton,
    renderAdditionalComponent,
    submitButtonDisabled,
    submitButtonStyle,
    submitButtonText,
    submitButtonTextStyle,
    hideSubmitButton,
    hideHeader,
    headerProps,
  } = props ?? EMPTY_OBJECT;

  const {
    formState,
    errors,
    visibleFields,
    updateFormState,
    validateFormData: validateFormStateData,
    enableValidation,
  } = useFormFields({ formFields: fields });

  const handleOnSubmitPressed = useCallback(() => {
    const isValid = validateFormStateData();
    if (isValid) {
      onSubmit?.(formState);
    } else {
      enableValidation?.();
    }
  }, [onSubmit, enableValidation]);

  const renderSubmitButtonComponent = () => {
    if (hideSubmitButton) {
      return null;
    }

    if (!_isNil(renderSubmitButton) && _isFunction(renderSubmitButton)) {
      return renderSubmitButton?.(
        formState,
        validateFormStateData,
        enableValidation,
      );
    }

    return (
      <PrimaryButton
        label={submitButtonText}
        disabled={submitButtonDisabled}
        style={submitButtonStyle}
        labelStyle={submitButtonTextStyle}
        onPress={handleOnSubmitPressed}
      />
    );
  };

  const renderHeaderComponent = () => {
    if (hideHeader) {
      return null;
    }

    if (!_isNil(renderHeader) && _isFunction(renderHeader)) {
      return renderHeader?.(formState, validateFormStateData, enableValidation);
    }

    return <Header {...headerProps} />;
  };

  const renderFormFields = () =>
    _map(visibleFields, (fieldInfo, index) => {
      const { key } = fieldInfo ?? EMPTY_OBJECT;
      const { type } = fieldInfo ?? EMPTY_OBJECT;

      switch (type) {
        case FORM_INPUT_TYPES.RANGE_TEXT_INPUT:
          const { minValueKey, maxValueKey } = fieldInfo ?? EMPTY_OBJECT;
          return (
            <RangeTextInput
              key={index}
              field={fieldInfo}
              minKeyValue={_get(formState, [minValueKey])}
              maxKeyValue={_get(formState, [maxValueKey])}
              error={_get(errors, [minValueKey]) ?? _get(errors, [maxValueKey])}
              onMinValueChange={(value) =>
                updateFormState({ key: minValueKey, value })
              }
              onMaxValueChange={(value) =>
                updateFormState({ key: maxValueKey, value })
              }
            />
          );
        default:
          return (
            <FormField
              key={index}
              field={fieldInfo}
              value={_get(formState, [key])}
              error={_get(errors, [key])}
              onValueChange={(value) => updateFormState({ key, value })}
            />
          );
      }
    });

  return (
    <View style={styles.container}>
      {renderHeaderComponent()}
      {renderAdditionalComponent?.(
        formState,
        validateFormStateData,
        enableValidation,
      )}
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {renderFormFields()}
        <View
          style={{
            height: 50,
          }}
        />
      </ScrollView>
      {renderSubmitButtonComponent()}
    </View>
  );
};

export default React.memo(FormView);
