import React from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import ShareResultModal from './ShareResultModal';
import { ShareResultModalProps } from './types';

class ShareResultModalContainer extends React.Component<
  {},
  {
    isVisible: boolean;
    modalProps: Partial<ShareResultModalProps<any>> | null;
  }
> {
  private static instance: ShareResultModalContainer | null = null;

  state = {
    isVisible: false,
    modalProps: null,
  };

  static getInstance(): ShareResultModalContainer {
    if (!ShareResultModalContainer.instance) {
      throw new Error('ShareResultModalContainer is not mounted');
    }
    return ShareResultModalContainer.instance;
  }

  componentDidMount() {
    ShareResultModalContainer.instance = this;
  }

  componentWillUnmount() {
    ShareResultModalContainer.instance = null;
  }

  showModal = (props: Partial<ShareResultModalProps<any>>) => {
    this.setState({
      isVisible: true,
      modalProps: props,
    });
    Analytics.track(ANALYTICS_EVENTS.VIEW_SHARE_VIA);
  };

  hideModal = () => {
    Analytics.track(ANALYTICS_EVENTS.CLOSE_SHARE_VIA);
    this.state.modalProps?.onClose?.();
    this.setState({
      isVisible: false,
      modalProps: null,
    });
  };

  render() {
    const { isVisible, modalProps } = this.state;

    return (
      <ShareResultModal
        {...modalProps}
        visible={isVisible}
        onClose={this.hideModal}
      />
    );
  }
}

export const openShareableCardFlow = <T extends unknown>(
  props: Omit<ShareResultModalProps<T>, 'visible' | 'onClose'>,
): void => {
  try {
    const instance = ShareResultModalContainer.getInstance();
    instance.showModal(props);
  } catch (error) {
    console.error('Failed to open shareable card flow:', error);
  }
};

export const closeShareableCardFlow = (): void => {
  try {
    const instance = ShareResultModalContainer.getInstance();
    instance.hideModal();
  } catch (error) {
    console.error('Failed to close shareable card flow:', error);
  }
};

export { ShareResultModal };
export default ShareResultModalContainer;
