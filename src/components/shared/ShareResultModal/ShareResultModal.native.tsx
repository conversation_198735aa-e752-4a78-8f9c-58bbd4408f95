import React, { useEffect, useRef, useState } from 'react';
import {
  Image,
  Modal,
  Platform,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import * as MediaLibrary from 'expo-media-library';
import ViewShot from 'react-native-view-shot';
import Share from 'react-native-share';
import LinearGradient from 'atoms/LinearGradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import {
  ShareResultModalProps,
  SocialPlatform,
} from 'shared/ShareResultModal/types';
import {
  OTHER_PLATFORMS,
  SOCIAL_CONFIGS,
} from 'shared/ShareResultModal/constants';
import _map from 'lodash/map';
import _keys from 'lodash/keys';
import _get from 'lodash/get';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import styles from './ShareResultModal.style';
import ScrollView from '../../atoms/Scrollview';

function ShareResultModal<T>({
  visible,
  onClose,
  renderResultCard,
  message = 'Check out my game results!',
  storyBackgroundColors = EMPTY_OBJECT,
  socialPlatforms = Object.keys(SOCIAL_CONFIGS) as SocialPlatform[],
  onShareSuccess = () => {},
  onShareError = () => {},
}: ShareResultModalProps<T>): JSX.Element {
  const [loading, setLoading] = useState<boolean>(false);
  const [status, requestPermission] = MediaLibrary.usePermissions();
  const [hasMediaPermission, setHasMediaPermission] = useState<boolean>(false);
  const resultCardRef = useRef<View>(null);

  useEffect(() => {
    if (status === null && visible) {
      requestPermission();
    }
  }, [status, requestPermission, visible]);

  // Request media permission for saving to device
  const requestMediaPermission = async (): Promise<boolean> => {
    if (Platform.OS !== 'web') {
      const { status } = await MediaLibrary.requestPermissionsAsync(false, [
        'photo',
      ]);
      setHasMediaPermission(status === 'granted');
      return status === 'granted';
    }
    return false;
  };

  // Function to capture the result card as an image
  const captureResultCard = async (): Promise<string | null> => {
    try {
      if (!resultCardRef.current) return null;

      return resultCardRef.current.capture().then(
        (uri) =>
          // console.info('MOHAN: captured uri', uri);
          uri,
      );
    } catch (error) {
      console.error('Error capturing result card:', error);
      onShareError(error as Error);
      return null;
    }
  };

  // Function to save the image to device
  const saveToDevice = async (uri: string): Promise<void> => {
    try {
      const hasPermission = await requestMediaPermission();

      if (!hasPermission) {
        alert('Permission to access media library is required to save images.');
        return;
      }

      const asset = await MediaLibrary.createAssetAsync(uri);
      await MediaLibrary.createAlbumAsync('MatiksGameResults', asset, false);

      alert('Image saved to gallery!');
      onShareSuccess('download');
    } catch (error) {
      console.error('Error saving image:', error);
      alert('Failed to save image.');
      onShareError(error as Error);
    }
  };

  // Function to handle sharing
  const handleShare = async (platform: SocialPlatform): Promise<void> => {
    Analytics.track(ANALYTICS_EVENTS.SHARE_VIA_CHANNEL, { channel: platform });
    setLoading(true);

    try {
      const imageUri = await captureResultCard();
      setLoading(false);
      if (!imageUri) {
        throw new Error('Failed to capture image');
      }

      if (platform === 'download') {
        await saveToDevice(imageUri);
        return;
      }

      const config = SOCIAL_CONFIGS[platform] ?? OTHER_PLATFORMS[platform];
      const shareOptions = {
        title: message,
        message,
        url: imageUri,
        stickerImage: imageUri,
        ...config.shareOptions,
        ...storyBackgroundColors,
      };

      if (config.shareOptions.social) {
        await Share.shareSingle(shareOptions);
      } else {
        await Share.open(shareOptions);
      }

      onShareSuccess(platform);
    } catch (error) {
      const err = error as Error;
      if (err.message !== 'User did not share') {
        alert(`Failed to share to ${platform}.`);
      }
      onShareError(err, platform);
      Analytics.track(ANALYTICS_EVENTS.SHARE_VIA_ERROR, {
        error: err.message,
        channel: platform,
      });
    } finally {
      setLoading(false);
    }
  };

  const renderIcon = (config) => {
    const {
      icon,
      iconImage,
      iconSize = 24,
      iconType = ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
      iconColor = 'white',
    } = config;

    if (iconImage) {
      return <Image source={iconImage} style={styles.iconImage} />;
    }
    return (
      <Icon name={icon} size={iconSize} color={iconColor} type={iconType} />
    );
  };

  // Render social media buttons
  const renderSocialButton = (platform): JSX.Element => {
    const config =
      _get(SOCIAL_CONFIGS, [platform]) ?? _get(OTHER_PLATFORMS, [platform]);
    if (!config) return null;

    const { color, label, isAllowed = true } = config;

    if (!isAllowed) return null;

    const renderIconContainer = () => {
      if (Array.isArray(color)) {
        return (
          <LinearGradient
            colors={color}
            style={styles.iconContainer}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {renderIcon(config)}
          </LinearGradient>
        );
      }

      return (
        <View style={[styles.iconContainer, { backgroundColor: color }]}>
          {renderIcon(config)}
        </View>
      );
    };

    return (
      <TouchableOpacity
        key={platform}
        style={styles.socialButton}
        onPress={() => handleShare(platform)}
        disabled={loading}
      >
        {renderIconContainer()}
        <Text style={styles.socialButtonText}>{label}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      animationType="slide"
      transparent
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="overFullScreen"
      statusBarTranslucent
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View
              style={{
                flex: 1,
                width: '100%',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <MaterialCommunityIcons
                  name="close"
                  size={24}
                  color={dark.colors.textDark}
                />
              </TouchableOpacity>

              {/* Result Card Container */}
              <ViewShot
                ref={resultCardRef}
                options={{ fileName: 'result-card', format: 'jpg', quality: 1 }}
              >
                {renderResultCard?.()}
              </ViewShot>
            </View>

            {/* Social Share Options */}
            <View style={styles.shareOptionsContainer}>
              <Text style={styles.shareText}>Share via:</Text>
              <ScrollView horizontal>
                <View style={styles.socialButtonsContainer}>
                  {_map(socialPlatforms, (platform) =>
                    renderSocialButton(platform),
                  )}
                </View>
              </ScrollView>
              <View style={styles.moreButtonsContainer}>
                {_map(_keys(OTHER_PLATFORMS), (platform) =>
                  renderSocialButton(platform),
                )}
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

export default React.memo(ShareResultModal);
