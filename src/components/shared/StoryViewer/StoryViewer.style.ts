import { Dimensions, Platform, StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 38,
    paddingVertical: 21,
    paddingHorizontal: 16,
    overflow: 'hidden',
  },
  titleAndDescContainer: {
    gap: 16,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  progressContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 5,
  },
  progressBarContainer: {
    flex: 1,
    height: 5,
    backgroundColor: dark.colors.tertiary,
    borderRadius: 20,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: 'white',
  },
  storyContainer: {
    flex: 1,
    gap: 35,
  },
  titleContainer: {
    padding: 16,
    paddingTop: 24,
  },
  titleText: {
    color: dark.colors.secondary,
    fontSize: 20,
    fontFamily: 'Montserrat-900',
  },
  contentWrapper: {
    flex: 1,
    position: 'relative',
  },
  contentArea: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pressAreasContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    zIndex: 10,
  },
  leftPressArea: {
    width: width / 3,
    height: '100%',
  },
  rightPressArea: {
    width: (width * 2) / 3,
    height: '100%',
    position: 'absolute',
    right: 0,
  },
  contentContainer: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textDark,
    letterSpacing: 1,
    lineHeight: 20,
  },
  riveAnimationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  animatableRiveWrapper: {
    width: 300,
    height: 260,
    overflow: 'hidden',
  },
  riveAnimation: {
    width: 300,
    height: 260,
  },
  riveSize: {
    ...Platform.select({
      web: {
        width: 360,
        height: 300,
      },
      android: {
        width: 360,
        height: 300,
      },
      ios: {
        width: 280,
        height: 240,
      },
    }),
  },
  gradientOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 350,
    zIndex: 3,
  },
});

export default styles;
