import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Pressable, Text, View } from 'react-native';
import _map from 'lodash/map';
import dark from 'core/constants/themes/dark';
import Rive from 'atoms/Rive';
import _size from 'lodash/size';
import LinearGradient from 'atoms/LinearGradient';
import * as Animatable from 'react-native-animatable';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import styles from './StoryViewer.style';

interface StyledTextSegment {
  text: string;
  color?: string;
  style?: object;
}

interface Story {
  id: string;
  title: string;
  content: string | StyledTextSegment[];
  riveAnimation?: string;
  duration?: number;
}

interface StoryViewerProps {
  stories: Story[];
  onIndexChange: Function;
  renderFooter: Function;
}

const DEFAULT_STORY_DURATION = 5000;

const StoryViewer = ({
  stories = [],
  onIndexChange = () => {},
  renderFooter,
}: StoryViewerProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const animatedValues = useRef(
    _map(stories, () => new Animated.Value(0)),
  ).current;
  const animations = useRef<Animated.CompositeAnimation[]>([]);

  useEffect(() => {
    animatedValues.forEach((value) => value.setValue(0));
    setCurrentIndex(0);
  }, [stories]);

  const handleSkip = useCallback(() => {
    animatedValues.forEach((value) => value.setValue(1));
    setCurrentIndex(_size(stories) - 1);
    onIndexChange?.(_size(stories) - 1);
  }, [animatedValues, onIndexChange, stories]);

  const startAnimation = useCallback(
    (index = 0) => {
      const currentStory = stories[index];
      const duration = currentStory?.duration || DEFAULT_STORY_DURATION;
      if (animations.current[index]) {
        animations.current[index].stop();
      }

      animations.current[index] = Animated.timing(animatedValues[index], {
        toValue: 1,
        duration,
        useNativeDriver: false,
      });

      animations.current[index].start(({ finished }: { finished: boolean }) => {
        if (finished && !isPaused) {
          if (index < _size(stories) - 1) {
            setCurrentIndex(index + 1);
            onIndexChange?.(index + 1);
          }
        }
      });
    },
    [animatedValues, stories, isPaused, onIndexChange],
  );

  useEffect(() => {
    if (isPaused) {
      animations.current[currentIndex]?.stop();
    } else {
      startAnimation(currentIndex);
    }
  }, [currentIndex, isPaused, startAnimation]);

  const handlePressIn = useCallback(() => {
    setIsPaused(true);
  }, []);

  const handlePressOut = useCallback(() => {
    setIsPaused(false);
  }, []);

  const handlePressRight = useCallback(() => {
    if (currentIndex < _size(stories) - 1) {
      animations.current[currentIndex]?.stop();
      animatedValues[currentIndex].setValue(1);
      setCurrentIndex((prevIndex) => {
        const newIndex = prevIndex + 1;
        onIndexChange?.(newIndex);
        return newIndex;
      });
    }
  }, [currentIndex, stories, animatedValues, onIndexChange]);

  const handlePressLeft = useCallback(() => {
    if (currentIndex > 0) {
      animations.current[currentIndex]?.stop();
      animatedValues[currentIndex].setValue(0);
      const newIndex = currentIndex - 1;
      animations.current[newIndex]?.stop();
      animatedValues[newIndex].setValue(0);

      setCurrentIndex((prevIndex) => {
        const updatedIndex = prevIndex - 1;
        onIndexChange?.(updatedIndex);
        return updatedIndex;
      });
    }
  }, [currentIndex, animatedValues, onIndexChange]);

  const renderProgressBar = useCallback(
    () => (
      <View style={styles.progressContainer}>
        {_map(stories, (_, index) => {
          const width = animatedValues[index].interpolate({
            inputRange: [0, 1],
            outputRange: ['0%', '100%'],
            extrapolate: 'clamp',
          });

          const backgroundColor =
            index <= currentIndex ? 'white' : dark.colors.tertiary;

          return (
            <View key={index} style={styles.progressBarContainer}>
              <Animated.View
                style={[styles.progressBar, { width, backgroundColor }]}
              />
            </View>
          );
        })}
      </View>
    ),
    [stories, animatedValues, currentIndex],
  );

  const renderTitleComponent = useCallback(() => {
    const story = stories?.[currentIndex];
    return <Text style={styles.titleText}>{story?.title}</Text>;
  }, [stories, currentIndex]);

  const renderStyledContent = useCallback((content: any) => {
    if (typeof content === 'string') {
      return <Text style={styles.contentText}>{content}</Text>;
    }

    return (
      <Text style={styles.contentText}>
        {_map(content, (segment, index) => (
          <Text
            key={index}
            style={[
              segment.style || {},
              segment.color ? { color: segment.color } : {},
            ]}
          >
            {segment.text}
          </Text>
        ))}
      </Text>
    );
  }, []);

  const renderContentComponent = useCallback(() => {
    const story = stories?.[currentIndex];
    if (_isNil(story)) return null;

    return renderStyledContent(story.content);
  }, [stories, currentIndex, renderStyledContent]);

  const renderRiveAnimation = useCallback(() => {
    const story = stories?.[currentIndex];
    if (_isNil(story) || !story.riveAnimation) return null;

    return (
      <View
        style={styles.riveAnimationContainer}
        key={`rive-animation-${story.id}`}
      >
        <Animatable.View
          animation="fadeInUp"
          duration={500}
          style={[styles.animatableRiveWrapper, styles.riveSize]}
        >
          <Rive url={story.riveAnimation} autoPlay style={styles.riveSize} />
        </Animatable.View>
      </View>
    );
  }, [currentIndex, stories]);

  if (_isNil(stories) || _isEmpty(stories)) {
    return null;
  }

  return (
    <View style={styles.container}>
      {renderProgressBar()}

      <View style={styles.storyContainer}>
        <View style={styles.titleAndDescContainer}>
          {renderTitleComponent()}
          {renderContentComponent()}
        </View>
        <View style={styles.riveAnimationContainer}>
          {renderRiveAnimation()}
          {currentIndex < _size(stories) - 1 && (
            <LinearGradient
              colors={[`${dark.colors.background}00`, dark.colors.background]}
              style={styles.gradientOverlay}
            />
          )}
        </View>

        <View style={styles.pressAreasContainer}>
          <Pressable
            style={styles.leftPressArea}
            onPress={handlePressLeft}
            onLongPress={handlePressIn}
            onPressOut={handlePressOut}
            delayLongPress={250}
          />
          <Pressable
            style={styles.rightPressArea}
            onPress={handlePressRight}
            onLongPress={handlePressIn}
            onPressOut={handlePressOut}
            delayLongPress={250}
          />
        </View>
      </View>

      <View style={{ zIndex: 20 }}>
        {renderFooter?.(currentIndex, handleSkip)}
      </View>
    </View>
  );
};

export default StoryViewer;
