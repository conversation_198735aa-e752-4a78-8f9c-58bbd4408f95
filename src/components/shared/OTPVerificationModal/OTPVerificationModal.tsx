import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import styles from './OTPVerificationModal.style';
import Entypo from '@expo/vector-icons/Entypo';
import ScrollView from '../../atoms/Scrollview';

interface OTPVerificationModalProps {
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  onOtpSubmit: (email: string) => void;
}

const OTPVerificationModal: React.FC<OTPVerificationModalProps> = (props) => {
  const { modalVisible, setModalVisible, onOtpSubmit } = props ?? EMPTY_OBJECT;

  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const emailInputRef = useRef<TextInput>(null);

  useEffect(() => {
    if (modalVisible) {
      setEmail('');
      setOtp('');
      setIsOtpSent(false);
      setError('');
      emailInputRef.current?.focus();
    }
  }, [modalVisible]);

  const handleSendOtp = async () => {
    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsOtpSent(true);
      setError('');
    } catch (err) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (otp.trim() === '') {
      setError('Please enter the OTP');
      return;
    }
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setModalVisible(false);
      onOtpSubmit(email);
    } catch (err) {
      setError('Invalid OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleCloseModal = () => setModalVisible(false);

  return (
    <Modal animationType="fade" transparent={true} visible={modalVisible}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Text style={styles.headerText}>OTP Verification</Text>

            <Entypo
              name="cross"
              size={24}
              color={'white'}
              onPress={() => setModalVisible(false)}
            />
          </View>
          <ScrollView>
            {!isOtpSent ? (
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email</Text>
                <TextInput
                  ref={emailInputRef}
                  style={styles.inputField}
                  placeholder="Enter your email"
                  keyboardType="email-address"
                  value={email}
                  onChangeText={setEmail}
                />
              </View>
            ) : (
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>OTP</Text>
                <TextInput
                  style={styles.inputField}
                  placeholder="Enter the OTP"
                  keyboardType="numeric"
                  value={otp}
                  onChangeText={setOtp}
                />
              </View>
            )}
            {error ? <Text style={styles.errorText}>{error}</Text> : null}
          </ScrollView>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCloseModal}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.submitButton, isLoading && styles.disabledButton]}
              onPress={isOtpSent ? handleVerifyOtp : handleSendOtp}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.submitButtonText}>
                  {isOtpSent ? 'Verify OTP' : 'Send OTP'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default React.memo(OTPVerificationModal);
