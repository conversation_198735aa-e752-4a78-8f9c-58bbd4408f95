import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Text, View } from 'react-native';
import TertiaryButton from 'atoms/TertiaryButton';
import userReader from '@/src/core/readers/userReader';
import _isNil from 'lodash/isNil';
import _isFunction from 'lodash/isFunction';
import UserImage from 'atoms/UserImage';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';
import styles from './CancelRematchOverlay.style';

interface CancelRematchOverlayProps {
  opponentUser?: any;
  gameId?: string;
  onCancel: (gameId: string) => void;
  waitingTime: number;
  onTimerEnd?: Function;
}

const CancelRematchOverlay: React.FC<CancelRematchOverlayProps> = ({
  opponentUser = EMPTY_OBJECT,
  gameId,
  onCancel,
  waitingTime,
  onTimerEnd,
}) => {
  const [timeLeft, setTimeLeft] = useState(waitingTime);

  const onTimerEndRef = useRef(onTimerEnd);
  onTimerEndRef.current = onTimerEnd;

  useEffect(() => {
    if (timeLeft <= 0) {
      onTimerEndRef?.current?.();
      return;
    }

    const intervalId = setInterval(() => {
      setTimeLeft((prevTime) => {
        const newTime = prevTime - 1;
        if (newTime <= 0) {
          onTimerEndRef?.current?.();
        }
        return newTime;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, [timeLeft]);

  useEffect(() => {
    setTimeLeft(waitingTime);
  }, [waitingTime]);

  const handleCancel = useCallback(() => {
    if (!_isNil(gameId) && _isFunction(onCancel)) {
      onCancel?.(gameId);
    }
  }, [onCancel, gameId]);

  return (
    <View style={styles.overlay}>
      <View style={styles.container}>
        <View style={styles.userInfo}>
          <UserImage user={opponentUser} />
          <Text style={styles.userNameText}>
            {userReader.displayName(opponentUser)}
          </Text>
        </View>
        <View style={{ gap: 4 }}>
          <View style={styles.timerContainer}>
            <MaterialIcons
              name="timer"
              color={dark.colors.textDark}
              size={16}
            />
            <Text style={styles.timerText}>{`${timeLeft}s`}</Text>
          </View>
          {/* <Text style={styles.gameDurationText}>Rematch Requested</Text> */}
          <Text style={styles.waitingText}>
            {`Waiting for ${userReader.displayName(opponentUser)} to respond…`}
          </Text>
        </View>

        <TertiaryButton
          title="Cancel Request"
          onPress={handleCancel}
          containerStyle={styles.cancelButton}
          titleStyle={styles.cancelButtonText}
        />
      </View>
    </View>
  );
};

export default CancelRematchOverlay;
