import React, { useCallback } from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';
import LinearGradient from '../../../../atoms/LinearGradient';
import Dark from '@/src/core/constants/themes/dark';
import UserImage from '../../../../atoms/UserImage';
import _toUpper from 'lodash/toUpper';
import userReader from 'core/readers/userReader';
import styles from './ExpandedUserStat.style';
import { IRenderStat, UserStatProps } from '../../types';

const ExpandedUserStat = ({ user }: UserStatProps) => {
  // const countryName = getCountryNameFromCountryCode(user?.countryCode);
  // const countryFlag = getCountryFlag(user?.countryCode);

  const renderStat = useCallback(
    ({ label, value, valuePrefix = '' }: IRenderStat) => {
      const statValue = value ? `${valuePrefix}${value}` : '-';
      return (
        <View style={styles.statContainer}>
          <Text style={styles.statLabel}>{_toUpper(label)}</Text>
          <Text style={styles.statValue}>{statValue}</Text>
        </View>
      );
    },
    [],
  );

  return (
    <LinearGradient
      colors={Dark.colors.containerBorderGradientColors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.gradientContainer}
    >
      <View style={styles.darkBackgroundContainer}>
        <LinearGradient
          colors={Dark.colors.containerBorderGradientColors}
          start={{ x: 1, y: 0 }}
          end={{ x: 0, y: 0 }}
          style={styles.gradientContentContainer}
        >
          <View style={styles.container}>
            <View style={styles.profileContainer}>
              <UserImage
                rounded={false}
                user={user}
                style={styles.profileImageContainer}
                size={40}
              />
              <View style={styles.userInfoContainer}>
                <Text style={styles.userInfoName}>
                  {userReader.username(user)}
                </Text>
                {/*<Text style={styles.countryName}>{countryName} {countryFlag}</Text>*/}
              </View>
            </View>
            {renderStat({ label: 'Rating', value: userReader.rating(user) })}
            {renderStat({
              label: 'World Rank',
              value: userReader.globalRank(user),
              valuePrefix: '#',
            })}
            {renderStat({
              label: 'Games Played',
              value: userReader.gamesPlayed(user),
              valuePrefix: '#',
            })}
          </View>
        </LinearGradient>
      </View>
    </LinearGradient>
  );
};

export default React.memo(ExpandedUserStat);
