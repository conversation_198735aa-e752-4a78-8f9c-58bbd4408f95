import React, { useCallback } from 'react';
import { View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import { KENKEN_PUZZLE_ACTION_TYPES } from 'shared/KenKenPuzzleQuestion/hooks/useKenKenPuzzleContextState';
import { useKenKenPuzzleQuestion } from '../../context/context';

import styles from './KenKenPuzzleQuestionActions.style';

const ACTION_CONFIGS = {
  UNDO: {
    iconConfig: {
      name: 'undo-variant',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
      color: 'white',
      size: 16,
    },
  },
  REDO: {
    iconConfig: {
      name: 'redo-variant',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
      color: 'white',
      size: 16,
    },
  },
  CLEAR: {
    iconConfig: {
      name: 'playlist-remove',
      type: ICON_TYPES.MATERIAL_ICONS,
      color: 'white',
      size: 16,
    },
    label: 'CLEAR',
  },
};

const KenKenPuzzleQuestionActions: React.FC = () => {
  const { state, dispatch } = useKenKenPuzzleQuestion();
  const { canUndo, canRedo, selectedCellIndex } = state;
  const isCellSelected = selectedCellIndex !== null;

  const handleUndo = useCallback(() => {
    if (canUndo) {
      dispatch({ type: KENKEN_PUZZLE_ACTION_TYPES.UNDO });
    }
  }, [canUndo, dispatch]);

  const handleRedo = useCallback(() => {
    if (canRedo) {
      dispatch({ type: KENKEN_PUZZLE_ACTION_TYPES.REDO });
    }
  }, [canRedo, dispatch]);

  const handleClear = useCallback(() => {
    dispatch({ type: KENKEN_PUZZLE_ACTION_TYPES.CLEAR_ALL });
  }, [dispatch]);

  return (
    <View style={styles.container}>
      <InteractiveSecondaryButton
        onPress={handleUndo}
        iconConfig={ACTION_CONFIGS.UNDO.iconConfig}
        buttonContainerStyle={[
          styles.redoUndoButtonContainer,
          !canUndo && { opacity: 0.5 },
        ]}
      />

      <InteractiveSecondaryButton
        label={ACTION_CONFIGS.CLEAR.label}
        labelStyle={styles.labelStyle}
        onPress={handleClear}
        iconConfig={ACTION_CONFIGS.CLEAR.iconConfig}
        buttonContainerStyle={styles.clearButtonContainer}
      />

      <InteractiveSecondaryButton
        onPress={handleRedo}
        iconConfig={ACTION_CONFIGS.REDO.iconConfig}
        buttonContainerStyle={[
          styles.redoUndoButtonContainer,
          !canRedo && { opacity: 0.5 },
        ]}
      />
    </View>
  );
};

export default React.memo(KenKenPuzzleQuestionActions);
