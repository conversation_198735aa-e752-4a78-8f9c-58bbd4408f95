import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  cell: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: dark.colors.background,
    position: 'relative',
    borderWidth: 0,
  },
  selectedCell: {
    backgroundColor: dark.colors.puzzle.secondary,
  },
  duplicateCell: {
    backgroundColor: dark.colors.puzzle.error,
  },
  cellText: {
    fontSize: 18,
    fontFamily: 'Montserrat-500',
    color: dark.colors.puzzle.primary,
  },
  operationText: {
    position: 'absolute',
    top: 1,
    left: 2,
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    zIndex: 1,
  },
  pencilMarksContainer: {
    flexDirection: 'row',
    width: '100%',
    position: 'absolute',
    bottom: 2,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  pencilMarkText: {
    fontSize: 10,
    fontFamily: 'Montserrat-500',
    color: dark.colors.puzzle.primary,
    opacity: 0.7,
    marginHorizontal: 2,
    textAlign: 'center',
  },

  cageBorderTop: {
    borderTopWidth: 2,
    borderTopColor: dark.colors.puzzle.primary,
  },
  cageBorderBottom: {
    borderBottomWidth: 2,
    borderBottomColor: dark.colors.puzzle.primary,
  },
  cageBorderLeft: {
    borderLeftWidth: 2,
    borderLeftColor: dark.colors.puzzle.primary,
  },
  cageBorderRight: {
    borderRightWidth: 2,
    borderRightColor: dark.colors.puzzle.primary,
  },
  innerBorderBottom: {
    borderBottomWidth: 0.5,
    borderBottomColor: dark.colors.tertiary,
  },
  innerBorderRight: {
    borderRightWidth: 0.5,
    borderRightColor: dark.colors.tertiary,
  },
});

export default styles;
