import React, { useMemo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { KENKEN_PUZZLE_ACTION_TYPES } from 'shared/KenKenPuzzleQuestion/hooks/useKenKenPuzzleContextState';
import _isNil from 'lodash/isNil';
import _size from 'lodash/size';
import _map from 'lodash/map';
import {
  KenKenGridCellUI,
  useKenKenPuzzleQuestion,
} from '../../context/context';
import styles from './KenKenPuzzleQuestionGrid.style';

const KenKenPuzzleQuestionGrid: React.FC = () => {
  const { state, dispatch } = useKenKenPuzzleQuestion();
  const { gridCells, dimension, selectedCellIndex } = state;
  const { size } = dimension;

  const duplicateCellIndices = useMemo(() => {
    const duplicates = new Set<number>();
    const userInputGrid: (number | null)[][] = Array(size)
      .fill(null)
      .map(() => Array(size).fill(null));

    gridCells.forEach((cell) => {
      if (
        cell.userInput !== null &&
        cell.x !== undefined &&
        cell.y !== undefined
      ) {
        userInputGrid[cell.y][cell.x] = cell.userInput;
      }
    });

    for (let y = 0; y < size; y++) {
      const rowValues = new Map<number, number[]>();
      for (let x = 0; x < size; x++) {
        const value = userInputGrid[y][x];
        if (value !== null) {
          if (!rowValues.has(value)) {
            rowValues?.set(value, []);
          }
          rowValues?.get(value)?.push(y * size + x);
        }
      }

      rowValues?.forEach((indices, value) => {
        if (indices.length > 1) {
          indices.forEach((index) => duplicates.add(index));
        }
      });
    }

    for (let x = 0; x < size; x++) {
      const colValues = new Map<number, number[]>();
      for (let y = 0; y < size; y++) {
        const value = userInputGrid[y][x];
        if (value !== null) {
          if (!colValues.has(value)) {
            colValues?.set(value, []);
          }
          colValues?.get(value)?.push(y * size + x);
        }
      }

      colValues?.forEach((indices, value) => {
        if (indices.length > 1) {
          indices?.forEach((index) => duplicates.add(index));
        }
      });
    }
    return duplicates;
  }, [gridCells, size]);

  const handleCellPress = (index: number) => {
    const cell = gridCells[index];
    const isSelected = index === selectedCellIndex;

    if (cell.userInput !== null && isSelected) {
      dispatch({
        type: KENKEN_PUZZLE_ACTION_TYPES.INPUT_VALUE,
        payload: { index, value: null },
      });
    } else {
      dispatch({
        type: KENKEN_PUZZLE_ACTION_TYPES.SELECT_CELL,
        payload: { index },
      });
    }
  };

  const renderCell = (cell: KenKenGridCellUI, index: number) => {
    const isSelected = index === selectedCellIndex;
    const isDuplicate =
      duplicateCellIndices.has(index) && cell.userInput !== null;

    const cellStyle = [
      styles.cell,
      cell.cageBorders.top && styles.cageBorderTop,
      cell.cageBorders.bottom && styles.cageBorderBottom,
      cell.cageBorders.left && styles.cageBorderLeft,
      cell.cageBorders.right && styles.cageBorderRight,
      cell.innerBorders.bottom &&
        !cell.cageBorders.bottom &&
        styles.innerBorderBottom,
      cell.innerBorders.right &&
        !cell.cageBorders.right &&
        styles.innerBorderRight,
      isDuplicate && styles.duplicateCell,
      isSelected && styles.selectedCell,
    ];

    return (
      <TouchableOpacity
        testID={`grid-item-${index}`}
        key={cell.id}
        style={cellStyle}
        onPress={() => handleCellPress(index)}
        activeOpacity={0.7}
      >
        {cell.isTopLeftOfGroup && cell.operationDescription && (
          <Text
            style={[
              styles.operationText,
              isDuplicate && { color: 'white' },
              isSelected && { color: dark.colors.background },
            ]}
          >
            {cell.operationDescription}
          </Text>
        )}
        {!_isNil(cell.userInput) ? (
          <Text
            style={[
              styles.cellText,
              isDuplicate && { color: 'white' },
              isSelected && { color: dark.colors.background },
            ]}
          >
            {cell.userInput}
          </Text>
        ) : _size(cell.pencilMarks) > 0 ? (
          <View style={styles.pencilMarksContainer}>
            {_map(cell.pencilMarks, (num) => (
              <Text
                key={`pencil-${num}`}
                style={[
                  styles.pencilMarkText,
                  isDuplicate && { color: 'white' },
                  isSelected && { color: dark.colors.background },
                ]}
              >
                {num}
              </Text>
            ))}
          </View>
        ) : (
          <Text
            style={[
              styles.cellText,
              isSelected && { color: dark.colors.background },
            ]}
          />
        )}
      </TouchableOpacity>
    );
  };

  const gridContainerStyle = {
    width: 50 * size,
    height: 50 * size,
    flexDirection: 'row' as 'row',
    flexWrap: 'wrap' as 'wrap',
    backgroundColor: dark.colors.background,
  };

  return (
    <View style={gridContainerStyle}>
      {gridCells.map((cell, index) => renderCell(cell, index))}
    </View>
  );
};

export default React.memo(KenKenPuzzleQuestionGrid);
