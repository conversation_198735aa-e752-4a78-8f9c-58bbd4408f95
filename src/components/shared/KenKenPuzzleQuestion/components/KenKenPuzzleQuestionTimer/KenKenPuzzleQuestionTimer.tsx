import React, { useEffect, useRef, useState } from 'react';
import { Text, View } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';
import _isNaN from 'lodash/isNaN';
import _toString from 'lodash/toString';
import _isNil from 'lodash/isNil';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { useKenKenPuzzleQuestion } from '../../context/context';
import styles from './KenKenPuzzleQuestionTimer.style';

const formatTime = (totalSeconds: number): string => {
  if (_isNaN(totalSeconds) || totalSeconds < 0) {
    return '00:00';
  }
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = Math.floor(totalSeconds % 60);
  return `${_toString(minutes).padStart(2, '0')}:${_toString(seconds).padStart(
    2,
    '0',
  )}`;
};

const KenKenPuzzleQuestionTimer: React.FC = () => {
  const { state } = useKenKenPuzzleQuestion();

  const { startTime, isPuzzleSolved, prevTimeSpent = 0 } = state;

  const [displayedTimeMs, setDisplayedTimeMs] = useState(prevTimeSpent);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const clearCurrentInterval = () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };

    if (isPuzzleSolved) {
      clearCurrentInterval();
      if (!_isNil(startTime)) {
        const finalTime =
          prevTimeSpent + (getCurrentTimeWithOffset() - startTime);

        setDisplayedTimeMs(finalTime >= 0 ? finalTime : 0);
      } else {
        setDisplayedTimeMs(prevTimeSpent >= 0 ? prevTimeSpent : 0);
      }
      return;
    }

    if (!_isNil(startTime)) {
      const initialTime =
        prevTimeSpent + (getCurrentTimeWithOffset() - startTime);
      setDisplayedTimeMs(initialTime >= 0 ? initialTime : 0);

      clearCurrentInterval();

      intervalRef.current = setInterval(() => {
        const currentTotalTime =
          prevTimeSpent + (getCurrentTimeWithOffset() - startTime);
        setDisplayedTimeMs(currentTotalTime >= 0 ? currentTotalTime : 0);
      }, 1000);
    } else {
      clearCurrentInterval();
      setDisplayedTimeMs(prevTimeSpent >= 0 ? prevTimeSpent : 0);
    }

    return () => {
      clearCurrentInterval();
    };
  }, [startTime, isPuzzleSolved, prevTimeSpent]);

  return (
    <View style={styles.container}>
      <MaterialIcons
        name="timer"
        color={dark.colors.puzzle.primary}
        size={16}
      />
      <Text style={styles.time}>
        {formatTime(Math.floor(displayedTimeMs / 1000))}
      </Text>
    </View>
  );
};

export default React.memo(KenKenPuzzleQuestionTimer);
