import React from 'react';
import { TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import dark from 'core/constants/themes/dark';
import { KENKEN_PUZZLE_ACTION_TYPES } from 'shared/KenKenPuzzleQuestion/hooks/useKenKenPuzzleContextState';
import { useKenKenPuzzleQuestion } from '../../context/context';
import styles from './KenKenPuzzleQuestionPencilToggle.style';
import useHaptics from '@/src/core/hooks/useHaptics';

const KenKenPuzzleQuestionPencilToggle: React.FC = () => {
  const { state, dispatch } = useKenKenPuzzleQuestion();
  const { isPencilMode } = state;
  const { triggerHaptic } = useHaptics();

  const togglePencilMode = () => {
    triggerHaptic();
    dispatch({
      type: KENKEN_PUZZLE_ACTION_TYPES.TOGGLE_PENCIL_MODE,
    });
  };

  return (
    <TouchableOpacity
      style={[styles.container, isPencilMode && styles.containerActive]}
      onPress={togglePencilMode}
      activeOpacity={0.7}
    >
      <MaterialIcons
        name="edit"
        color={
          isPencilMode ? dark.colors.background : dark.colors.puzzle.primary
        }
        size={16}
      />
    </TouchableOpacity>
  );
};

export default React.memo(KenKenPuzzleQuestionPencilToggle);
