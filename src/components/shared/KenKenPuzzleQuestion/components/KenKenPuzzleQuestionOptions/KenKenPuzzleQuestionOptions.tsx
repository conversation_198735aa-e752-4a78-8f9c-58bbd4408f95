import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { KENKEN_PUZZLE_ACTION_TYPES } from 'shared/KenKenPuzzleQuestion/hooks/useKenKenPuzzleContextState';
import _map from 'lodash/map';
import { useKenKenPuzzleQuestion } from '../../context/context';
import styles from './KenKenPuzzleQuestionOptions.style';
import useHaptics from '@/src/core/hooks/useHaptics';

const KenKenPuzzleQuestionOptions: React.FC = () => {
  const { state, dispatch } = useKenKenPuzzleQuestion();
  const { dimension, selectedCellIndex, isPencilMode } = state;
  const { size } = dimension;
  const isCellSelected = selectedCellIndex !== null;
  const { triggerHaptic } = useHaptics();

  const handleOptionPress = (value: number) => {
    triggerHaptic();
    dispatch({
      type: KENKEN_PUZZLE_ACTION_TYPES.INPUT_VALUE,
      payload: { value },
    });
  };

  const renderOptionButton = (value: number) => {
    const title = value.toString();
    const key = `num-${value}`;

    const buttonStyle = [styles.optionContainer];

    return (
      <TouchableOpacity
        testID={`footer-${title}`}
        key={key}
        style={buttonStyle}
        onPress={isCellSelected ? () => handleOptionPress(value) : undefined}
        activeOpacity={isCellSelected ? 0.7 : 1}
      >
        <Text style={styles.text}>{title}</Text>
      </TouchableOpacity>
    );
  };

  const numberButtons = [];
  for (let i = 1; i <= size; i++) {
    numberButtons.push(renderOptionButton(i));
  }

  const maxPerRow = 5;
  const rows = [];
  for (let i = 0; i < numberButtons.length; i += maxPerRow) {
    rows.push(numberButtons.slice(i, i + maxPerRow));
  }

  return (
    <View style={styles.container}>
      {_map(rows, (row, index) => (
        <View key={`row-${index}`} style={styles.optionsRow}>
          {row}
        </View>
      ))}
    </View>
  );
};

export default React.memo(KenKenPuzzleQuestionOptions);
