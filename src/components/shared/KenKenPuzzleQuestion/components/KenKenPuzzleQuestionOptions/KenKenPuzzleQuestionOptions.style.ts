import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    gap: 16,
    marginTop: 10,
    marginBottom: 15,
  },
  optionsRow: {
    flexDirection: 'row',
    gap: 16,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  text: {
    fontSize: 16,
    color: 'black',
    fontFamily: 'Montserrat-600',
  },
  optionContainer: {
    width: 40,
    height: 40,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: dark.colors.puzzle.primary,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  disabledContainer: {
    backgroundColor: dark.colors.tertiary,
    opacity: 0.8,
    elevation: 0,
    shadowOpacity: 0,
  },
  pencilToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  pencilButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: dark.colors.background,
    borderWidth: 1,
    borderColor: dark.colors.puzzle.primary,
  },
  pencilButtonActive: {
    backgroundColor: dark.colors.puzzle.secondary,
  },
  pencilButtonText: {
    fontSize: 14,
    color: dark.colors.puzzle.primary,
    fontFamily: 'Montserrat-500',
    marginLeft: 4,
  },
  pencilButtonTextActive: {
    color: dark.colors.background,
  },
});

export default styles;
