import React, { createContext, useContext } from 'react';
import { Kenken } from 'modules/puzzles/utils/kenkenPuzzleGenerator';

export interface KenKenGridCellUI {
  id: number;
  x: number;
  y: number;
  userInput: number | null;
  pencilMarks: number[];
  isEditable: boolean;
  groupId: number;
  cageBorders: {
    top: boolean;
    bottom: boolean;
    left: boolean;
    right: boolean;
  };
  innerBorders: {
    bottom: boolean;
    right: boolean;
  };
  isTopLeftOfGroup: boolean;
  operationDescription: string | undefined;
}

export interface KenKenPuzzleQuestionState {
  gridCells: KenKenGridCellUI[];
  selectedCellIndex: number | null;
  isPuzzleSolved: boolean;
  isPuzzleFilled: boolean;
  startTime: number;
  puzzle: Kenken;
  history: Record<number, number | null>[];
  historyIndex: number;
  canUndo: boolean;
  canRedo: boolean;
  dimension: { size: number };
  prevTimeSpent: number;
  isPencilMode: boolean;
}

export interface KenKenPuzzleQuestionContextValue {
  state: KenKenPuzzleQuestionState;
  dispatch: React.Dispatch<{ type: string; payload?: any }>;
}

export const KenKenPuzzleQuestionContext = createContext<
  KenKenPuzzleQuestionContextValue | undefined
>(undefined);

export const useKenKenPuzzleQuestion = () => {
  const context = useContext(KenKenPuzzleQuestionContext);
  if (!context) {
    throw new Error(
      'useKenKenPuzzleQuestion must be used within a KenKenPuzzleQuestion provider',
    );
  }
  return context;
};
