import { useEffect, useMemo, useReducer, useRef } from 'react';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _isNaN from 'lodash/isNaN';
import _isNil from 'lodash/isNil';
import {
  Kenken,
  KenKenCell,
  KenKenCellGroup,
  parseKenKenString,
} from 'modules/puzzles/utils/kenkenPuzzleGenerator';
import { validateKenKenSolution } from 'modules/puzzles/utils/kenkenPuzzleValidator';
import { PuzzleType } from 'modules/puzzles/types/puzzleType';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import _forEach from 'lodash/forEach';
import _map from 'lodash/map';
import _slice from 'lodash/slice';
import _every from 'lodash/every';
import _isArray from 'lodash/isArray';
import {
  KenKenGridCellUI,
  KenKenPuzzleQuestionContextValue,
  KenKenPuzzleQuestionState,
} from '../context/context';
import useTimeSpentInKenKenPuzzle from './useTimeSpentInKenKenPuzzle';
import useGridStateInKenKenPuzzle from './useGridStateInKenKenPuzzle';

export const KENKEN_PUZZLE_ACTION_TYPES = {
  SELECT_CELL: 'SELECT_CELL',
  INPUT_VALUE: 'INPUT_VALUE',
  TOGGLE_PENCIL_MODE: 'TOGGLE_PENCIL_MODE',
  TOGGLE_PENCIL_MARK: 'TOGGLE_PENCIL_MARK',
  UNDO: 'UNDO',
  REDO: 'REDO',
  CHECK_SOLUTION: 'CHECK_SOLUTION',
  RESET_STATE: 'RESET_STATE',
  CLEAR_ALL: 'CLEAR_ALL',
};

type KenKenAction = { type: string; payload?: any };

interface UseKenKenPuzzleContextStateProps {
  puzzle: PuzzleType;
  onSubmitPuzzle: ({ timeSpent }: { timeSpent: number }) => void;
  shouldCacheTime?: boolean;
  shouldCacheGrid?: boolean;
  onWrongCombination?: () => void;
  onWrongBoxFill?: () => void;
  cachedUserInputs?: (number | null)[] | null;
  cachedPencilMarks?: number[][] | null;
}

const MAX_PENCIL_MARKS = 3;

const initializeGrid = (puzzle: Kenken): KenKenGridCellUI[] => {
  const { size } = puzzle;
  const gridCells: KenKenGridCellUI[] = [];

  for (let y = 0; y < size; y++) {
    for (let x = 0; x < size; x++) {
      const cellData = puzzle.board[x][y];
      const groupId = cellData.cellGroup?.groupID ?? -1;
      const id = y * size + x;

      gridCells.push({
        id,
        x,
        y,
        userInput: null,
        pencilMarks: [],
        isEditable: true,
        groupId,
        cageBorders: { top: false, bottom: false, left: false, right: false },
        innerBorders: { bottom: false, right: false },
        isTopLeftOfGroup: false,
        operationDescription: undefined,
      });
    }
  }

  _forEach(puzzle.cellGroups, (group: KenKenCellGroup) => {
    let topLeftCellIndex: number | null = null;
    let minX = size;
    let minY = size;

    _forEach(group.cells, (cell: KenKenCell) => {
      if (cell.x === undefined || cell.y === undefined) {
        return;
      }
      if (_isNaN(cell.x) || _isNaN(cell.y)) {
        return;
      }

      const cellX = cell.x;
      const cellY = cell.y;

      const gridIndex = cellY * size + cellX;
      if (gridIndex < 0 || gridIndex >= gridCells.length) {
        return;
      }

      if (gridIndex < 0 || gridIndex >= gridCells.length) {
        return;
      }
      const currentGridCell = gridCells[gridIndex];

      if (cellY < minY || (cellY === minY && cellX < minX)) {
        minY = cellY;
        minX = cellX;
        topLeftCellIndex = gridIndex;
      }

      const neighbors = cell.getNeighborsOriented();

      currentGridCell.cageBorders.top = !(
        neighbors.up && neighbors.up.cellGroup?.groupID === group.groupID
      );
      currentGridCell.cageBorders.bottom = !(
        neighbors.down && neighbors.down.cellGroup?.groupID === group.groupID
      );
      currentGridCell.cageBorders.left = !(
        neighbors.left && neighbors.left.cellGroup?.groupID === group.groupID
      );
      currentGridCell.cageBorders.right = !(
        neighbors.right && neighbors.right.cellGroup?.groupID === group.groupID
      );

      currentGridCell.innerBorders.bottom = !!(
        neighbors.down && neighbors.down.cellGroup?.groupID === group.groupID
      );
      currentGridCell.innerBorders.right = !!(
        neighbors.right && neighbors.right.cellGroup?.groupID === group.groupID
      );
    });

    if (topLeftCellIndex !== null) {
      if (topLeftCellIndex >= 0 && topLeftCellIndex < gridCells.length) {
        gridCells[topLeftCellIndex].isTopLeftOfGroup = true;
        gridCells[topLeftCellIndex].operationDescription =
          group.operationDescription;
      }
    } else if (group.cells.length > 0) {
      const firstCell = group.cells[0];
      if (firstCell.x !== undefined && firstCell.y !== undefined) {
        const fallbackIndex = firstCell.y * size + firstCell.x;
        if (fallbackIndex >= 0 && fallbackIndex < gridCells.length) {
          gridCells[fallbackIndex].isTopLeftOfGroup = true;
          gridCells[fallbackIndex].operationDescription =
            group.operationDescription;
        }
      }
    }
  });

  return gridCells;
};

const checkAgainstBoard = (
  puzzle: Kenken,
  currentGridCells: KenKenGridCellUI[],
): boolean => {
  for (const cellUI of currentGridCells) {
    if (
      cellUI.x === undefined ||
      cellUI.y === undefined ||
      cellUI.x < 0 ||
      cellUI.y < 0 ||
      cellUI.x >= puzzle.size ||
      cellUI.y >= puzzle.size
    ) {
      return false;
    }
    const solutionCell = puzzle.board[cellUI.x]?.[cellUI.y];
    if (
      !solutionCell ||
      cellUI.userInput === null ||
      cellUI.userInput !== solutionCell.value
    ) {
      return false;
    }
  }
  return true;
};

const reducer = (
  state: KenKenPuzzleQuestionState,
  action: KenKenAction,
): KenKenPuzzleQuestionState => {
  switch (action.type) {
    case KENKEN_PUZZLE_ACTION_TYPES.SELECT_CELL: {
      const index = action.payload?.index;
      if (
        typeof index !== 'number' ||
        index < 0 ||
        index >= state.gridCells.length
      ) {
        return { ...state, selectedCellIndex: null };
      }
      return { ...state, selectedCellIndex: index };
    }

    case KENKEN_PUZZLE_ACTION_TYPES.INPUT_VALUE: {
      const { index, value } = action.payload ?? {};
      const targetIndex = index !== undefined ? index : state.selectedCellIndex;

      if (
        targetIndex === null ||
        typeof targetIndex !== 'number' ||
        targetIndex < 0 ||
        targetIndex >= state.gridCells.length
      ) {
        return state;
      }

      const currentCell = state.gridCells[targetIndex];
      if (!currentCell?.isEditable) return state;

      if (state.isPencilMode) {
        const parsedValue = parseInt(value, 10);
        if (
          !_isNaN(parsedValue) &&
          parsedValue >= 1 &&
          parsedValue <= state.dimension.size
        ) {
          const newGridCells = [...state.gridCells];
          const newPencilMarks = _isArray(currentCell?.pencilMarks)
            ? [...currentCell.pencilMarks]
            : [];

          const markIndex = newPencilMarks.indexOf(parsedValue);
          if (markIndex !== -1) {
            newPencilMarks.splice(markIndex, 1);
          } else {
            if (newPencilMarks.length >= MAX_PENCIL_MARKS) {
              newPencilMarks.shift();
            }
            newPencilMarks.push(parsedValue);
          }

          newGridCells[targetIndex] = {
            ...currentCell,
            pencilMarks: newPencilMarks,
            userInput: null,
          };

          return {
            ...state,
            gridCells: newGridCells,
          };
        }
        return state;
      }

      let newUserInput: number | null = null;
      if (value !== null && value !== undefined && value !== '') {
        const parsedValue = parseInt(value, 10);
        if (
          !_isNaN(parsedValue) &&
          parsedValue >= 1 &&
          parsedValue <= state.dimension.size
        ) {
          newUserInput = parsedValue;
        } else {
          newUserInput = null;
        }
      }

      if (value === null) {
        newUserInput = null;
      }

      if (currentCell.userInput === newUserInput) return state;

      const newGridCells = _isArray(state?.gridCells)
        ? [...state.gridCells]
        : [];
      newGridCells[targetIndex] = {
        ...currentCell,
        userInput: newUserInput,
        pencilMarks: [],
      };

      const currentUserInputs: Record<number, number | null> = {};
      _forEach(newGridCells, (cell, idx) => {
        if (cell.userInput !== null) {
          currentUserInputs[idx] = cell.userInput;
        }
      });

      const newHistory = _slice(state.history, 0, state.historyIndex + 1);
      const previousInputs = state.history[state.historyIndex] || {}; // Add fallback empty object
      const hasChanged =
        JSON.stringify(previousInputs) !== JSON.stringify(currentUserInputs);

      if (!hasChanged) return state;

      newHistory.push(currentUserInputs);
      const newHistoryIndex = newHistory.length - 1;

      const isNowFilled = _every(
        newGridCells,
        (cell) => !cell.isEditable || cell.userInput !== null,
      );

      let isConsideredSolved = false;
      if (isNowFilled) {
        const isValidByRules = validateKenKenSolution(
          state.puzzle,
          newGridCells,
        );
        const matchesSpecificSolution = checkAgainstBoard(
          state.puzzle,
          newGridCells,
        );
        isConsideredSolved = isValidByRules || matchesSpecificSolution;
      }

      return {
        ...state,
        gridCells: newGridCells,
        isPuzzleFilled: isNowFilled,
        history: newHistory,
        historyIndex: newHistoryIndex,
        canUndo: newHistoryIndex > 0,
        canRedo: false,
        isPuzzleSolved: isNowFilled ? isConsideredSolved : false,
      };
    }

    case KENKEN_PUZZLE_ACTION_TYPES.UNDO: {
      if (!state.canUndo) return state;

      const newHistoryIndex = state.historyIndex - 1;
      const previousUserInputs = state.history[newHistoryIndex];
      const newGridCells = _map(state.gridCells, (cell, idx) => ({
        ...cell,
        userInput:
          previousUserInputs[idx] !== undefined
            ? previousUserInputs[idx]
            : null,
      }));

      const isFilled = _every(
        newGridCells,
        (cell) => !cell.isEditable || cell.userInput !== null,
      );

      let isConsideredSolved = false;
      if (isFilled) {
        const isValidByRules = validateKenKenSolution(
          state.puzzle,
          newGridCells,
        );
        const matchesSpecificSolution = checkAgainstBoard(
          state.puzzle,
          newGridCells,
        );
        isConsideredSolved = isValidByRules || matchesSpecificSolution;
      }

      return {
        ...state,
        gridCells: newGridCells,
        historyIndex: newHistoryIndex,
        canUndo: newHistoryIndex > 0,
        canRedo: true,
        selectedCellIndex: null,
        isPuzzleFilled: isFilled,
        isPuzzleSolved: isFilled ? isConsideredSolved : false,
      };
    }

    case KENKEN_PUZZLE_ACTION_TYPES.REDO: {
      if (!state.canRedo) return state;

      const newHistoryIndex = state.historyIndex + 1;
      const nextUserInputs = state.history[newHistoryIndex];
      const newGridCells = _map(state.gridCells, (cell, idx) => ({
        ...cell,
        userInput:
          nextUserInputs[idx] !== undefined ? nextUserInputs[idx] : null,
      }));

      const isFilled = _every(
        newGridCells,
        (cell) => !cell.isEditable || cell.userInput !== null,
      );

      let isConsideredSolved = false;
      if (isFilled) {
        const isValidByRules = validateKenKenSolution(
          state.puzzle,
          newGridCells,
        );
        const matchesSpecificSolution = checkAgainstBoard(
          state.puzzle,
          newGridCells,
        );
        isConsideredSolved = isValidByRules || matchesSpecificSolution;
      }

      return {
        ...state,
        gridCells: newGridCells,
        historyIndex: newHistoryIndex,
        canUndo: true,
        canRedo: newHistoryIndex < state.history.length - 1,
        selectedCellIndex: null,
        isPuzzleFilled: isFilled,
        isPuzzleSolved: isFilled ? isConsideredSolved : false,
      };
    }

    case KENKEN_PUZZLE_ACTION_TYPES.RESET_STATE: {
      const initialGrid = initializeGrid(state.puzzle);
      const initialHistoryEntry: Record<number, number | null> = {};
      return {
        ...state,
        gridCells: initialGrid,
        selectedCellIndex: null,
        isPuzzleSolved: false,
        isPuzzleFilled: false,
        history: [initialHistoryEntry],
        historyIndex: 0,
        canUndo: false,
        canRedo: false,
        startTime: getCurrentTimeWithOffset(),
        prevTimeSpent: 0,
      };
    }

    case KENKEN_PUZZLE_ACTION_TYPES.CLEAR_ALL: {
      const clearedGridCells = _map(state.gridCells, (cell) => ({
        ...cell,
        userInput: null,
        pencilMarks: null,
      }));
      const clearedUserInputs: Record<number, number | null> = {};
      const newHistory = _slice(state.history, 0, state.historyIndex + 1);

      const previousInputs = state.history[state.historyIndex];
      if (
        JSON.stringify(previousInputs) !== JSON.stringify(clearedUserInputs)
      ) {
        newHistory.push(clearedUserInputs);
      }
      const newHistoryIndex = newHistory.length - 1;

      return {
        ...state,
        gridCells: clearedGridCells,
        isPuzzleFilled: false,
        isPuzzleSolved: false,
        selectedCellIndex: null,
        history: newHistory,
        historyIndex: newHistoryIndex,
        canUndo: newHistoryIndex > 0,
        canRedo: false,
      };
    }

    case KENKEN_PUZZLE_ACTION_TYPES.TOGGLE_PENCIL_MODE: {
      return { ...state, isPencilMode: !state.isPencilMode };
    }

    case KENKEN_PUZZLE_ACTION_TYPES.CHECK_SOLUTION: {
      const isValidByRules = validateKenKenSolution(
        state.puzzle,
        state.gridCells,
      );
      const matchesSpecificSolution = checkAgainstBoard(
        state.puzzle,
        state.gridCells,
      );
      const isConsideredSolved = isValidByRules || matchesSpecificSolution;

      if (!isConsideredSolved) {
        action.payload?.onWrongCombination?.();
      }

      return { ...state, isPuzzleSolved: isConsideredSolved };
    }

    default:
      return state;
  }
};

const useKenKenPuzzleContextState = ({
  puzzle: puzzleObject,
  onSubmitPuzzle,
  onWrongCombination,
  shouldCacheTime = false,
  shouldCacheGrid = false,
  cachedUserInputs = null,
  cachedPencilMarks = null,
}: UseKenKenPuzzleContextStateProps): KenKenPuzzleQuestionContextValue => {
  const puzzle = useMemo(
    () => parseKenKenString(puzzleReader.kenKenPuzzleString(puzzleObject)),
    [puzzleObject],
  );

  const puzzleId = useMemo(() => puzzleReader.id(puzzleObject), [puzzleObject]);

  const {
    initializeGridStateCache,
    clearGridStateCache,
    updateGridStateCache,
  } = useGridStateInKenKenPuzzle({
    puzzleID: puzzleId,
    hasSolved: false,
    shouldCacheGrid,
  });

  const initGridCacheRef = useRef(initializeGridStateCache);
  initGridCacheRef.current = initializeGridStateCache;

  const updateGridCacheRef = useRef(updateGridStateCache);
  updateGridCacheRef.current = updateGridStateCache;

  const [state, dispatch] = useReducer(
    reducer,
    { puzzle, cachedUserInputs, cachedPencilMarks },
    (initArgs: {
      puzzle: Kenken;
      cachedUserInputs: (number | null)[] | null;
      cachedPencilMarks: number[][] | null;
    }): KenKenPuzzleQuestionState => {
      const initialPuzzle = initArgs.puzzle;
      const initialGrid = _map(initializeGrid(initialPuzzle), (cell, index) => {
        const initialUserInput = initArgs?.cachedUserInputs?.[index];
        const initialPencilMarks = initArgs?.cachedPencilMarks?.[index] || [];

        return {
          ...cell,
          userInput: initialUserInput ?? null,
          pencilMarks: initialPencilMarks,
        };
      });

      const isPuzzleFilled = _every(
        initialGrid,
        (cell) => !cell.isEditable || cell.userInput !== null,
      );

      return {
        gridCells: initialGrid,
        selectedCellIndex: null,
        isPuzzleSolved: false,
        isPuzzleFilled,
        startTime: getCurrentTimeWithOffset(),
        puzzle: initialPuzzle,
        history: [],
        historyIndex: 0,
        canUndo: false,
        canRedo: false,
        dimension: { size: initialPuzzle.size },
        prevTimeSpent: 0,
        isPencilMode: false,
      };
    },
  );

  const { prevTimeSpent, initializeTimeSpentCache, clearTimeSpentCache } =
    useTimeSpentInKenKenPuzzle({
      startTime: state.startTime,
      puzzleID: puzzleId,
      hasSolved: state.isPuzzleSolved,
      shouldCacheTime,
    });

  const initTimeCacheRef = useRef(initializeTimeSpentCache);
  initTimeCacheRef.current = initializeTimeSpentCache;

  useEffect(() => {
    if (shouldCacheGrid && !state.isPuzzleSolved) {
      const timeoutId = setTimeout(() => {
        updateGridCacheRef.current(state.gridCells, state.isPuzzleSolved);
      }, 300);

      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [state.gridCells, shouldCacheGrid, state.isPuzzleSolved]);

  useEffect(() => {
    if (!_isNil(puzzleId)) {
      if (!cachedUserInputs) {
        dispatch({ type: KENKEN_PUZZLE_ACTION_TYPES.RESET_STATE });
      }

      if (shouldCacheTime) {
        initTimeCacheRef.current();
      }

      if (shouldCacheGrid && !cachedUserInputs) {
        initGridCacheRef.current();
      }
    }
  }, [puzzleId, shouldCacheTime, shouldCacheGrid, cachedUserInputs]);

  useEffect(() => {
    if (state.isPuzzleFilled) {
      if (state.isPuzzleSolved && !_isNil(state.startTime)) {
        const timeTaken =
          prevTimeSpent + (getCurrentTimeWithOffset() - state.startTime);
        onSubmitPuzzle({ timeSpent: timeTaken });

        if (shouldCacheTime) {
          clearTimeSpentCache();
        }

        if (shouldCacheGrid) {
          clearGridStateCache();
        }
      } else if (!state.isPuzzleSolved) {
        onWrongCombination?.();
      }
    }
  }, [
    state.isPuzzleFilled,
    state.isPuzzleSolved,
    state.startTime,
    onSubmitPuzzle,
    onWrongCombination,
    prevTimeSpent,
    shouldCacheTime,
    shouldCacheGrid,
    clearTimeSpentCache,
    clearGridStateCache,
  ]);

  const contextState = useMemo(
    () => ({
      ...state,
      prevTimeSpent,
    }),
    [state, prevTimeSpent],
  );

  return {
    state: contextState,
    dispatch,
  };
};

export default useKenKenPuzzleContextState;
