import { useCallback, useRef, useState } from 'react';
import _isNil from 'lodash/isNil';
import useLocalCache from 'core/hooks/useLocalCache';
import _get from 'lodash/get';
import _omit from 'lodash/omit';
import _map from 'lodash/map';
import _orderBy from 'lodash/orderBy';
import { KenKenGridCellUI } from '../context/context';

const PUZZLE_GRID_STATE_KEY = 'PUZZLE_GRID_STATE_KEY';
const MAX_CACHE_SIZE = 50;

interface UseGridStateInKenKenPuzzleProps {
  puzzleID: string | number;
  hasSolved: boolean;
  shouldCacheGrid?: boolean;
}

const INITIALIZED_CACHE_DATA = {};

const useGridStateInKenKenPuzzle = ({
  puzzleID,
  hasSolved,
  shouldCacheGrid = false,
}: UseGridStateInKenKenPuzzleProps) => {
  const [cachedUserInputs, setCachedUserInputs] = useState<
    KenKenGridCellUI[] | null
  >(null);
  const { getData, setData } = useLocalCache(PUZZLE_GRID_STATE_KEY);

  const getDataRef = useRef(getData);
  getDataRef.current = getData;

  const setDataRef = useRef(setData);
  setDataRef.current = setData;

  const initializeGridStateCache = useCallback(async (): Promise<
    (number | null)[] | null
  > => {
    if (INITIALIZED_CACHE_DATA[puzzleID] || hasSolved) return null;

    const puzzleGridStateData = await getDataRef.current();
    const cachedPuzzleData = _get(puzzleGridStateData, puzzleID);

    if (!_isNil(cachedPuzzleData)) {
      // Handle both old format (array) and new format (object with userInputs and timestamp)
      const userInputs = cachedPuzzleData.userInputs || cachedPuzzleData;
      setCachedUserInputs(userInputs);
      INITIALIZED_CACHE_DATA[puzzleID] = true;
      return userInputs;
    }
    return null;
  }, [puzzleID, hasSolved]);

  const clearGridStateCache = useCallback(async () => {
    const puzzleGridStateData = await getDataRef.current();
    const puzzleGridState = _get(puzzleGridStateData, puzzleID);

    if (!_isNil(puzzleGridState)) {
      await setDataRef.current(_omit(puzzleGridStateData, puzzleID));
    }
  }, [puzzleID]);

  const updateGridStateCache = useCallback(
    async (gridCells: KenKenGridCellUI[], currentHasSolved = hasSolved) => {
      if (!shouldCacheGrid || currentHasSolved) return;

      const puzzleGridStateData = await getDataRef.current();
      const userInputs = _map(gridCells, (cell) => cell?.userInput);
      const pencilMarks = _map(gridCells, (cell) => cell?.pencilMarks || []);

      // Create a new entry with timestamp
      const timestamp = Date.now();
      const newEntry = {
        userInputs,
        pencilMarks,
        timestamp,
      };

      // Add the new puzzle to the cache
      const updatedCache = {
        ...(puzzleGridStateData ?? EMPTY_OBJECT),
        [puzzleID]: newEntry,
      };

      // Check if we need to remove old entries to maintain the cache size limit
      const puzzleEntries = Object.entries(updatedCache);
      if (puzzleEntries.length > MAX_CACHE_SIZE) {
        // Sort entries by timestamp (oldest first)
        const sortedEntries = _orderBy(
          puzzleEntries.map(([id, data]) => ({
            id,
            timestamp: data.timestamp || 0,
          })),
          ['timestamp'],
          ['asc'],
        );

        // Remove the oldest entries to maintain the cache size limit
        const entriesToRemove = sortedEntries.slice(
          0,
          puzzleEntries.length - MAX_CACHE_SIZE,
        );
        entriesToRemove.forEach(({ id }) => {
          delete updatedCache[id];
        });
      }

      await setDataRef.current(updatedCache);
    },
    [puzzleID, shouldCacheGrid, hasSolved],
  );

  return {
    cachedUserInputs,
    initializeGridStateCache,
    clearGridStateCache,
    updateGridStateCache,
  };
};

export default useGridStateInKenKenPuzzle;
