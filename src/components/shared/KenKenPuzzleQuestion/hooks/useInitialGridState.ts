import { useEffect, useState } from 'react';
import { getStorageState } from 'core/hooks/useStorageState';
import _get from 'lodash/get';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';

const PUZZLE_GRID_STATE_KEY = 'PUZZLE_GRID_STATE_KEY';

interface CachedGridState {
  userInputs: (number | null)[];
  pencilMarks: number[][];
}

const useInitialGridState = (
  puzzleID: string | number,
  shouldCacheGrid: boolean,
) => {
  const [isLoading, setIsLoading] = useState(true);
  const [cachedGridState, setCachedGridState] =
    useState<CachedGridState | null>(null);

  useEffect(() => {
    const loadInitialGridState = async () => {
      if (!shouldCacheGrid) {
        setIsLoading(false);
        return;
      }

      try {
        const stringifiedData = await getStorageState(PUZZLE_GRID_STATE_KEY);
        if (stringifiedData) {
          const parsedData = JSON.parse(stringifiedData);
          const gridState = _get(parsedData, puzzleID);
          if (!_isNil(gridState)) {
            if (!_isEmpty(gridState.userInputs)) {
              setCachedGridState({
                userInputs: gridState.userInputs,
                pencilMarks:
                  gridState.pencilMarks ||
                  Array(gridState.userInputs.length).fill([]),
              });
            } else {
              setCachedGridState({
                userInputs: gridState,
                pencilMarks: Array(gridState.length).fill([]),
              });
            }
          }
        }
      } catch (error) {
        // console.error('Error loading initial grid state:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialGridState();
  }, [puzzleID, shouldCacheGrid]);

  return { cachedGridState, isLoading };
};

export default useInitialGridState;
