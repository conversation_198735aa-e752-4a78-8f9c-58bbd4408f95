import React, { useEffect } from 'react';
import { <PERSON><PERSON> } from 'modules/puzzles/utils/kenkenPuzzleGenerator';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import Loading from 'atoms/Loading';
import KenKenPuzzleQuestionActions from './components/KenKenPuzzleQuestionActions';
import KenKenPuzzleQuestionOptions from './components/KenKenPuzzleQuestionOptions';
import KenKenPuzzleQuestionTimer from './components/KenKenPuzzleQuestionTimer';
import KenKenPuzzleQuestionGrid from './components/KenKenPuzzleQuestionGrid';
import KenKenPuzzleQuestionPencilToggle from './components/KenKenPuzzleQuestionPencilToggle';
import { KenKenPuzzleQuestionContext } from './context/context';
import useKenKenPuzzleContextState from './hooks/useKenKenPuzzleContextState';
import useInitialGridState from './hooks/useInitialGridState';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

export interface KenKenPuzzleQuestionProps {
  puzzle: Kenken;
  onSubmitPuzzle: ({ timeSpent }: { timeSpent: number }) => void;
  onWrongBoxFill?: () => void;
  onWrongCombination?: () => void;
  children: React.ReactNode;
  shouldCacheTime?: boolean;
  shouldCacheGrid?: boolean;
  cachedUserInputs?: (number | null)[] | null;
  cachedPencilMarks?: number[][] | null;
}

const KenKenPuzzleQuestionRoot: React.FC<KenKenPuzzleQuestionProps> = ({
  puzzle,
  onSubmitPuzzle,
  onWrongBoxFill,
  onWrongCombination,
  shouldCacheTime = false,
  shouldCacheGrid = false,
  cachedUserInputs,
  cachedPencilMarks,
  children,
}) => {
  const value = useKenKenPuzzleContextState({
    puzzle: puzzle as any,
    onSubmitPuzzle,
    shouldCacheTime,
    shouldCacheGrid,
    onWrongBoxFill,
    onWrongCombination,
    cachedUserInputs,
    cachedPencilMarks,
  });

  useEffect(() => {
    const intervalId = setInterval(() => {
      // adding this so that if user is taking more than 5 minutes it should not ends the session
      Analytics.track(ANALYTICS_EVENTS.KEN_KEN_PUZZLE.SOLVING_KEN_KEN_PUZZLE);
    }, 60000);
    return () => clearInterval(intervalId);
  }, []);

  return (
    <KenKenPuzzleQuestionContext.Provider value={value}>
      {children}
    </KenKenPuzzleQuestionContext.Provider>
  );
};

const KenKenPuzzleQuestionRootContainer = (props: any) => {
  const { puzzle, shouldCacheGrid = false } = props;
  const puzzleId = puzzleReader.id(puzzle);

  const { cachedGridState, isLoading } = useInitialGridState(
    puzzleId,
    shouldCacheGrid,
  );

  if (isLoading && shouldCacheGrid) {
    return <Loading />;
  }

  return (
    <KenKenPuzzleQuestionRoot
      {...props}
      cachedUserInputs={cachedGridState?.userInputs || null}
      cachedPencilMarks={cachedGridState?.pencilMarks || null}
    />
  );
};

const KenKenPuzzleQuestion = Object.assign(KenKenPuzzleQuestionRootContainer, {
  Grid: KenKenPuzzleQuestionGrid,
  Actions: KenKenPuzzleQuestionActions,
  Options: KenKenPuzzleQuestionOptions,
  Timer: KenKenPuzzleQuestionTimer,
  PencilToggle: KenKenPuzzleQuestionPencilToggle,
});

export default KenKenPuzzleQuestion;
