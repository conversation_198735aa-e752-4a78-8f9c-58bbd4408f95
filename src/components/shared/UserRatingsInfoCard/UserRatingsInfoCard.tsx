import React, { useCallback, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSession } from 'modules/auth/containers/AuthProvider';
import _map from 'lodash/map';
import { GAME_CATEGORY_DETAILED_INFO } from 'core/constants/validUserRatingKeysAndInfo';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import styles from './UserRatingsInfoCard.style';
import { IndividualRatingInfoCardProps } from './types';

const ShimmerCard = () => (
  <View style={styles.individualRatingInfoCard}>
    <View
      style={[
        styles.icon,
        { backgroundColor: dark.colors.tertiary, borderRadius: 4 },
      ]}
    />
    <View style={{ gap: 6 }}>
      <View
        style={{
          height: 12,
          width: 60,
          backgroundColor: dark.colors.tertiary,
          borderRadius: 4,
        }}
      />
      <View
        style={{
          height: 20,
          width: 40,
          backgroundColor: dark.colors.tertiary,
          borderRadius: 4,
        }}
      />
    </View>
  </View>
);

const IndividualRatingInfoCard: React.FC<IndividualRatingInfoCardProps> = (
  props,
) => {
  const { iconUrl, title, rating } = props;
  return (
    <View style={styles.individualRatingInfoCard}>
      <Image source={iconUrl} style={styles.icon} />
      <View style={{ gap: 2 }}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.rating}>{rating ?? '-'}</Text>
      </View>
    </View>
  );
};

const UserRatingsInfoCard = () => {
  const { user, refreshCurrentUser } = useSession();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    Analytics.track(ANALYTICS_EVENTS.REFRESH_RATING);
    if (isRefreshing || !refreshCurrentUser) return;
    setIsRefreshing(true);
    try {
      await refreshCurrentUser();
    } catch (error) {
      // console.error('Failed to refresh user data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshCurrentUser, isRefreshing]);

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Text style={styles.ratingsTitleText}>RATINGS</Text>
        <TouchableOpacity
          onPress={handleRefresh}
          disabled={isRefreshing || !refreshCurrentUser}
          style={{ padding: 4 }}
        >
          {isRefreshing ? (
            <ActivityIndicator size="small" color={dark.colors.textDark} />
          ) : (
            <Icon
              name="refresh"
              type={ICON_TYPES.IONICON as keyof typeof ICON_TYPES}
              size={16}
              color={dark.colors.textDark}
            />
          )}
        </TouchableOpacity>
      </View>
      <View style={styles.innerContainer}>
        {isRefreshing
          ? _map(GAME_CATEGORY_DETAILED_INFO, (_, index) => (
              <ShimmerCard key={`shimmer-${index}`} />
            ))
          : _map(GAME_CATEGORY_DETAILED_INFO, (info, index) => (
              <IndividualRatingInfoCard
                key={index}
                {...info}
                rating={user?.ratingV2?.[info.ratingKey]}
              />
            ))}
      </View>
    </View>
  );
};

export default React.memo(UserRatingsInfoCard);
