import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const CALENDAR_WIDTH = 300;
const DAY_WIDTH = CALENDAR_WIDTH / 7;

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    padding: 10,
    borderRadius: 10,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  weekDaysContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  weekDayCell: {
    width: DAY_WIDTH,
    alignItems: 'center',
  },
  weekDayText: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  todayText: {
    color: dark.colors.errorDark,
    fontFamily: 'Montserrat-600',
  },
  weekRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  dateCell: {
    width: DAY_WIDTH,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateBorder: {
    width: DAY_WIDTH - 8,
    height: DAY_WIDTH - 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
  emptyDateBox: {
    width: DAY_WIDTH,
  },
  crownContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  crownText: {
    fontSize: 15,
  },
});

export default styles;
