import dark from 'core/constants/themes/dark';
import React, { useCallback, useMemo } from 'react';
import _map from 'lodash/map';
import _slice from 'lodash/slice';
import _size from 'lodash/size';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import ProgressIndicator from 'shared/ProgressIndicator';
import generateMonthDates from 'core/utils/generateMonthDates';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _isFunction from 'lodash/isFunction';
import { format } from 'date-fns';
import _toUpper from 'lodash/toUpper';
import styles from './CalendarView.style';
import { CalendarViewProps } from './types';

const DAY_WIDTH = 40;
const WEEK_DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const CalendarView: React.FC<CalendarViewProps> = (props) => {
  const { year, month, activity, onDatePress, renderCalendarDate } =
    props ?? EMPTY_OBJECT;

  const dates = useMemo(
    () => generateMonthDates({ year, month }),
    [year, month],
  );

  const currentDate = useMemo(() => new Date(getCurrentTime()), []);
  const today = useMemo(() => format(currentDate, 'yyyy-MM-dd'), [currentDate]);

  const renderWeekDays = useCallback(
    () => (
      <View style={styles.weekDaysContainer}>
        {_map(WEEK_DAYS, (day) => (
          <View key={day} style={styles.weekDayCell}>
            <Text style={styles.weekDayText}>{_toUpper(day)}</Text>
          </View>
        ))}
      </View>
    ),
    [],
  );

  const { isMobile: isCompactMode } = useMediaQuery();

  const renderDates = useCallback(() => {
    const rows: React.ReactNode[] = [];
    for (let i = 0; i < _size(dates); i += 7) {
      const week = _slice(dates, i, i + 7);
      rows.push(
        <View key={i} style={styles.weekRow}>
          {_map(week, (item) => {
            if (_isFunction(renderCalendarDate)) {
              return renderCalendarDate?.({ item });
            }
            if (item?.isEmpty) {
              return (
                <View key={item?.dateString} style={styles.emptyDateBox} />
              );
            }

            const progress = activity?.[item?.dateString]?.progress || 0;
            const isCompleted = progress === 100;
            const isToday = item?.dateString === today;

            return (
              <TouchableOpacity
                key={item.dateString}
                style={styles.dateCell}
                onPress={() => onDatePress?.({ date: item?.dateString })}
              >
                <View
                  style={[styles.dateBorder, isToday && styles.todayBackground]}
                >
                  <ProgressIndicator
                    progress={progress}
                    size={DAY_WIDTH - 8}
                    strokeWidth={1}
                    color={dark.colors.secondary}
                    strokeColor={dark.colors.tertiary}
                  >
                    {isCompleted ? (
                      <View style={styles.crownContainer}>
                        <Text style={styles.crownText}>👑</Text>
                      </View>
                    ) : (
                      <Text
                        style={[styles.dateText, isToday && styles.todayText]}
                      >
                        {item?.day}
                      </Text>
                    )}
                  </ProgressIndicator>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>,
      );
    }
    return rows;
  }, [dates, activity, onDatePress, today, renderCalendarDate]);

  return (
    <View
      style={[
        styles.container,
        { width: isCompactMode ? Dimensions.get('window').width - 48 : 350 },
      ]}
    >
      {renderWeekDays()}
      {renderDates()}
    </View>
  );
};

// CalendarView.CalendarMonthSelector = CalendarMonthSelector;

export default React.memo(CalendarView);
