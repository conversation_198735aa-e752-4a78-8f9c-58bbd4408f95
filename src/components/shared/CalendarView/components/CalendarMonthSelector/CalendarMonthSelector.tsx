import React, { useCallback } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { parse } from 'date-fns';
import { Text, View } from 'react-native';

import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _isNil from 'lodash/isNil';
import _toUpper from 'lodash/toUpper';
import styles from './CalendarMonthSelector.style';
import { MonthYearTypes } from './types';
import Pressable from '@/src/components/atoms/Pressable';

const MONTH_NAMES = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const isValidMontyAndYear = ({ month, year }: MonthYearTypes): boolean => {
  if (year < 2000 || month < 0 || month > 11) {
    return false;
  }
  const currentDate = new Date(getCurrentTimeWithOffset());
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  return !(
    year > currentYear ||
    (year === currentYear && month > currentMonth)
  );
};

export const getTodaysMonthYear = (selectedDate = ''): MonthYearTypes => {
  let currentDate = new Date(getCurrentTimeWithOffset());
  if (!_isNil(selectedDate)) {
    currentDate = parse(selectedDate, 'yyyy-MM-dd', new Date());
  }
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  return { month: currentMonth, year: currentYear };
};

const CalendarMonthSelector = ({
  selectedMonthYear = getTodaysMonthYear(),
  setSelectedMonthYear,
}: {
  selectedMonthYear: MonthYearTypes;
  setSelectedMonthYear: (monthYear: MonthYearTypes) => void;
}) => {
  const handleLeftPress = useCallback(() => {
    const newMonth =
      selectedMonthYear.month === 0 ? 11 : selectedMonthYear.month - 1;
    const newYear =
      selectedMonthYear.month === 0
        ? selectedMonthYear.year - 1
        : selectedMonthYear.year;
    if (!isValidMontyAndYear({ month: newMonth, year: newYear })) {
      return;
    }

    setSelectedMonthYear?.({ month: newMonth, year: newYear });
  }, [selectedMonthYear, setSelectedMonthYear]);

  const handleRightPress = useCallback(() => {
    const newMonth =
      selectedMonthYear.month === 11 ? 0 : selectedMonthYear.month + 1;
    const newYear =
      selectedMonthYear.month === 11
        ? selectedMonthYear.year + 1
        : selectedMonthYear.year;
    if (!isValidMontyAndYear({ month: newMonth, year: newYear })) {
      return;
    }

    setSelectedMonthYear?.({ month: newMonth, year: newYear });
  }, [selectedMonthYear, setSelectedMonthYear]);

  const formattedMonthYear = `${MONTH_NAMES[selectedMonthYear.month]} ${selectedMonthYear.year}`;

  return (
    <View>
      <View style={styles.container}>
        <Pressable onPress={handleLeftPress} style={styles.arrowButton}>
          <Ionicons name="chevron-back" size={16} color="white" />
        </Pressable>

        <Text style={styles.dateText}>{_toUpper(formattedMonthYear)}</Text>

        <Pressable onPress={handleRightPress} style={styles.arrowButton}>
          <Ionicons name="chevron-forward" size={16} color="white" />
        </Pressable>
      </View>
    </View>
  );
};

CalendarMonthSelector.propTypes = {};

export default React.memo(CalendarMonthSelector);
