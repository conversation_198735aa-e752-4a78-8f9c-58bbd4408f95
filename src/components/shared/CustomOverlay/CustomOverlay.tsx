import React from 'react';
import { View } from 'react-native';
import { Overlay, Text } from '@rneui/themed';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import styles from './CustomOverlay.style';
import { CustomOverlayProps } from './types';

const CustomOverlay: React.FC<CustomOverlayProps> = ({
  isVisible,
  toggleOverlay,
  title,
  cancelText,
  confirmText,
  onCancel,
  onConfirm,
  overlayStyle,
  cancelButtonColor,
  confirmButtonColor,
  confirmButtonTextStyle,
  cancelButtonTextStyle,
  children,
}) => (
  <Overlay
    isVisible={isVisible}
    onBackdropPress={toggleOverlay}
    overlayStyle={[styles.overlay, overlayStyle]}
  >
    <View style={styles.overlayContent}>
      <Text style={styles.overlayText}>{title}</Text>
      {children}
      <View style={styles.overlayButtons}>
        <View style={styles.buttonContainer}>
          <InteractiveSecondaryButton
            label={cancelText}
            onPress={onCancel}
            borderColor={cancelButtonColor || dark.colors.red}
            labelStyle={[styles.cancelLabelStyle, cancelButtonTextStyle]}
          />
        </View>
        <View style={styles.buttonContainer}>
          <InteractiveSecondaryButton
            testID="confirm-button"
            label={confirmText}
            onPress={onConfirm}
            borderColor={confirmButtonColor || dark.colors.secondary}
            labelStyle={confirmButtonTextStyle}
          />
        </View>
      </View>
    </View>
  </Overlay>
);

export default CustomOverlay;
