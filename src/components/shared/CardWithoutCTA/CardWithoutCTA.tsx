import { Text, View } from 'react-native';
import React from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useCardWithoutCTAStyle from './CardWithoutCTA.style';
import { CardWithoutCTAProps } from './types';

const CardWithoutCTA = (props: CardWithoutCTAProps) => {
  const styles = useCardWithoutCTAStyle();
  const { infoText, renderInfoIcon } = props;

  const { isMobile: isCompactDevice } = useMediaQuery();

  return (
    <View
      style={[styles.container, !isCompactDevice && styles.expandedContainer]}
    >
      <View
        style={{
          flexDirection: 'row',
          gap: 10,
          justifyContent: 'flex-start',
          alignItems: 'center',
        }}
      >
        <View style={styles.imageContainer}>{renderInfoIcon?.()}</View>
        <Text style={styles.infoText}>{infoText}</Text>
      </View>
    </View>
  );
};

export default CardWithoutCTA;
