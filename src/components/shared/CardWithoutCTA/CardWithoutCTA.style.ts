import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      width: '100%',
      flexDirection: 'row',
      borderRadius: 12,
      gap: 8,
      backgroundColor: dark.colors.primary,
      paddingVertical: 10,
    },
    expandedContainer: {
      justifyContent: 'space-between',
      paddingRight: 20,
      paddingVertical: 10,
      alignItems: 'center',
      gap: 6,
    },
    titleText: {
      flex: 1,
      fontSize: 14,
      fontFamily: 'Montserrat-500',
      lineHeight: 20,
      maxWidth: '90%',
      color: 'white',
      minHeight: 20,
      paddingVertical: 2, // Add padding to ensure text is fully visible
      textAlignVertical: 'center', // Center text vertically for iOS
    },
    buttonText: {
      fontSize: 13,
      lineHeight: 20,
      fontFamily: 'Montserrat-500',
      color: dark.colors.secondary,
    },
    imageContainer: {
      backgroundColor: dark.colors.gradientBackground,
      height: 40,
      width: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    infoText: {
      color: dark.colors.textDark,
      fontSize: 12,
      fontFamily: 'Montserrat-500',
      lineHeight: 12,
    },
    infoIconBox: {
      backgroundColor: dark.colors.cardBackground,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

const useCardWithoutCTAStyle = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useCardWithoutCTAStyle;
