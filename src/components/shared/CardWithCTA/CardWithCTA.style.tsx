import { StyleSheet } from 'react-native';
import dark from '../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    backgroundColor: dark.colors.primary,
    paddingVertical: 10,
  },
  expandedContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: 20,
    paddingVertical: 10,
    alignItems: 'center',
    gap: 6,
  },
  titleText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    lineHeight: 20,
    maxWidth: '90%',
    color: 'white',
  },
  buttonText: {
    fontSize: 13,
    lineHeight: 20,
    fontFamily: 'Montserrat-500',
    color: dark.colors.secondary,
  },
  imageContainer: {
    backgroundColor: dark.colors.gradientBackground,
    height: 40,
    width: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default styles;
