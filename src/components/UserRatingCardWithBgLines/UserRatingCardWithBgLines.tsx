import { Text, View } from 'react-native';
import styles from 'modules/games/components/GameModeSubSection/GameModesSubSection.styles';
import GradientRotatedLine from '@/src/components/shared/GradientRotatedLine';
import TextWithShadow from 'shared/TextWithShadow';
import dark from 'core/constants/themes/dark';
import React from 'react';
import _map from 'lodash/map';

const UserRatingCardWithBgLines = ({
  ratingLabel,
  userRating,
  textStyle,
  shadowColor,
}: {
  ratingLabel: string;
  userRating: number;
  textStyle: any;
  shadowColor?: string;
}) => {
  const leftLineConfigs = [
    { angle: 15, length: 100 },
    { angle: 0, length: 95 },
    { angle: -15, length: 100 },
  ];

  const rightLineConfigs = [
    { angle: -15, length: 100, flipGradient: true },
    { angle: 0, length: 95, flipGradient: true },
    { angle: 15, length: 100, flipGradient: true },
  ];

  return (
    <View style={styles.ratingContainer}>
      <View style={styles.leftLinesContainer}>
        {_map(leftLineConfigs, (config, index) => (
          <GradientRotatedLine
            key={index}
            angle={config.angle}
            length={config.length}
          />
        ))}
      </View>
      <View style={styles.innerRatingContainer}>
        <Text style={styles.labelText}>{ratingLabel}</Text>
        <TextWithShadow
          text={userRating ?? "0"}
          textStyle={{ ...styles.ratingText, ...textStyle }}
          containerStyle={styles.ratingContainerStyle}
          shadowColor={shadowColor ?? dark.colors.tertiary}
          shadowWidth={8}
          shadowOffsetX={-2}
          shadowOffsetY={-2}
          strokeWidth={5}
          strokeColor="#000000"
          width={130}

        />
      </View>
      <View style={styles.rightLinesContainer}>
        {_map(rightLineConfigs, (config, index) => (
          <GradientRotatedLine
            key={index}
            angle={config.angle}
            length={config.length}
            flipGradient={config.flipGradient}
          />
        ))}
      </View>
    </View>
  );
};

export default React.memo(UserRatingCardWithBgLines);
