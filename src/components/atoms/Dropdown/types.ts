import { StyleProp, ViewStyle } from 'react-native';

export interface DropdownProps {
  testId?: string;
  data: any[];
  onChange: (value: any) => void;
  value: any;
  placeholderText: string;
  searchPlaceholderText: string;
  style?: StyleProp<ViewStyle>;
  placeholderStyle?: StyleProp<ViewStyle>;
  selectedTextStyle?: StyleProp<ViewStyle>;
  inputSearchStyle?: StyleProp<ViewStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  iconStyle?: StyleProp<ViewStyle>;
  itemTextStyle?: StyleProp<ViewStyle>;
}
