import React from 'react';

import FontAwesome from '@expo/vector-icons/FontAwesome';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import AntDesign from '@expo/vector-icons/AntDesign';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import Ionicons from '@expo/vector-icons/Ionicons';
import Entypo from '@expo/vector-icons/Entypo';
import Feather from '@expo/vector-icons/Feather';
import Octicons from '@expo/vector-icons/Octicons';

export const ICON_TYPES = {
  FONT_AWESOME: 'font-awesome',
  FONT_AWESOME_5: 'font-awesome-5',
  FONT_AWESOME_6: 'font-awesome-6',
  ANT_DESIGN: 'antdesign',
  MATERIAL_ICONS: 'material',
  MATERIAL_COMMUNITY_ICONS: 'material-community',
  IONICON: 'ionicon',
  ENTYPO: 'entypo',
  FEATHER: 'feather',
  OCTICONS: 'octicons',
};

export type IconType = keyof typeof ICON_TYPES;

const ICON_COMPONENT = {
  [ICON_TYPES.FONT_AWESOME]: FontAwesome,
  [ICON_TYPES.FONT_AWESOME_5]: FontAwesome5,
  [ICON_TYPES.FONT_AWESOME_6]: FontAwesome6,
  [ICON_TYPES.ANT_DESIGN]: AntDesign,
  [ICON_TYPES.MATERIAL_ICONS]: MaterialIcons,
  [ICON_TYPES.MATERIAL_COMMUNITY_ICONS]: MaterialCommunityIcons,
  [ICON_TYPES.IONICON]: Ionicons,
  [ICON_TYPES.ENTYPO]: Entypo,
  [ICON_TYPES.FEATHER]: Feather,
  [ICON_TYPES.OCTICONS]: Octicons,
};

interface IconProps {
  type?: keyof typeof ICON_TYPES;
  name: string;
  size?: number;
  color?: string;
}

const Icon: React.FC<IconProps> = (props) => {
  const { type = ICON_TYPES.FONT_AWESOME, ...restProps } = props;

  const IconComponent = ICON_COMPONENT[type];
  return <IconComponent {...restProps} />;
};

export default React.memo(Icon);
