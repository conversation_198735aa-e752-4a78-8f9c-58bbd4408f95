import { StyleProp, StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { Button, Text } from '@rneui/themed';
import Dark from '@/src/core/constants/themes/dark';
import React from 'react';

interface PrimaryButtonProps {
  testID?: string;
  label: string;
  onPress: () => void;
  labelStyle?: StyleProp<TextStyle>;
  buttonStyle?: StyleProp<ViewStyle>;
  radius?: 'sm' | 'md' | 'lg' | 'xl' | number;
  disabled?: boolean;
}

const styles = StyleSheet.create({
  primaryButton: {
    backgroundColor: Dark.colors.secondary,
    gap: 8,
  },
  label: {
    textAlign: 'center',
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
});

const PrimaryButton: React.FC<PrimaryButtonProps> = (props) => {
  const {
    testID,
    label,
    onPress,
    labelStyle,
    buttonStyle,
    radius = 'sm',
    disabled = false,
  } = props;

  return (
    <Button
      testID={testID}
      onPress={onPress}
      radius={radius}
      type="solid"
      buttonStyle={[styles.primaryButton, buttonStyle]}
      disabled={disabled}
    >
      <Text style={[styles.label, labelStyle]}>{label}</Text>
    </Button>
  );
};

export default React.memo(PrimaryButton);
