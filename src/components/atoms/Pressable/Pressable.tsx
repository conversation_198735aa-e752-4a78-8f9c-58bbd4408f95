import useHaptics from '@/src/core/hooks/useHaptics';
import React, { useCallback } from 'react';
import {
  Pressable as RNPressable,
  GestureResponderEvent,
} from 'react-native';
import { CustomPressableProps } from './types';

const Pressable = (props: CustomPressableProps) => {
  const { triggerHaptic } = useHaptics();
  const { onPress, impactFeedbackStyle } = props;

  const handlePress = useCallback(
    (e: GestureResponderEvent) => {
      triggerHaptic(impactFeedbackStyle);
      onPress?.(e);
    },
    [onPress, triggerHaptic],
  );

  return <RNPressable {...props} onPress={handlePress} />;
};

export default React.memo(Pressable);
