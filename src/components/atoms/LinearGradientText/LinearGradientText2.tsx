import React from 'react';
import Svg, { Defs, LinearGradient, Stop, Text, TSpan } from 'react-native-svg';
import Dark from '@/src/core/constants/themes/dark';

interface LinearGradientTextProps {
  text: string;
  colors?: string[];
  fontSize: number;
  viewBoxWidth: number;
  viewBoxHeight: number;
}

const LinearGradientText: React.FC<LinearGradientTextProps> = ({
  text,
  colors = [Dark.colors.gradientLeft, Dark.colors.gradientRight],
  fontSize,
  viewBoxWidth,
  viewBoxHeight,
}) => {
  const gradientId = 'rainbow'; // Unique ID for the gradient

  return (
    <Svg
      viewBox={`0 0 ${viewBoxWidth} ${viewBoxHeight}`}
      height={viewBoxHeight}
      width={viewBoxWidth}
    >
      <Defs>
        <LinearGradient
          id={gradientId}
          x1="0%"
          y1="0%"
          x2="100%"
          y2="0%"
          gradientUnits="userSpaceOnUse"
        >
          {colors.map((color, index) => (
            <Stop
              key={index}
              offset={`${(index / (colors.length - 1)) * 100}%`}
              stopColor={color}
            />
          ))}
        </LinearGradient>
      </Defs>
      <Text
        fill={`url(#${gradientId})`}
        fontSize={fontSize}
        fontWeight="bold"
        fontFamily="Montserrat"
        textAnchor="middle"
        x={viewBoxWidth / 2}
        y={viewBoxHeight / 1.2}
      >
        <TSpan>{text}</TSpan>
      </Text>
    </Svg>
  );
};

export default React.memo(LinearGradientText);
