import React from 'react';
import { ScrollView } from 'react-native';

interface ScrollViewComponentProps
  extends React.ComponentProps<typeof ScrollView> {}

const ScrollViewComponent: React.FC<ScrollViewComponentProps> = (props) => {
  return (
    <ScrollView
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      keyboardDismissMode="none"
      {...props}
    />
  );
};

export default React.memo(ScrollViewComponent);
