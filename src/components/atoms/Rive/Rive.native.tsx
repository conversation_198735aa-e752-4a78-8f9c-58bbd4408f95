import React, { useEffect, useState } from 'react';
import Rive from 'rive-react-native';
import { Text, View, ViewStyle } from 'react-native';
import dark from 'core/constants/themes/dark';

interface RiveComponentProps {
  url: string;
  resourceName?: string;
  artboardName: string;
  stateMachineName: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  onLoopEnd?: () => void;
}

const RiveComponent: React.FC<RiveComponentProps> = (props) => {
  const {
    url,
    resourceName,
    artboardName,
    stateMachineName,
    style,
    autoPlay,
    loop = true,
    onLoopEnd,
  } = props;

  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setHasError(false);
  }, [url]);

  const handleError = (error) => {
    setHasError(true);
  };

  if (hasError) {
    return (
      <View style={style}>
        <Text style={{ color: dark.colors.textDark }}>Rive animation</Text>
      </View>
    );
  }

  return (
    <Rive
      url={url}
      resourceName={resourceName}
      stateMachineName={stateMachineName}
      artboardName={artboardName}
      style={style}
      autoplay={autoPlay}
      loop={loop}
      onLoopEnd={onLoopEnd}
      onError={handleError}
    />
  );
};

export default React.memo(RiveComponent);
