import React from 'react';
import { fireEvent, render } from '@testing-library/react-native';
import TertiaryButton from '../index';

describe('TertiaryButton', () => {
  it('renders without crashing', () => {
    render(
      <TertiaryButton
        title="Test Button"
        label="Test Button"
        onPress={() => {}}
        titleStyle={{}}
      />,
    );
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByText } = render(
      <TertiaryButton
        title="Test Button"
        label="Test Button"
        onPress={onPress}
        titleStyle={{}}
      />,
    );
    const button = getByText('Test Button');
    fireEvent.press(button);
    expect(onPress).toHaveBeenCalled();
  });
});
