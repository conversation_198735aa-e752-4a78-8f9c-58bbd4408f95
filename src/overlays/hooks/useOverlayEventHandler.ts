import { EventName } from '@/src/core/event/types';
import EventManager from 'core/event';
import { useEffect, useRef, useState } from 'react';

const useOverlayEventHandler = (
  eventName: EventName,
  listenersName: string,
  handleEvent: ({ payload }: { payload: any }) => void,
) => {
  const [payload, setPayload] = useState<any>(EMPTY_OBJECT);
  const subscriptionRef = useRef<any>(null);

  useEffect(() => {
    const eventManager = new EventManager();
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
    }
    subscriptionRef.current = eventManager.on(
      eventName,
      listenersName,
      (_payload) => {
        setPayload(_payload);
        handleEvent({ payload: _payload });
      },
    );
    return () => {
      subscriptionRef.current?.unsubscribe();
    };
  }, []);
  return payload;
};

export default useOverlayEventHandler;
