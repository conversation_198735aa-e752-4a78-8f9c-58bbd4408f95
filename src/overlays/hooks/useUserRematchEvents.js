import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import { usePathname, useRouter } from 'expo-router';
import fa_notification from '@/assets/audio/fa_notification.mp3';
import _isEqual from 'lodash/isEqual';
import { REMATCH_REQUEST_GAME_TYPE } from '@/src/modules/game/pages/Rematch/RequestCard/RequestCard';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useCancelRematchRequest from 'modules/game/hooks/mutations/useCancelRematchRequest';
import _get from 'lodash/get';
import { getCurrentActiveGameId } from '@/src/core/utils/getUserCurrentActivity';
import userReader from 'core/readers/userReader';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import _isNil from 'lodash/isNil';
import { REMATCH_REQUEST_STATUS } from '../constants/rematchRequests';
import useSound from 'core/hooks/useSound';
import useIsPlaying from '../hooks/useIsPlaying';

const useUserRematchEvents = () => {
  const router = useRouter();
  const { userId: currentUserId } = useSession();
  const { cancelRematchRequest } = useCancelRematchRequest();
  const currentUrl = usePathname();
  const isPlayingGame = useIsPlaying();

  const currActiveGameId = useMemo(
    () => getCurrentActiveGameId({ currentUrl }),
    [currentUrl],
  );

  const [showRematchRequestOverlay, setShowRematchRequestOverlay] =
    useState(false);
  const [showCancelOverlay, setShowCancelOverlay] = useState(false);
  const [rematchPayload, setRematchPayload] = useState(null);
  const [cancelTimeoutId, setCancelTimeoutId] = useState(null);

  const { playSound } = useSound({
    soundFile: fa_notification,
  });
  const playSoundTimeoutRef = useRef(null);


  const clearExistingTimeout = useCallback(() => {
    if (cancelTimeoutId) {
      clearTimeout(cancelTimeoutId);
      setCancelTimeoutId(null);
    }
  }, [cancelTimeoutId]);

  useEffect(
    () => () => {
      clearExistingTimeout();
    },
    [clearExistingTimeout],
  );

  const handleRematchRequested = useCallback(
    ({ payload }) => {
      clearExistingTimeout();
      setRematchPayload(payload);
      const { waitingTime, opponentUser, requestedBy, gameId } = payload;

      if (String(currentUserId) === String(requestedBy)) {
        setShowCancelOverlay(true);
        setShowRematchRequestOverlay(false);

        if (waitingTime) {
          const timeoutId = setTimeout(() => {
            const stillActiveGameId = getCurrentActiveGameId({ currentUrl });
            if (_isEqual(stillActiveGameId, gameId)) {
              setShowCancelOverlay(false);
              showToast({
                type: TOAST_TYPE.INFO,
                description: `${userReader.displayName(opponentUser) || 'Opponent'} did not respond.`,
              });
            }
            setCancelTimeoutId(null);
          }, waitingTime * 1000);
          setCancelTimeoutId(timeoutId);
        }
      } else {
        if (!isPlayingGame) {
          if (playSoundTimeoutRef.current) {
            clearTimeout(playSoundTimeoutRef.current);
          }
          playSoundTimeoutRef.current = setTimeout(() => playSound(), 500);
        }
        setShowRematchRequestOverlay(true);
        setShowCancelOverlay(false);
        Analytics.track(
          ANALYTICS_EVENTS.REMATCH_REQUEST_OVERLAY.RECEIVED_REMATCH_REQUEST,
          { opponentUserId: opponentUser?._id },
        );

        if (waitingTime) {
          const timeoutId = setTimeout(() => {
            setShowRematchRequestOverlay(false);
            setCancelTimeoutId(null);
          }, waitingTime * 1000);
          setCancelTimeoutId(timeoutId);
        }
      }
    },
    [playSound, currentUserId, clearExistingTimeout, currentUrl, isPlayingGame],
  );

  useEffect(() => {
    return () => {
      if (playSoundTimeoutRef.current) {
        clearTimeout(playSoundTimeoutRef.current);
      }
    };
  }, []);

  const handleRematchAccepted = useCallback(
    ({ payload }) => {
      clearExistingTimeout();
      hideToast();
      setShowRematchRequestOverlay(false);
      setShowCancelOverlay(false);
      const { opponentUser, newGameId, requestedBy, gameType, originalGameId } =
        payload ?? {};

      const isOnSameGame = _isEqual(currActiveGameId, originalGameId);

      if (String(currentUserId) === String(requestedBy) && isOnSameGame) {
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: `${userReader.displayName(opponentUser)} Accepted Game Request`,
        });
        Analytics.track(
          ANALYTICS_EVENTS.REMATCH_REQUEST_OVERLAY
            .OPPONENT_ACCEPTED_REMATCH_REQUEST,
          { opponentUserId: userReader.id(opponentUser) },
        );
      }

      if (!_isNil(newGameId)) {
        if (gameType === REMATCH_REQUEST_GAME_TYPE.PUZZLE) {
          router.replace(`/puzzle-game/game/${newGameId}`);
        } else {
          router.replace(`/game/${newGameId}/play`);
        }
      }
    },
    [router, currentUserId, clearExistingTimeout, currActiveGameId],
  );

  const handleRematchRejected = useCallback(
    ({ payload }) => {
      clearExistingTimeout();
      hideToast();
      setShowRematchRequestOverlay(false);
      setShowCancelOverlay(false);
      const { opponentUser, requestedBy, gameId } = payload;
      const isOnSameGame = _isEqual(currActiveGameId, gameId);

      if (String(currentUserId) === String(requestedBy) && isOnSameGame) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `${userReader.displayName(opponentUser)} Rejected Rematch Request`,
        });
        Analytics.track(
          ANALYTICS_EVENTS.REMATCH_REQUEST_OVERLAY
            .OPPONENT_REJECTED_REMATCH_REQUEST,
          { opponentUserId: userReader.id(opponentUser) },
        );
      }
    },
    [currentUserId, clearExistingTimeout, currActiveGameId],
  );

  const handleCancelRematch = useCallback(
    async (gameId) => {
      if (!gameId) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: 'Failed to cancel rematch.',
        });
        return;
      }
      const isOnSameGame = _isEqual(currActiveGameId, gameId);
      clearExistingTimeout();
      try {
        const response = await cancelRematchRequest({ gameId });
        const cancelledRematchRequest = _get(
          response,
          ['data', 'cancelRematchRequest'],
          false,
        );
        if (cancelledRematchRequest) {
          setShowCancelOverlay(false);
          if (isOnSameGame) {
            showToast({
              type: TOAST_TYPE.INFO,
              description: 'Rematch request cancelled.',
            });
          }
        }
      } catch (error) {
        if (isOnSameGame) {
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Failed to cancel rematch.',
          });
        }
      }
    },
    [cancelRematchRequest, clearExistingTimeout, currActiveGameId],
  );

  const handleRequestTimeout = useCallback(
    ({ payload }) => {
      clearExistingTimeout();
      hideToast();
      setShowRematchRequestOverlay(false);
      setShowCancelOverlay(false);
      const { opponentUser, requestedBy, gameId } = payload;
      const isOnSameGame = _isEqual(currActiveGameId, gameId);

      if (String(currentUserId) === String(requestedBy) && isOnSameGame) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `${opponentUser?.name || 'Opponent'} didn't respond in time.`,
        });
      }
    },
    [currentUserId, clearExistingTimeout, currActiveGameId],
  );

  const handleRematchCancelled = useCallback(
    ({ payload }) => {
      clearExistingTimeout();
      hideToast();
      setShowRematchRequestOverlay(false);
      setShowCancelOverlay(false);
      const { opponentUser, requestedBy, gameId } = payload;
      const isOnSameGame = _isEqual(currActiveGameId, gameId);

      if (String(currentUserId) !== String(requestedBy) && isOnSameGame) {
        showToast({
          type: TOAST_TYPE.INFO,
          description: `${opponentUser?.name || 'Opponent'} cancelled the rematch request.`,
        });
      }
    },
    [currentUserId, clearExistingTimeout, currActiveGameId],
  );

  const handleRematchRequestEvents = useCallback(
    ({ payload }) => {
      setRematchPayload(payload);

      const { status } = payload;
      switch (status) {
        case REMATCH_REQUEST_STATUS.REMATCH_REQUESTED: {
          handleRematchRequested({ payload });
          break;
        }
        case REMATCH_REQUEST_STATUS.REMATCH_ACCEPTED:
          handleRematchAccepted({ payload });
          break;
        case REMATCH_REQUEST_STATUS.REMATCH_REJECTED:
          handleRematchRejected({ payload });
          break;
        case REMATCH_REQUEST_STATUS.REMATCH_AUTO_CLOSED: {
          handleRequestTimeout({ payload });
          break;
        }
        case REMATCH_REQUEST_STATUS.REMATCH_CANCELLED: {
          handleRematchCancelled({ payload });
          break;
        }
        default:
          break;
      }
    },
    [
      handleRematchRequested,
      handleRematchAccepted,
      handleRematchRejected,
      handleRequestTimeout,
      handleRematchCancelled,
    ],
  );

  return {
    showRematchRequestOverlay,
    showCancelOverlay,
    setShowCancelOverlay,
    handleRematchRequestEvents,
    handleCancelRematch,
    rematchPayload,
  };
};

export default useUserRematchEvents;
