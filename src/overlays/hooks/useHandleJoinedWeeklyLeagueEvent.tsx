import { useCallback } from 'react';
import _get from 'lodash/get';
import { openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import PromotionOrDemotionInfoBottomSheet from 'modules/matiksWeeklyLeague/components/PromotionOrDemotionInfoBottomSheet';
import { WEEKLY_LEAGUE_INFO } from 'modules/matiksWeeklyLeague/constants/weeklyLeagueTypes';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

const useHandleJoinedWeeklyLeagueEvent = () => {
  const handleUserJoinedWeeklyLeagueEvent = useCallback(
    ({ payload }: { payload: any }) => {
      const league = _get(payload, 'leagueInfo.league', '');
      const progressState = _get(payload, 'leagueInfo.progressState', '');
      const leagueDetails = WEEKLY_LEAGUE_INFO[league];
      Analytics.track(ANALYTICS_EVENTS.WEEKLY_LEAGUE, {
        league,
        progressState,
      });

      openBottomSheet({
        content: ({ closeBottomSheet }: { closeBottomSheet: () => void }) => (
          <PromotionOrDemotionInfoBottomSheet
            league={league}
            progressState={progressState}
            closeBottomSheet={closeBottomSheet}
          />
        ),
        styles: {
          frame: {
            borderTopColor: leagueDetails.textColor,
          },
        },
      });
    },
    [],
  );

  return {
    handleUserJoinedWeeklyLeagueEvent,
  };
};

export default useHandleJoinedWeeklyLeagueEvent;
