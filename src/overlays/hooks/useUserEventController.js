import _isEqual from 'lodash/isEqual';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import usePrevious from 'core/hooks/usePrevious';
import useHandleJoinedWeeklyLeagueEvent from '@/src/overlays/hooks/useHandleJoinedWeeklyLeagueEvent';
import useUserEventsSubscription from './useUserEventsSubscription';
import { useSession } from '../../modules/auth/containers/AuthProvider';
import { USER_EVENTS } from '../constants/userEvents';
import Analytics from '../../core/analytics';
import { ANALYTICS_EVENTS } from '../../core/analytics/const';
import useUserRematchEvents from './useUserRematchEvents';
import useChallengeUserEvents from './useChallengeUserEvents';
import useUserShowdownNotificationEvent from './useUserShowdownNotificationEvent';
import useUserRatingFixtureEvents from './useRatingFixtureEvents';
import useUserStatikCoinsEarnedEvent from './useUserStatikCoinsEarnedEvent';
import useHandleStreakMaintainedEvent from './useStreakMaintainedEvent';

const useUserEventController = () => {
  const { userId, refreshCurrentUser } = useSession();

  const { event, payload } = useUserEventsSubscription(userId);

  const { status } = payload ?? EMPTY_OBJECT;

  const prevEvent = usePrevious(event);
  const prevStatus = usePrevious(status);

  const { handleUserRatingFixtureEvents } = useUserRatingFixtureEvents();

  const handleUserRatingFixtureEvent = useCallback(() => {
    handleUserRatingFixtureEvents({ payload });
  }, [handleUserRatingFixtureEvents, payload]);

  const handleUserEvent = useCallback(
    (event) => {
      switch (event) {
        case USER_EVENTS.RatingFixtureEvent: {
          handleUserRatingFixtureEvent();
          break;
        }
      }
    },
    [handleUserRatingFixtureEvent],
  );

  const handleUserEventRef = useRef(handleUserEvent);
  handleUserEventRef.current = handleUserEvent;

  const previousPayloadRef = usePrevious(payload);

  useEffect(() => {
    if (
      prevEvent !== event ||
      prevStatus !== status ||
      !_isEqual(previousPayloadRef, payload)
    ) {
      // console.info('RITESH : Payload is', payload, event);
      handleUserEventRef.current(event);
    }
  }, [prevEvent, event, status, prevStatus, payload, previousPayloadRef]);

  return {};
};

export default useUserEventController;
