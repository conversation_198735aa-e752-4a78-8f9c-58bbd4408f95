import EventManager from '@/src/core/event';
import { events, listenersNamespace } from '@/src/core/event/constants';
import { useCallback, useEffect, useRef, useState } from 'react';

const useChallengeUserEventsTrigger = (handleChallengeUserEvents: any) => {
  const [payload, setPayload] = useState(EMPTY_OBJECT);
  const onEventTriggered = useCallback(
    (_payload: any) => {
      setPayload(_payload);
      handleChallengeUserEvents({ payload: _payload });
    },
    [handleChallengeUserEvents],
  );

  const onEventTriggeredRef = useRef(onEventTriggered);
  onEventTriggeredRef.current = onEventTriggered;

  useEffect(() => {
    const eventManager = new EventManager();

    const handleEvent = (_payload: any) => {
      onEventTriggeredRef?.current?.(_payload);
    };

    const subscriptionChallengeUserEvent = eventManager.on(
      events.ChallengeUserEvent,
      listenersNamespace.ChallengeUserEvent,
      handleEvent,
    );
    const subscriptionGameCanceledEvent = eventManager.on(
      events.GameCanceledEvent,
      listenersNamespace.GameCanceledEvent,
      handleEvent,
    );

    const subscriptionChallengeForPuzzleGameEvent = eventManager.on(
      events.ChallengeForPuzzleGameEvent,
      listenersNamespace.ChallengeForPuzzleGameEvent,
      handleEvent,
    );

    return () => {
      subscriptionChallengeUserEvent.unsubscribe();
      subscriptionGameCanceledEvent.unsubscribe();
      subscriptionChallengeForPuzzleGameEvent.unsubscribe();
    };
  }, []);

  return payload;
};

export default useChallengeUserEventsTrigger;
