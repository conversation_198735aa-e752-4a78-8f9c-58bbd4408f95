import { useEffect, useRef, useState } from 'react';

const useUserShowdownNotificationEvent = () => {
  const timeoutRef = useRef(null);
  const [showShowdownOverlay, setshowShowdownOverlay] = useState(false);

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setshowShowdownOverlay(false);
    }, 5000);
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [showShowdownOverlay]);

  const handleShowdownOverlay = () => {
    setshowShowdownOverlay(true);
  };

  return {
    handleShowdownOverlay,
    showShowdownOverlay,
  };
};

export default useUserShowdownNotificationEvent;
