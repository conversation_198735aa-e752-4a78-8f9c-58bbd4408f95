import React from 'react';
import RematchRequestOverlay from 'shared/RematchRequestOverlay'
import BadgeEarnedOverlay from "modules/profile/components/BadgeEarnedOverlay";
import ShowdownOverlay from 'shared/ShowdownOverlay';
import ChallengeUserOverlay from 'shared/ChallengeUserOverlay';
import StreakMaintainedOverlay from 'shared/StreakMaintainedOverlay';
import { usePathname } from 'expo-router';
import { useMemo } from 'react';

const UserEventOverlays = () => {
    const pathName = usePathname();

    return (
        <>
            <RematchRequestOverlay />
            <BadgeEarnedOverlay />
            <ChallengeUserOverlay />
            {/* <ShowdownOverlay {...showdownOverlayProps} /> */}
            <StreakMaintainedOverlay />
        </>
    );
}

export default React.memo(UserEventOverlays);
