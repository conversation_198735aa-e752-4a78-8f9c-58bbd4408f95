import { Platform } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { createJSONStorage } from "zustand/middleware";
import type { StateStorage } from "zustand/middleware";

export const clearStore = (setState: (state: any) => void, defaultConfig: any = EMPTY_OBJECT) => {
    setState(defaultConfig);
}

export const zustandStorage = createJSONStorage((): StateStorage => {
  if (Platform.OS === "web") {
    return {
      getItem: async (key) => {
        const value = localStorage.getItem(key);
        return value ?? null;
      },
      setItem: async (key, value) => {
        localStorage.setItem(key, value);
      },
      removeItem: async (key) => {
        localStorage.removeItem(key);
      },
    };
  }

  // For (Android & iOS)
  return {
    getItem: async (key) => {
      const value = await AsyncStorage.getItem(key);
      return value ?? null;
    },
    setItem: async (key, value) => {
      await AsyncStorage.setItem(key, value);
    },
    removeItem: async (key) => {
      await AsyncStorage.removeItem(key);
    },
  };
});