export type FeedState = {
  feeds: any[];
  lastId: string | null;
  hasMore: boolean;
  loading: boolean;
  isInAppToastShown: boolean;
  inAppMessageQueue: any[];
  error: Error | null;
  isRead: boolean;
  fetchFeeds: () => Promise<void>;
  updateFeedLikeStatus: (feedId: string) => Promise<void>;
  updateLastReadFeedId: (feedId: string) => Promise<void>;
  onInAppMessage: (message: any) => void;
  emptyInAppMessageQueue: () => void;
  showInAppToast: () => void;
  clearStore: () => void;
};

export type FeedStore = (state: FeedState) => any;

export type GetFeedStore = (state?: FeedState) => FeedState;
export type SetFeedStore = (
  update: FeedState | ((state: FeedState) => void)
) => void;

