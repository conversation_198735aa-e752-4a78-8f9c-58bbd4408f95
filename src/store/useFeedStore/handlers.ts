import { handleAsync } from '@/src/core/utils/asyncUtils';
import { GetFeedStore, SetFeedStore } from './types';
import { FeedHelper, FeedHelperInterface } from './helper';
import _get from 'lodash/get';
import _reduce from 'lodash/reduce';
import FeedApi, { FeedApiInterface } from './api';

interface HandlersInterface {
  fetchFeeds: () => Promise<void>;
  updateFeedLikeStatus: (
    feedId: string,
  ) => Promise<void>;
  onInAppMessage: (message: any) => void;
  emptyInAppMessageQueue: () => void;
  showInAppToast: () => void;
}

export class FeedHandlers implements HandlersInterface {
  private set: SetFeedStore;
  private get: GetFeedStore;
  private api: FeedApiInterface;
  private helper: FeedHelperInterface;

  constructor(set: SetFeedStore, get: GetFeedStore) {
    this.set = set;
    this.get = get;
    this.api = new FeedApi();
    this.helper = new FeedHelper();
  }

  /*
   * Fetches notifications from the API and updates the state.
   */
  fetchFeeds = async () => {
    const { lastId, hasMore, loading } = this.get();
    if (!hasMore || loading) {
      return;
    }
    // set loading state
    this.set((state) => {
      state.loading = true;
      state.error = null;
    });
    // api call
    const [data, error]: [any, any] = await handleAsync(
      this.api.fetchFeeds,
      lastId,
      50,
    );
    // error handling
    if (error) {
      this.set((state) => {
        state.loading = false;
        state.error = error;
      });
      return;
    }
    const userDetails = _get(data, 'userDetails', []);
    const userMap = _reduce(
      userDetails,
      (acc: any, user: any) => {
        acc[user.id] = user;
        return acc;
      },
      {},
    );
    const feeds = _get(data, 'feeds', []);
    for (const feed of feeds) {
      const additionalData = _get(data, ['feedData', 'additionalInfo'], EMPTY_OBJECT);
      const sentByUser = _get(additionalData, ['connectionRequest', 'sentBy'], '');
      if (sentByUser) {
        feed.userDetails = userMap[sentByUser];
      }
    }

    const isRead = _get(data, 'isRead', false);

    this.set((state) => {
      state.feeds = [...state.feeds, ...feeds];
      state.lastId = data?.lastId;
      state.hasMore = data?.hasMore;
      state.loading = false;
      state.error = error;
      state.isRead = isRead;
    });
  };

  /*
   * Updates the like status of a notification in the API and updates the state.
   */
  updateFeedLikeStatus = async (feedId: string) => {
    this.set((state) => {
      state.error = null;
      state.feeds = this.helper.changeFeedLikeStatus(state.feeds, feedId);
    });
    const [wasLikeSuccesful, error]: [any, any] = await handleAsync(
      this.api.updateLikeStatus,
      feedId,
    );

    // error handling
    if (error || !wasLikeSuccesful) {
      this.set((state) => {
        state.loading = false;
        state.error = error;
        state.feeds = this.helper.changeFeedLikeStatus(
          state.feeds,
          feedId,
        );
      });
      return;
    }

    // update state
    this.set((state) => {
      state.loading = false;
      state.error = null;
    });
  };

  updateLastReadFeedId = async () => {
    this.set((state) => {
      state.error = null;
    });
    const latestFeedId = _get(this.get(), ['feeds', 0, '_id'], null);
    const [wasUpdateSuccesful, error]: [any, any] = await handleAsync(
      this.api.updateLastReadFeedId,
      latestFeedId,
    );

    // error handling
    if (error || !wasUpdateSuccesful) {
      this.set((state) => {
        state.loading = false;
        state.error = error;
      });
      return;
    }

    // update state
    this.set((state) => {
      state.loading = false;
      state.isRead = true;
      state.error = null;
    });
  };

  showInAppToast = () => {
    this.set((state) => ({
      ...state,
      isInAppShown: true,
    }));
  };

  onInAppMessage = (message: any) => {
    this.set((state) => ({
      ...state,
      inAppMessageQueue: [...state.inAppMessageQueue, message],
    }));
  };

  emptyInAppMessageQueue = () => {
    this.set((state) => ({
      ...state,
      isInAppShown: false,
      inAppMessageQueue: [],
    }));
  };
}
