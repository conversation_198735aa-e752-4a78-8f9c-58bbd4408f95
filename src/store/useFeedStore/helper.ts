export interface FeedHelperInterface {
  changeFeedLikeStatus: (
    feeds: any[],
    feedId: string,
  ) => any[];
}

export class FeedHelper implements FeedHelperInterface {
  changeFeedLikeStatus = (
    feeds: any[],
    feedId: string,
  ) => {
    return feeds.map((feed) => {
      if (feed._id === feedId) {
        return {
          ...feed,
          isLiked: !feed.isLiked,
          feedData: {
            ...feed?.feedData,
            likesCount: Math.max(0, feed.isLiked ? feed?.feedData?.likesCount - 1 : feed?.feedData?.likesCount + 1),
          },
        };
      }
      return feed;
    });
  };
}
