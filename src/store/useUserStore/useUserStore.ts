import { create } from "zustand";
import { UserState } from "./types";
import { persist } from "zustand/middleware";
import { zustandStorage } from "../helpers";
import { UserHandlers } from "./handlers";
import { clearStore } from "../helpers";

const defaultConfig = {
  user: null,
  recentPresets: [],
  savedPresets: [],
  recentPresetsLoading: false,
  savedPresetsLoading: false,
  recentPresetsError: null,
  savedPresetsError: null,
  isOnline: true,
  isMatiksReachable: true,
  isNetworkReachable: true,
  isWasmReady: false,
  showToastValues: {
    visible: false,
    title: null,
    description: null,
    imageUrl: null,
    onAccept: null,
    onReject: null,
    onClose: null,
    timeout: 5000,
  },
}

const useZustandUserStore = create<UserState>()(persist(
  (set) => {
    const handlers = new UserHandlers(set);

    return {
      clearStore: () => clearStore(set, defaultConfig),
      ...defaultConfig,
      ...handlers,
    };
  },
  {
    name: "user-zustand-store",
    storage: zustandStorage,
    partialize: (state) => ({
      user: state.user,
      recentPresets: state.recentPresets,
      recentPresetsLoading: state.recentPresetsLoading,
      recentPresetsError: state.recentPresetsError,
      savedPresets: state.savedPresets,
      savedPresetsLoading: state.savedPresetsLoading,
      savedPresetsError: state.savedPresetsError,
    }),
  }
))
export default useZustandUserStore;
