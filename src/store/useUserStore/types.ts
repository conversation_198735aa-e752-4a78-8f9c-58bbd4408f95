type User = {
  _id: string;
  name: string;
  profileImageUrl: string;
  rating: number;
  ratingV2: RatingV2;
  statikCoins: number;
  hasFixedRating: boolean;
  countryCode: string;
  isGuest: boolean;
  bio: string;
  globalRank: number;
  countryRank: number;
  additional: Additional;
  userStreaks: UserStreaks;
  stats: Stats;
  badge: string;
  awardsAndAchievements: AwardsAndAchievements[];
  links: string[];
  showToastValues: ShowInAppToastValues;
};

type ShowInAppToastValues = {
  visible: boolean;
  title: string | null;
  description: string | null;
  imageUrl: string | null;
  onAccept: (() => void) | null;
  onReject: (() => void) | null;
  onClose: (() => void) | null;
  timeout: number;
}

type RatingV2 = {
  flashAnzanRating: number;
  globalRating: number;
  abilityDuelsRating: number;
  puzzleRating: number;
};

type Additional = {
  timeSpent: number;
};

type UserStreaks = {
  currentStreak: number;
  longestStreak: number;
  lastSevenDays: number[];
};

type Stats = {
  followersCount: number;
  followingsCount: number;
  friendsCount: number;
  ngp: number;
  hr: number;
};

type AwardsAndAchievements = {
  imageUrl: string;
  link: string;
  title: string;
  description: string;
};

type UserPresets = {
  _id: string;
  globalPresetId: string;
  userId: string;
  identifier: string;
  name: string;
  questionsSolved: number;
  curAvgTime: number;
  curAvgAccuracy: number;
  bestTime: number;
  bestStreak: number;
  numOfCorrectSubmissions: number;
  last10Time: number[];
  last10IncorrectAttempts: number[];
  saved: boolean;
  savedConfig: any;
}

type UserPresetsResponse = {
  userPresets: UserPresets[];
  totalCount: number;
}

type UserState = {
  user: User | null;
  recentPresets: UserPresets[];
  savedPresets: UserPresets[];
  recentPresetsLoading: boolean;
  savedPresetsLoading: boolean;
  recentPresetsError: Error | null;
  savedPresetsError: Error | null;
  isOnline: boolean;
  showToastValues: ShowInAppToastValues;
  isWasmReady: boolean;
  isMatiksReachable: boolean;
  isNetworkReachable: boolean;
  fetchUserRecentPresets: () => Promise<void>;
  fetchUserSavedPresets: () => Promise<void>;
  toggleMode: (mode: boolean) => void;
  clearStore: () => void;
  showInAppToast: (values: ShowInAppToastValues) => void;
  updateWasmReady: (status: boolean) => void;
}


export type UserStore = (state: UserState) => any;  

export type GetUserStore = (state?: UserState) => UserState;
export type SetUserStore = (
  update: UserState | ((state: UserState) => UserState)
) => void;

export type {
  User,
  RatingV2,
  Additional,
  UserStreaks,
  Stats,
  AwardsAndAchievements,
  UserPresets,
  UserPresetsResponse,
  UserState,
  ShowInAppToastValues,
};

