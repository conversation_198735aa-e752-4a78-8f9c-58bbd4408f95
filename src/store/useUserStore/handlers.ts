import { handleAsync } from "../../core/utils/asyncUtils";
import { User<PERSON><PERSON>, UserApiInterface } from "./api";
import { UserState, SetUserStore, ShowInAppToastValues } from "./types";
import _get from "lodash/get";

interface UserHandlersInterface {
  fetchUserRecentPresets: () => Promise<void>;
  fetchUserSavedPresets: () => Promise<void>;
  toggleMode: (mode: boolean) => void;
  showInAppToast: (values: ShowInAppToastValues) => void;
  updateWasmReady: (status: boolean) => void;
}

export class UserHandlers implements UserHandlersInterface {
  private api: UserApiInterface;
  private setState: SetUserStore;

  constructor(setState: SetUserStore) {
    this.api = new UserApi();
    this.setState = setState;
  }

  toggleMode: (mode: boolean) => void = (mode) => {
    this.setState((state: UserState) => ({ ...state, isOnline: mode }));
  }

  updateNetworkStatusOfInternet: (status: boolean) => void = (status) => {
    this.setState((state: UserState) => ({ ...state, isNetworkReachable: status }));
  }

  updateNetworkStatusOfMatiks: (status: boolean) => void = (status) => {
    this.setState((state: UserState) => ({ ...state, isMatiksReachable: status }));
  }

  fetchUserRecentPresets: () => Promise<void> = async () => {
    this.setState((state: UserState) => ({ ...state, recentPresetsLoading: true, recentPresetsError: null }));
    const [resp, error] : [any, Error | null] = await handleAsync(this.api.fetchUserRecentPresets);

    if (error || resp?.error) {
      this.setState((state: UserState) => ({ ...state, recentPresetsError: error ?? resp?.error, recentPresetsLoading: false  }));
      return;
    }

    const recentPresets = _get(resp, ['data','userPresets', 'userPresets'], []);
    this.setState((state: UserState) => ({ ...state, recentPresets, recentPresetsLoading: false }));
  }

  fetchUserSavedPresets: () => Promise<void> = async () => {
    this.setState((state: UserState) => ({ ...state, savedPresetsLoading: true, savedPresetsError: null }));
    const [resp, error] : [any, Error | null] = await handleAsync(this.api.fetchUserSavedPresets);
    
    if (error || resp?.error) {
      this.setState((state: UserState) => ({ ...state, savedPresetsError: error ?? resp?.error, savedPresetsLoading: false }));
      return;
    }

    const savedPresets = _get(resp, ['data','userPresets', 'userPresets'], []);
    this.setState((state: UserState) => ({ ...state, savedPresets, savedPresetsLoading: false }));
  }

  showInAppToast: (values: ShowInAppToastValues) => void = (values) => {
    this.setState((state: UserState) => ({ ...state, showToastValues: values }));
  }

  updateWasmReady: (status: boolean) => void = (status) => {
    this.setState((state: UserState) => ({ ...state, isWasmReady: status }));
  }
}   