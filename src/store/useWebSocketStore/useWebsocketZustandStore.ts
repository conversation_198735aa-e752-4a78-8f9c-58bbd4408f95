import { create } from 'zustand';
import { WebsocketHandlers } from './handlers';
import { WebsocketState } from './types';

const useWebsocketZustandStore = create<WebsocketState>((set, get) => {
  const handlers = new WebsocketHandlers(set as any, get as any);

  return {
    ws: null,
    token: '',
    isConnected: false,
    lastMessage: new Map<string, any>(),
    channels: new Set<string>(),
    cachedMessageQueue: [],
    reconnectCount: 0,
    reconnectTimeout: 500,
    pingPongTimeout: 5000,
    connect: handlers.connect.bind(handlers),
    disconnect: handlers.disconnect.bind(handlers),
    joinChannel: handlers.joinChannel.bind(handlers),
    leaveChannel: handlers.leaveChannel.bind(handlers),
    sendMessage: handlers.sendMessage.bind(handlers),
    cleanUp: handlers.cleanUp.bind(handlers),
    isConnecting: handlers.isConnecting.bind(handlers),
    updateLastMessage: handlers.updateLastMessage.bind(handlers),
    reconnect: handlers.reconnect.bind(handlers),
    close: handlers.close.bind(handlers),
  };
});

export default useWebsocketZustandStore;
