export type WebsocketState = {
  ws: WebSocket | null;
  token: string;
  isConnected: boolean;
  lastMessage: Record<string, any>;
  channels: Set<string>;
  cachedMessageQueue: any[];
  reconnectCount: number;
  reconnectTimeout: number;
  pingPongTimeout: number;
  connect: (token: string, onMessage?: (event: any) => void) => void;
  disconnect: () => void;
  joinChannel: (channel: string) => void;
  leaveChannel: (channel: string) => void;
  sendMessage: (message: any) => void;
  reconnect: () => void;
  isConnecting: () => boolean;
  cleanUp: () => void;
  close: () => void;
  updateLastMessage: (channel: string, message: any) => void;
}

export type SetWebsocketStore = (
  state: (state: WebsocketState) => WebsocketState,
) => void;
export type GetWebsocketStore = () => WebsocketState;

