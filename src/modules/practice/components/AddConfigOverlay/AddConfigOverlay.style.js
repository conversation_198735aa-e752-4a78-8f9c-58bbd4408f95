import { Dimensions, StyleSheet } from "react-native";
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from 'react'
import dark from "../../../../core/constants/themes/dark";

const createStyles = (isCompactMode) => StyleSheet.create({
    textInput: {
        justifyContent: "center",
        alignItems: "center",
        fontSize: 14,
        color: 'white',
        borderRadius: 12,
        fontFamily: 'Montserrat-400',
        outlineStyle: 'none',
        borderWidth: 1,
        height: 36,
        borderColor: 'white',
        paddingHorizontal: 8,
        marginBottom: 12,
        paddingVertical: 10,
        minWidth: 35
    },
    buttonContainer: {
        flexDirection: 'row',
        gap: 10,
        justifyContent: 'flex-end',
        marginTop: 0
    },
    overlayStyle: {
        backgroundColor: dark.colors.background,
        borderRadius: 12,
        width: Math.min(Dimensions.get("window").width - 25, 400),
        paddingHorizontal: 16,
        paddingVertical: 12,
        gap: 12,
        justifyContent: 'center',
        borderColor: dark.colors.tertiary,
        borderWidth: 1
    }
})


const useAddConfigOverlayStyles = () => {
    const { isMobile } = useMediaQuery();

    const styles = useMemo(() => createStyles(isMobile), [isMobile]);

    return styles;
};

export default useAddConfigOverlayStyles