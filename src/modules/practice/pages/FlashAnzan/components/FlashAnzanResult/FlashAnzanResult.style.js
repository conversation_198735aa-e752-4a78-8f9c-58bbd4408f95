import { StyleSheet } from "react-native";
import useMediaQuery from 'core/hooks/useMediaQuery'
import { useMemo } from 'react'
import dark from "core/constants/themes/dark";
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout'

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: 1,
        height: '100%',
        width: "100%",
        alignItems: "center"
    },
    timeTakenText: {
        fontSize: isCompactMode ? 20 : 16,
        color: 'white',
        fontFamily: 'Montserrat-700',
        lineHeight: 24
    },
    timeText: {
        fontFamily: "Montserrat-600",
        fontSize: 14,
        lineHeight: 17,
        color: dark.colors.textDark
    },
    timeTakenWithQueButtonContainer: {
        width: "92%",
        borderColor: dark.colors.tertiary,
        borderWidth: isCompactMode ? 1 : 0,
        paddingHorizontal: isCompactMode ? 16 : 0,
        paddingVertical: 16,
        borderRadius: isCompactMode ? 12 : 0,
        flexDirection: "row",
        alignItems: "center",
        marginHorizontal: 16,
        marginBottom: isCompactMode ? 15 : 0,
        justifyContent: "space-between"
    },
    innerContainer: {
        flex: 1,
        flexDirection: "row",
        marginVertical: isCompactMode ? 0 : 50,
        height: "auto",
        width: isCompactMode ? "100%" : "80%",
        borderColor: dark.colors.tertiary,
        borderWidth: isCompactMode ? 0 : 1,
        borderRadius: 20,
        overflow: "hidden"
    },
    queContainer: {
        flex: 2,
        paddingHorizontal: 16,
        paddingVertical: isCompactMode ? 0 : 16,
        borderRadius: 20,
        alignItems:"center",
        justifyContent:"space-around",
    },
    analyticsContainer: {
        flex: 1,
        borderRightColor: dark.colors.tertiary,
        borderRightWidth: isCompactMode ? 0 : 1,
    },
    timeByOperationTypeStats: {
        marginTop: 16,
        flexDirection: 'row',
        gap: 8,
        marginBottom: 15
    },
    statsBox: {
        minWidth: 155,
        height: 61,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: dark.colors.tertiary,
        padding: 12,
        gap: 8
        // justifyContent:"center",
        // alignItems:"center"
    },
    statsValue: {
        fontFamily: 'Montserrat-700',
        lineHeight: 17,
        fontSize: 14,
        color: "white"
    },
    statsLabel: {
        fontFamily: 'Montserrat-700',
        color: dark.colors.textDark,
        fontSize: 10,
        lineHeight: 12
    },
    rowText: {
        flex: 1,
        color: 'white',
        fontFamily: 'Montserrat-500',
        fontSize: 14,
        lineHeight: 20
    },
    headerRowText: {
        flex: 1,
        fontSize: 10,
        color: dark.colors.textDark,
        fontFamily: "Montserrat-500",
        lineHeight: 20
    },
    viewText: {
        flex: 1,
        color: dark.colors.secondary,
        fontFamily: 'Montserrat-600',
        fontSize: 13,
        lineHeight: 20
    },
    buttonStyle: {
        height: 40,
        backgroundColor: dark.colors.secondary,
        borderRadius: 24,
        width: isCompactMode ? '90%' : 240,
        marginHorizontal: 16,
        justifyContent: "center",
        alignItems: "center",
        marginBottom: isCompactMode ? 15 : 0,
    },
    buttonLabelStyle: {
        fontSize: 14,
        fontFamily: 'Montserrat-600'
    },

    goBackLabelStyle: {
        color: dark.colors.secondary,
        fontSize: 14,
        fontFamily: 'Montserrat-600'
    },

    tabBar: {
        width: '100%',
        backgroundColor: 'transparent',
    },
    indicator: {
        backgroundColor: dark.colors.background,
    },
    tabStyle: {
        width: 100,
    },
    label: {
        fontFamily: 'Montserrat-500',
        fontSize: 14,
        width: 90,
        textAlign: "center"
    },
    fullWidthLine: {
        position: 'absolute',
        top: 38,
        height: 2,
        width: '100%',
        maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
        backgroundColor: dark.colors.tertiary,
    },

    goBackAndPracticeAgainRow: {
        width: "100%",
        height: 'auto',
        backgroundColor: dark.colors.background,
        borderTopColor: dark.colors.tertiary,
        borderTopWidth: 1,
        paddingVertical: 10,
        position: "absolute",
        bottom: 0,
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: "space-between",
        alignItems: "center"
    },

    configTagsContainer:{ 
        flexWrap: 'wrap', 
        flexDirection: 'row', 
        marginTop: 8, 
        gap: 8 
    },

    statsR1:{
        flexDirection: 'row', 
        gap: 13, 
        marginTop: 8
    },

    sessionStatsRow :{
        color: dark.colors.textDark, 
        fontSize: 10, 
        fontFamily: "Montserrat-600", 
        lineHeight: 14, 
        marginTop: 16 
    },
    savePresetContainer: {
        width: "100%",
        backgroundColor: dark.colors.primary,
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 8,
        marginTop:30,
        justifyContent: 'space-between',
        flexDirection: 'row'
    },
    savePresetText :{ 
        fontFamily: "Montserrat-600", 
        color: "white", 
        fontSize: 12, 
        lineHeight: 20 
    },
    yesSaveText:{ 
        fontFamily: "Montserrat-600", 
        color: dark.colors.secondary, 
        fontSize: 11, 
        lineHeight: 20 
    },
})

const useFlashAnzanResultStyles = () => {
    const { isMobile } = useMediaQuery();

    const styles = useMemo(() => createStyles(isMobile), [isMobile]);

    return styles;
};

export default useFlashAnzanResultStyles