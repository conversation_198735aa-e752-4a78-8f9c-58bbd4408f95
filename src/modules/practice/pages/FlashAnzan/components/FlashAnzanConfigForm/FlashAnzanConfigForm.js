import React, { useCallback, useState } from "react";
import CheckBoxInput from "../../../../components/CheckboxInput/CheckBoxInput"
import SelectDirectionOfInput from "../../../../components/DirectionOfInputField"
import NumberInput from "../../../../components/NumberInput/NumberInput"
import { View, Text, ScrollView } from "react-native"
import PrimaryButton from "atoms/PrimaryButton"
import useFlashAnzanConfigurationFormStyles from "./FlashAnzanConfigForm.style";
import { useRouter } from "expo-router";
import { usePracticeContext } from "../../../../../../../app/_layout";
import { FLASH_ANZAN_DEFAULT_CONFIG } from "../../../../constants/presetConfig";

const FlashAnzanConfigForm = (props) => {

    const styles = useFlashAnzanConfigurationFormStyles()

    const [selectedConfig, setSelectedConfig] = useState(FLASH_ANZAN_DEFAULT_CONFIG)

    const router = useRouter()

    const { setPracticeConfig } = usePracticeContext()

    const { digits, numberCount, includeSubstraction, typingDirection, noOfQuestions, flashSpeed } = selectedConfig

    const updateConfig = useCallback((key, value) => {
        setSelectedConfig((prev) => ({ ...prev, [key]: value }))
    }, [setSelectedConfig]);

    const onPracticePressed = useCallback(() => {
        setPracticeConfig({ selectedConfig })
        router.push('/nets/flash-anzan')
    }, [setPracticeConfig, selectedConfig])

    return (
        <View style={styles.container}>
            <ScrollView contentContainerStyle={styles.innerContainer} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
                <Text style={styles.headerText}>
                    Flash Anzan
                </Text>
                <NumberInput
                    label="Number of digits"
                    value={digits}
                    onValueChange={(value) => updateConfig('digits', value)}
                    minValue={1}
                    maxValue={10}
                />
                <NumberInput
                    label="Number Count"
                    value={numberCount}
                    minValue={2}
                    onValueChange={(value) => updateConfig('numberCount', value)}
                    maxValue={1000}
                />
                <CheckBoxInput
                    label="Include subtraction"
                    value={includeSubstraction}
                    onValueChange={(value) => updateConfig('includeSubstraction', value)}
                />
                <SelectDirectionOfInput
                    onInputDirectionChange={(value) => updateConfig('typingDirection', value)}
                    selectedDirection={typingDirection} />
                <NumberInput
                    label="Number of Questions"
                    value={noOfQuestions}
                    minValue={2}
                    onValueChange={(value) => updateConfig('noOfQuestions', value)}
                    maxValue={1000}
                />

                <NumberInput
                    label="Flash Speed(in ms)"
                    value={flashSpeed}
                    minValue={200}
                    onValueChange={(value) => updateConfig('flashSpeed', value)}
                    maxValue={5000}
                    incrementBy={100}
                />

                <View style={{ width: "100%", alignItems: "flex-end", marginVertical: 15, marginTop: 50 }}>
                    <PrimaryButton
                        label={'Practice '}
                        radius={'xl'}
                        buttonStyle={{ height: 35, justifyContent: "center", padding: 0, width: 130 }}
                        labelStyle={{ fontSize: 14, fontFamily: 'Montserrat-600' }}
                        onPress={onPracticePressed} />
                </View>
            </ScrollView>
        </View>
    )
}

export default React.memo(FlashAnzanConfigForm)