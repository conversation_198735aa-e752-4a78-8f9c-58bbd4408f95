import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import _reduce from 'lodash/reduce';
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _toString from 'lodash/toString';
import _size from 'lodash/size';
import ACTIVITY_TYPES from '@/src/core/constants/activityTypes';
import { generateNumbers } from 'modules/game/hooks/useHandleFlashAnzanPlay';
import { getIdentifierStringFromConfig } from '../utils/getIdentifierStringFromConfig';
import { PRACTICE_CATEGORIES } from '../pages/Practice/components/OperatorSelector/constants/practice';

import useGetUserSavedPresets from './queries/useGetSavedPresetQuery';
import useGetRecentPresetsQuery from './queries/useGetRecentPresetsQuery';
import useSubmitUserPresetResultMutation from './mutations/useSubmitUserPresetResultMutation';
import useUserActivityTracker from '../../../core/hooks/useUserActivityTracker';

const useFlashAnzanPlayController = ({ config }) => {
  const {
    digits,
    numberCount,
    includeSubstraction,
    noOfQuestions,
    flashSpeed,
  } = config;

  const [isGameCompleted, setIsGameCompleted] = useState(false);
  const [isStartingCountdownCompleted, setIsStartingCountdownCompleted] =
    useState(false);
  const [startCountdown, setStartCountdown] = useState(3);
  const [isCorrect, setIsCorrect] = useState(false);

  const [timer, setGameTimer] = useState(0);

  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [numbers, setNumbers] = useState([]);
  const [currentNumberIndex, setCurrentNumberIndex] = useState(-1);
  const [userAnswer, setUserAnswer] = useState('');
  const [countdown, setCountdown] = useState(5);
  const [isFlashing, setIsFlashing] = useState(false);
  const [isInputWrong, setIsInputWrong] = useState(false);
  const [gameResults, setGameResults] = useState([]);
  const [incorrectAttempts, setIncorrectAttempts] = useState(0);
  const [flashTrigger, setFlashTrigger] = useState(false);

  const { reFetchUserRecentPresets } = useGetRecentPresetsQuery();
  const { submitUserPresetResultMutation } =
    useSubmitUserPresetResultMutation();
  const { updateActivity } = useUserActivityTracker();

  const { loading, error, userPresets } = useGetUserSavedPresets({
    page: 1,
    pageSize: 100,
  });

  const savedPresetsIdentifier = useMemo(
    () =>
      _map(userPresets, (preset, index) => ({
        identifier: _toString(preset.identifier),
        name: _toString(preset.name),
      })),
    [userPresets],
  );

  const identifierString = useMemo(
    () =>
      getIdentifierStringFromConfig({
        config,
        tag: PRACTICE_CATEGORIES.FLASH_ANZAN,
      }),
    [config],
  );

  const isSaved = useMemo(
    () =>
      _filter(
        savedPresetsIdentifier,
        (preset) => preset.identifier === identifierString,
      ).length > 0,
    [savedPresetsIdentifier, identifierString],
  );

  const startNextQuestion = useCallback(() => {
    const config = { digits, numberCount, includeSubstraction };
    const newNumbers = generateNumbers({ config });
    setNumbers(newNumbers);
    setCurrentNumberIndex(0);
    setIsFlashing(true);
    setUserAnswer('');
    setIncorrectAttempts(0);
    setIsCorrect(false);
    setIsInputWrong(false);
    setFlashTrigger(false);
  }, [digits, numberCount, includeSubstraction]);

  const bestStreak = useMemo(() => {
    let bestCount = 0;
    let currentStreak = 0;

    for (let i = 0; i < gameResults.length; i++) {
      if (gameResults[i].isCorrect) {
        currentStreak++;
        bestCount = Math.max(bestCount, currentStreak);
      } else {
        currentStreak = 0;
      }
    }

    return bestCount;
  }, [gameResults]);

  const getUserPresetResultArrayToSave = useCallback(() => {
    if (_isEmpty(gameResults)) {
      return [];
    }

    const correctAnswers = _filter(
      gameResults,
      (result) => result?.isCorrect,
    ).length;

    return [
      {
        date: new Date(),
        identifier: identifierString,
        numOfQuestions: noOfQuestions,
        bestStreak,
        numOfCorrectSubmissions: correctAnswers,
        savedConfig: JSON.stringify(config),
      },
    ];
  }, [identifierString, gameResults, config, noOfQuestions, bestStreak]);

  const saveUserPresetResult = useCallback(async () => {
    try {
      const userPresetResultInput = getUserPresetResultArrayToSave();
      for (let i = 0; i < _size(userPresetResultInput); i++) {
        updateActivity({
          activityType: ACTIVITY_TYPES.PRACTICE_NETS,
          duration:
            flashSpeed * noOfQuestions * numberCount + noOfQuestions * 5 * 1000,
        });
      }
      await submitUserPresetResultMutation({ userPresetResultInput });
      reFetchUserRecentPresets();
    } catch (e) {
      console.info(e);
    }
  }, [
    getUserPresetResultArrayToSave,
    submitUserPresetResultMutation,
    updateActivity,
    flashSpeed,
    noOfQuestions,
    numberCount,
  ]);

  useEffect(() => {
    let countdownInterval;
    if (!isStartingCountdownCompleted) {
      if (startCountdown > 0) {
        countdownInterval = setInterval(() => {
          setStartCountdown((prev) => prev - 1);
        }, 1000);
      } else {
        setIsStartingCountdownCompleted(true);
        setIsFlashing(true);
      }
    }

    return () => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
    };
  }, [startCountdown, isStartingCountdownCompleted]);

  useEffect(() => {
    if (isStartingCountdownCompleted && currentQuestion <= noOfQuestions) {
      startNextQuestion();
    } else if (currentQuestion > noOfQuestions) {
      setIsGameCompleted(true);
    }
  }, [
    currentQuestion,
    noOfQuestions,
    startNextQuestion,
    isStartingCountdownCompleted,
  ]);

  const saveUserPresetResultRef = useRef(saveUserPresetResult);
  saveUserPresetResultRef.current = saveUserPresetResult;

  useEffect(() => {
    if (isGameCompleted) {
      saveUserPresetResultRef.current();
    }
  }, [isGameCompleted]);

  useEffect(() => {
    if (isFlashing && currentNumberIndex < numberCount) {
      setFlashTrigger(true);
      const flashResetTimer = setTimeout(() => {
        setFlashTrigger(false);
      }, flashSpeed / 2);

      const numberTimer = setTimeout(() => {
        setCurrentNumberIndex((prev) => prev + 1);
      }, flashSpeed);

      return () => {
        clearTimeout(flashResetTimer);
        clearTimeout(numberTimer);
      };
    }
    if (isFlashing && currentNumberIndex >= numberCount) {
      setIsFlashing(false);
      setCountdown(5);
    }
  }, [currentNumberIndex, isFlashing, numberCount, flashSpeed]);

  useEffect(() => {
    if (!isFlashing && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
    if (!isFlashing && countdown === 0) {
      checkAnswer();
    }
  }, [countdown, isFlashing]);

  const checkAnswer = useCallback(() => {
    const correctAnswer = _reduce(numbers, (acc, num) => acc + num, 0);
    const isCorrect = Number(userAnswer) === correctAnswer;

    const result = {
      questionNumber: currentQuestion,
      numbers,
      userAnswer: Number(userAnswer),
      correctAnswer,
      isCorrect,
      incorrectAttempts,
    };

    setGameResults((prev) => [...prev, result]);
    setCurrentQuestion((prev) => prev + 1);
    setIsInputWrong(!isCorrect);

    if (!isCorrect) {
      setIncorrectAttempts((prev) => prev + 1);
    }
  }, [numbers, userAnswer, currentQuestion]);

  const handleInputChange = useCallback(
    (text) => {
      setUserAnswer(text);
      const correctAnswer = _reduce(numbers, (acc, num) => acc + num, 0);
      if (_toString(text).length !== `${correctAnswer}`.length) {
        return;
      }
      const isCorrect = Number(text) === correctAnswer;
      setIsCorrect(isCorrect);
      setIsInputWrong(!isCorrect);
    },
    [numbers],
  );

  return {
    isGameCompleted,
    isCorrect,
    isFlashing,
    isInputWrong,
    userAnswer,
    currentQuestion,
    currentNumberIndex,
    numbers,
    countdown,
    gameResults,
    identifierString,
    startCountdown,
    isStartingCountdownCompleted,
    flashTrigger,
    isSaved,
    bestStreak,
    setUserAnswer: handleInputChange,
  };
};

export default useFlashAnzanPlayController;
