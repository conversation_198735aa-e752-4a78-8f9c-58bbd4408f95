import React, { useCallback, useRef, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import _map from 'lodash/map';

import StreakIcon from '@/assets/images/icons/Streak.png';
import { useRouter } from 'expo-router';
import { Icon } from '@rneui/themed';
import { Square } from 'tamagui';
import styles from './StreakTracker.style';
import useStreakAnalytics from '../../../profile/hooks/query/useStreakAnalytics';
import dark from '../../../../core/constants/themes/dark';
import HomeBannerCarouselContainer from '../HomeBannerCarusel/HomeBannerCarouselContainer';

const StreakTracker = () => {
  const { lastFiveDaysStreakStatus, lastFiveWeekDays } = useStreakAnalytics();
  const router = useRouter();
  const todayIndex = 4;

  const todayContainerRef = useRef();
  const [todayContainerStyle, setTodayContainerStyle] = useState({});

  const onCardLayoutChange = useCallback(() => {
    todayContainerRef.current?.measure((x, y, width, height, pageX, pageY) => {
      setTodayContainerStyle({ left: pageX });
    });
  }, [todayContainerRef]);

  const onPressOneMinDuel = useCallback(() => {
    router.push('/search?timeLimit=1');
  }, [router]);

  if (lastFiveDaysStreakStatus[todayIndex]) {
    return (
      <View style={{ width: '100%', gap: 16 }}>
        <HomeBannerCarouselContainer />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.streakRow}>
        {_map(lastFiveWeekDays, (weekText, index) => (
          <View style={{ alignItems: 'center' }}>
            <View
              key={index}
              style={[
                styles.streakItem,
                index === todayIndex && styles.todayHighlight,
              ]}
              ref={index === todayIndex ? todayContainerRef : null}
              onLayout={onCardLayoutChange}
            >
              {lastFiveDaysStreakStatus[index] ? (
                <Image source={StreakIcon} style={styles.icon} />
              ) : (
                <Image
                  source={require('@/assets/images/active_bolt.png')}
                  style={[styles.icon, { opacity: 0.4, height: 15, width: 15 }]}
                />
              )}
            </View>
            <Text style={styles.streakText}>{weekText}</Text>
          </View>
        ))}
      </View>
      <Square
        enterStyle={{
          scale: 1.1,
          y: -10,
          opacity: 0,
        }}
        animation="bouncy"
        elevation="$4"
        opacity={1}
        scale={1}
        y={0}
        height={40}
        justifyContent="center"
      >
        <TouchableOpacity
          style={styles.messageContainer}
          onPress={onPressOneMinDuel}
        >
          <View style={[styles.outerTriangle, todayContainerStyle]} />
          <View style={styles.outerMessageBox}>
            <View style={[styles.triangle, todayContainerStyle]} />
            <View style={styles.messageBox}>
              <Text style={styles.messageText}>
                Earn your streak in 1 min with a quick duel!
              </Text>
              <View style={styles.arrowContainer}>
                <Icon
                  name="chevron-right"
                  type="font-awesome-5"
                  color={dark.colors.secondary}
                  size={12}
                />
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Square>
      <View style={{ marginTop: 16 }}>
        <HomeBannerCarouselContainer />
      </View>
    </View>
  );
};

export default React.memo(StreakTracker);
