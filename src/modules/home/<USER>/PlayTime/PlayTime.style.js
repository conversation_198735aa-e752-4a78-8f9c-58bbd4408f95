import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  superContainer: {
    flex: 1,
    alignItems: 'center',
  },
  container: {
    justifyContent: 'start',
    alignItems: 'center',
    width: '100%',
  },
  gameTypeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomContainer: {
    width: '100%',
    position: 'absolute',
    bottom: 0,
    alignItems: 'center',
    paddingBottom: 40,
  },
  gameConfigContainer: {
    flex: 1,
    paddingHorizontal: 0,
    maxWidth: 400,
    marginBottom: 80,
  },

  playButton: {
    maxWidth: 400,
    width: '100%',
    paddingHorizontal: 20,
  },
  inviteFriendButton: {
    flex: 1,
    minWidth: 200,
    backgroundColor: dark.colors.secondary,
    maxWidth: 400,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
  },
  buttonText: {
    fontSize: 14,
    fontFamily: 'Montserrat-800',
    lineHeight: 17,
    color: dark.colors.card,
  },
  howtoplaylabel: {
    color: dark.colors.textLight,
  },
  howtoplaybutton: {
    backgroundColor: dark.colors.primary,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255, 0.2)',
    height: 42,
    width: 140,
  },
  howtoplaybackground: {
    backgroundColor: dark.colors.textLight,
    opacity: 0.2,
  },
  howtoPlayText: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default styles;
