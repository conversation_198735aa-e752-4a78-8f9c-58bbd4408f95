import { View, Text, Image } from "react-native"
import styles from './PlayConfigCard.style.js'
import dark from "../../../../core/constants/themes/dark.js"
import groupIcon from 'assets/images/group.png'
import React, { useCallback } from "react"
import PropTypes from "prop-types"
import { TouchableOpacity } from "react-native"
import { GAME_TYPE_DETAILS, GAME_TYPES } from "../../constants/gameTypes.js"
import Analytics from "../../../../core/analytics/index.js"

import { PAGE_NAMES } from "../../../../core/constants/pageNames.js"
import { ANALYTICS_EVENTS } from "../../../../core/analytics/const.js"
import { icons } from "../../constants/gameTypeImages.js"

const PlayConfigCard = (props) => {

    const { isSelected, onPress, gameType } = props

    const { icon, title, subtitle } = GAME_TYPE_DETAILS[gameType]

    const onPressGameType = useCallback(() => {
        let eventName = ''
        switch (gameType) {
            case GAME_TYPES.PLAY_ONLINE:
                eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_ONLINE_DUELS
                break
            case GAME_TYPES.PLAY_WITH_FRIEND:
                eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_FRIEND
                break
            case GAME_TYPES.PRACTICE:
                eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_PRACTICE
                break
        }
        Analytics.track(eventName, {
            pageName: PAGE_NAMES.ARENA_PAGE
        });
        onPress?.({ gameType })
    }, [gameType, onPress, title]);

    return (
        <TouchableOpacity style={[styles.container, isSelected && { borderColor: 'white', borderWidth: 1 }]} onPress={onPressGameType}>
            <View style={{ backgroundColor: '#323232', height: 40, width: 40, borderRadius: 12, justifyContent: "center", alignItems: "center" }}>
                <Image source={icons[icon]} style={{ height: 20, width: 20 }} />
            </View>
            <View style={{ gap: 4 }}>
                <Text style={styles.titleText}>
                    {title}
                </Text>
                <Text style={styles.infoText} numberOfLines={2}>
                    {subtitle}
                </Text>
            </View>
        </TouchableOpacity>
    )
}

PlayConfigCard.propTypes = {
    isSelected: PropTypes.bool,
    onPress: PropTypes.func,
    gameType: PropTypes.string
}

export default React.memo(PlayConfigCard)