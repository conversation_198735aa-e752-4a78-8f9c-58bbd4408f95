import React from 'react';
import { Platform, Text, View } from 'react-native';
import LinearGradientText from 'atoms/LinearGradientText';
import dark from 'core/constants/themes/dark';
import { Square } from 'tamagui';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from '@/src/core/readers/userReader';
import useMediaQuery from 'core/hooks/useMediaQuery';
import styles from './UnlockGameTypeOverlayCard.style';

const UnlockGameTypeOverlayCard = ({
  showUnlockMessage,
  isFirstLockedCard = false,
}: {
  showUnlockMessage: boolean;
  isFirstLockedCard?: boolean;
}) => {
  const { user } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();
  const gamesPlayed = userReader.gamesPlayed(user) || 0;
  const gamesNeeded = Math.max(1, 3 - gamesPlayed);
  const unlockMessage = `${gamesPlayed}/3 PLAYED, ${gamesNeeded} MORE ${gamesNeeded === 1 ? 'GAME' : 'GAMES'} AND YOU`;
  const canUnlockGameModeText = 'CAN UNLOCK OTHER GAME MODES';

  if (!showUnlockMessage || !isFirstLockedCard) {
    return null;
  }

  return (
    <View
      style={[styles.container, !isCompactMode && styles.expandedContainer]}
    >
      <Square
        enterStyle={{
          scale: 1.1,
          y: -10,
          opacity: 0,
        }}
        animation="bouncy"
        elevation="$4"
        opacity={1}
        scale={1}
        y={0}
        height={40}
        justifyContent="center"
      >
        <View style={styles.triangle} />
        <View style={[styles.messageBox, Platform.OS === 'web' && { gap: 3 }]}>
          <LinearGradientText
            colors={dark.colors.earnStreakText}
            textStyle={styles.unlockText}
          >
            {Platform.OS !== 'web' ? (
              <Text style={styles.unlockText}>{unlockMessage}</Text>
            ) : (
              unlockMessage
            )}
          </LinearGradientText>
          <LinearGradientText
            colors={dark.colors.earnStreakText}
            textStyle={styles.unlockText}
          >
            {Platform.OS !== 'web' ? (
              <Text style={styles.unlockText}>{canUnlockGameModeText}</Text>
            ) : (
              canUnlockGameModeText
            )}
          </LinearGradientText>
        </View>
      </Square>
    </View>
  );
};

export default React.memo(UnlockGameTypeOverlayCard);
