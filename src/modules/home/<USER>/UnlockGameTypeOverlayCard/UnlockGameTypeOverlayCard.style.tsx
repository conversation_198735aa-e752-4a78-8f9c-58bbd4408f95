import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 90,
    left: 200,
    transform: [{ translateX: -130 }],
    zIndex: 30,
    paddingTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'none',
  },
  expandedContainer: {
    top: 195,
    left: 155,
  },
  triangle: {
    position: 'absolute',
    top: -20,
    width: 0,
    height: 0,
    alignSelf: 'center',
    zIndex: 200,
    borderLeftWidth: 20,
    borderRightWidth: 20,
    borderTopWidth: 20,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: dark.colors.darkBg,
    transform: [{ rotate: '180deg' }],
  },
  messageBox: {
    backgroundColor: dark.colors.darkBg,
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: 240,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 3,
    elevation: 5,
  },
  messageText: {
    color: dark.colors.background,
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    flex: 1,
    marginRight: 10,
  },
  unlockText: {
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    textAlign: 'center',
  },
});

export default styles;
