import React, { useCallback } from 'react'
import { Text, View } from 'react-native'
import styles from './GameConfigsOverlay.style'
import dark from '../../../../core/constants/themes/dark'
import { closePopover } from 'molecules/Popover/Popover'
import PropTypes from 'prop-types'
import PrimaryButton from 'atoms/PrimaryButton'
import SecondaryButton from 'atoms/SecondaryButton'
import _map from 'lodash/map'
import useGameConfig from '../../../home/<USER>/useGameConfig'
import {
    GAME_CONFIGS,
    ABILITY_GAME_CONFIG,
} from '../../../home/<USER>/gameConfig'
import useMediaQuery from '../../../../core/hooks/useMediaQuery'
import { GAME_TYPE_DETAILS, GAME_TYPES } from '../../../home/<USER>/gameTypes'
import useCreateGameAction from '../../hooks/useCreateGameAction'

const GameConfigsOverlay = (props) => {
    const { gameType } = props

    const { ctaLabel, icon, title } = GAME_TYPE_DETAILS[gameType]

    const { gameConfig: selectedGameConfig, updateGameConfig } =
        useGameConfig(gameType)

    const { isCompactMode: isCompactMode } = useMediaQuery()

    const onPressClose = useCallback(() => {
        closePopover?.()
    }, [])

    const { onPressStartPlayingButton, game, creatingGame, error } =
        useCreateGameAction({
            gameConfig: selectedGameConfig,
            selectedGameType: gameType,
        })

    const handleCTAPress = useCallback(() => {
        onPressStartPlayingButton?.()
        onPressClose?.()
    }, [onPressStartPlayingButton, onPressClose])

    const renderGameConfigOption = useCallback(
        ({ option, gameConfig }) => {
            const { key: configKey } = gameConfig
            const { key, label } = option
            const isSelected = selectedGameConfig[configKey] === key

            return (
                <SecondaryButton
                    key={key}
                    radius={6}
                    label={label}
                    onPress={() => updateGameConfig({ key: configKey, value: key })}
                    containerStyle={[
                        styles.buttonContainer,
                        isCompactMode && styles.compactButtonContainer,
                    ]}
                    buttonStyle={[
                        styles.timeContainer,
                        isSelected && { borderColor: dark.colors.secondary },
                    ]}
                    labelStyle={styles.buttonLabelStyle}
                />
            )
        },
        [updateGameConfig, isCompactMode, selectedGameConfig]
    )

    const renderGameConfig = useCallback(
        (gameConfig) => {
            const { key, label, options } = gameConfig

            return (
                <View key={key} style={styles.gameConfigContainer}>
                    <View style={styles.configOptionsContainer}>
                        {_map(options, (option) =>
                            renderGameConfigOption({ option, gameConfig })
                        )}
                    </View>
                </View>
            )
        },
        [renderGameConfigOption]
    )

    return (
        <View style={styles.container}>
            <View style={styles.headerContainer}>
                <Text style={styles.headerText}>{title}</Text>
                {/* <Entypo name="cross" size={20} color={dark.colors.textDark} onPress={onPressClose} /> */}
            </View>
            {/* <View style={styles.imageRow}>
                <Image source={icons[icon]} style={styles.image} />
            </View> */}
            <View style={styles.configOptionsContainer}>
                {_map(
                    gameType === GAME_TYPES.ABILITY_DUELS
                        ? ABILITY_GAME_CONFIG
                        : GAME_CONFIGS,
                    (config) => renderGameConfig(config)
                )}
            </View>

            <PrimaryButton
                testID={`${gameType}-start-game-button`}
                onPress={handleCTAPress}
                label={creatingGame ? 'Creating Game.....' : ctaLabel}
                radius={20}
                buttonStyle={{ height: 30, width: 230, marginTop: 4 }}
            />
        </View>
    )
}

GameConfigsOverlay.propTypes = {
    gameType: PropTypes.string,
}

export default React.memo(GameConfigsOverlay)
