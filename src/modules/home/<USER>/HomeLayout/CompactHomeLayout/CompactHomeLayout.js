import React, { useCallback, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import _map from 'lodash/map';
import { useRouter } from 'expo-router';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import { GAME_MODE } from '@/src/modules/games/constants/gameModes';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from '@/src/core/readers/userReader';
import HomeBannerCarouselContainer from 'modules/home/<USER>/HomeBannerCarusel/HomeBannerCarouselContainer';
import EarnYourStreakOverlayCard from 'modules/home/<USER>/EarnYourStreakOverlayCard';
import UnlockGameTypeOverlayCard from 'modules/home/<USER>/UnlockGameTypeOverlayCard/UnlockGameTypeOverlayCard';
import InviteFriendToMatiksSection from 'shared/InviteFriendToMatiksSection';
import useGameTypes from '../../../hooks/useGameTypes';
import {
  GAME_TYPES,
  VALID_MOBILE_GAME_TYPES,
} from '../../../constants/gameTypes';
import styles from './CompactHomeLayout.style';
import Header from '../../../components/Header/Header';
import GameType from '../../../components/GameConfigs/GameType/GameType';
import DCByCategory from '../../../../dailyChallenge/components/DCByCategory/DCByCategory';
import OnlineUsers from '../../../components/OnlineUsers/OnlineUsers';
import Shortcuts from '../../../components/Shortcuts';

const CompactHomeLayout = () => {
  const { selectedGameType, setSelectedGameType } = useGameTypes();

  const router = useRouter();
  const { user } = useSession();
  const [showEarnStreak, setShowEarnStreak] = useState(true);

  const handleGameTypeSelect = useCallback(
    (gameType) => {
      setSelectedGameType(gameType);
      if (
        gameType === GAME_TYPES.FLASH_ANZAN ||
        gameType === GAME_TYPES.GROUP_PLAY
      ) {
        router.push(`/play-time/instructions?gameType=${gameType}`);
        return;
      }
      if (gameType === GAME_TYPES.MOST_PLAYED) {
        router.push(`/search?timeLimit=1&gameType=${GAME_TYPES.PLAY_ONLINE}`);
        return;
      }
      router.push(`/play-time?gameType=${gameType}`);
    },
    [setSelectedGameType, router],
  );

  const navigateToGamesMenu = useCallback(() => {
    router.push(`/games?gameMode=${GAME_MODE.BLITZ}`);
  }, [router]);

  const hasUnlockedAllGames = userReader.hasUnlockedAllGames(user);

  const renderPlayNowButton = () => {
    if (!hasUnlockedAllGames) {
      return null;
    }
    return (
      <View style={styles.playNowContainer}>
        <InteractivePrimaryButton
          onPress={navigateToGamesMenu}
          label="PLAY NOW"
          buttonStyle={styles.playNowButton}
          buttonContainerStyle={styles.playNowButtonContainer}
          labelStyle={styles.playNowButtonText}
          buttonBorderBackgroundStyle={styles.playNowBackground}
        />
      </View>
    );
  };

  const renderGameTypes = useCallback(
    () => (
      <View style={[styles.gameTypesMainContainer]}>
        <Text style={styles.headingText}>EXPLORE MATH DUELS</Text>
        <View style={styles.gameTypesContainer}>
          {_map(VALID_MOBILE_GAME_TYPES, (gameType) => {
            const isLocked =
              !hasUnlockedAllGames && gameType !== GAME_TYPES.PLAY_ONLINE;
            return (
              <GameType
                key={gameType}
                gameType={gameType}
                isSelected={gameType === selectedGameType}
                setSelectedGameType={() => handleGameTypeSelect(gameType)}
                isLocked={isLocked}
              />
            );
          })}

          {!hasUnlockedAllGames && (
            <UnlockGameTypeOverlayCard showUnlockMessage isFirstLockedCard />
          )}
        </View>
      </View>
    ),
    [handleGameTypeSelect, selectedGameType, hasUnlockedAllGames],
  );

  const handleOnScroll = useCallback((event) => {
    if (event?.nativeEvent?.contentOffset?.y > 50) {
      setShowEarnStreak(false);
    }
  }, []);

  return (
    <View style={{ flex: 1 }}>
      {renderPlayNowButton()}
      <Header user={user} />
      <ScrollView
        contentContainerStyle={{ gap: 24 }}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={handleOnScroll}
      >
        <HomeBannerCarouselContainer />
        <OnlineUsers />
        <Shortcuts />
        <DCByCategory />
        {renderGameTypes()}

        <InviteFriendToMatiksSection />
      </ScrollView>
      <EarnYourStreakOverlayCard showEarnStreak={showEarnStreak} />
    </View>
  );
};

export default React.memo(CompactHomeLayout);
