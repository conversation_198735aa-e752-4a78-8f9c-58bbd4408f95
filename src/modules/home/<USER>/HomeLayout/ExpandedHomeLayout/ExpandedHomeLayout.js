import React, { useCallback, useMemo, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import _map from 'lodash/map';
import { useLocalSearchParams } from 'expo-router';

import ExpandedHomeLayoutTabBar from 'modules/home/<USER>/ExpandedHomeLayoutTabBar';
import OnlineUsers from 'modules/home/<USER>/OnlineUsers'; // This is the correct card used
import DCByCategory from '@/src/modules/dailyChallenge/components/DCByCategory';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from '@/src/core/readers/userReader';
import UnlockGameTypeOverlayCard from 'modules/home/<USER>/UnlockGameTypeOverlayCard/UnlockGameTypeOverlayCard';
import {
  CATEGORY_WISE_GAME_TYPES,
  GAME_CATEGORIES,
  GAME_TYPES,
} from '../../../constants/gameTypes';
import styles, { CENTER_PANE_PADDING } from './ExpandedHomeLayout.style';
import HomeBannerCarouselContainer from '../../../components/HomeBannerCarusel/HomeBannerCarouselContainer';
import GameTypeCard from '../../../components/GameTypeCard/GameTypeCard';

const GAP_BETWEEN_GAME_CARDS = 24;

const ExpandedHomeLayout = () => {
  const { gameCategory = GAME_CATEGORIES.ALL } = useLocalSearchParams();
  const [centerPaneWidth, setCenterPaneWidth] = useState(860);
  const { isTablet } = useMediaQuery();
  const { user } = useSession();

  const handleOnCenterPaneContainerLayout = useCallback((event) => {
    const { width } = event.nativeEvent.layout;
    setCenterPaneWidth(width);
  }, []);

  const gameCardWidth = useMemo(() => {
    let columnCount = 2;
    if (centerPaneWidth >= 800) {
      columnCount = 3;
    }
    return Math.floor(
      (centerPaneWidth -
        CENTER_PANE_PADDING * 2 -
        (columnCount - 1) * GAP_BETWEEN_GAME_CARDS) /
        columnCount,
    );
  }, [centerPaneWidth]);

  const renderGameTypeCards = useCallback(() => {
    const hasUnlockedAllGames = userReader.hasUnlockedAllGames(user);

    return (
      <View
        style={[
          styles.dcCategory,
          {
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: GAP_BETWEEN_GAME_CARDS,
          },
        ]}
      >
        {_map(CATEGORY_WISE_GAME_TYPES[gameCategory], (gameType, index) => {
          const isLocked =
            !hasUnlockedAllGames &&
            gameType !== GAME_TYPES.MOST_PLAYED &&
            gameType !== GAME_TYPES.PLAY_ONLINE;
          return (
            <GameTypeCard
              key={gameType}
              gameType={gameType}
              gameCardWidth={gameCardWidth}
              isLocked={isLocked}
            />
          );
        })}

        {!hasUnlockedAllGames &&
          (gameCategory === GAME_CATEGORIES.ALL ||
            gameCategory === GAME_CATEGORIES.BLITZ) && (
            <UnlockGameTypeOverlayCard showUnlockMessage isFirstLockedCard />
          )}
      </View>
    );
  }, [gameCardWidth, gameCategory, user]);

  const renderCenterPane = () => (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingVertical: 24 }}
    >
      <View
        style={styles.superContainer}
        onLayout={handleOnCenterPaneContainerLayout}
      >
        <HomeBannerCarouselContainer />
        <View style={[styles.dcCategory, { paddingHorizontal: 0 }]}>
          <OnlineUsers />
        </View>
        {isTablet && (
          <View style={[styles.dcCategory, { paddingHorizontal: 0 }]}>
            <DCByCategory />
          </View>
        )}
        <View style={[styles.dcCategory, { gap: 2 }]}>
          <Text style={styles.playNowText}>ONLINE DUELS</Text>
          <ExpandedHomeLayoutTabBar />
        </View>

        {renderGameTypeCards()}
      </View>
    </ScrollView>
  );

  return <View style={styles.container}>{renderCenterPane()}</View>;
};

export default React.memo(ExpandedHomeLayout);
