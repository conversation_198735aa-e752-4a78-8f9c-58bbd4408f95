import React from 'react';
import { Text, View } from 'react-native';
import _map from 'lodash/map';
import _filter from 'lodash/filter';
import userReader from '@/src/core/readers/userReader';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import ShortCutCard from './ShortcutCard/ShortcutCard';
import styles from './Shortcuts.style';
import { SHORTCUTS } from '../../constants/shortcuts';

const Shortcuts = () => {
  const { user } = useSession();
  const activeShortcuts = _filter(SHORTCUTS, (shortcut) => shortcut.isActive);

  const needToShowQuickLinks = userReader.needToShowQuickLinks(user);

  if (!needToShowQuickLinks) {
    return null;
  }

  return (
    <View style={styles.mainContainer}>
      <View style={styles.headerContainer}>
        <Text style={styles.quickLinksText}>QUICK LINKS</Text>
      </View>
      <View style={styles.container}>
        {_map(activeShortcuts, (shortcut) => (
          <ShortCutCard key={shortcut.id} {...shortcut} />
        ))}
      </View>
    </View>
  );
};

export default React.memo(Shortcuts);
