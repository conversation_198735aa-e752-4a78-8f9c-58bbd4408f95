import { TouchableOpacity, View } from 'react-native';
import React, { useCallback } from 'react';

import UserImage from 'atoms/UserImage';

import { useRouter } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import UserStatikCoinsCard from 'modules/profile/components/UserStatikCoinsCard';
import { useSession } from 'modules/auth/containers/AuthProvider';
import FeedIcon from 'shared/Navbar/components/FeedIcon';
import useFeedStore from '@/src/store/useFeedStore';
import styles from './Header.style';
import StreakCard from '../../../profile/components/StreakCard';

const Header = () => {
  const { user } = useSession();
  const router = useRouter();

  const { isRead } = useFeedStore((state) => ({
    isRead: state.isRead,
  }));

  const navigateToProfileScreen = useCallback(() => {
    router.push(`/profile/${user?.username}`);
    Analytics.track(ANALYTICS_EVENTS.CLICKED_ON_PROFILE_ICON);
  }, [router, user?.username]);

  return (
    <View style={styles.header}>
      <View style={styles.headerContent}>
        <TouchableOpacity onPress={navigateToProfileScreen}>
          <View style={styles.matiksIconContainer}>
            <UserImage user={user} />
          </View>
        </TouchableOpacity>
      </View>
      <View style={{ flexDirection: 'row', gap: 10, alignItems: 'center' }}>
        <StreakCard />
        <UserStatikCoinsCard />
        <FeedIcon shouldShowBadge={!isRead} />
      </View>
    </View>
  );
};

export default Header;
