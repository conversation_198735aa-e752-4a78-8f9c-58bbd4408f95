import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';
import { withOpacity } from '../../../../core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    maxWidth: 300,
    width: '100%',
    minWidth: 240,
    height: 190,
    borderRadius: 12,
    backgroundColor: dark.colors.gradientBackground,
    opacity: 1,
    padding: 16,
    position: 'relative',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 6,
    height: '100%',
    justifyContent: 'space-between',
  },
  animationContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    maxHeight: '60%',
    position: 'relative',
    top: 8,
  },
  animation: {
    minWidth: 220,
    width: '100%',
    maxWidth: 240,
    height: 180,
  },

  hoveredContainer: {},
  titleText: {
    fontFamily: 'Montserrat-700',
    fontSize: 14,
    lineHeight: 15,
    letterSpacing: 1,
    color: 'white',
    textAlign: 'left',
  },
  image: {
    height: 39,
    width: 42,
  },
  hoveredImage: {
    height: 43,
    width: 46,
  },
  descriptionText: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    lineHeight: 10,
    letterSpacing: 0.15,
    color: 'white',
    textAlign: 'left',
  },
  playNowContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flexDirection: 'row',
    position: 'relative',
  },
  playNowRiveWrapper: {
    position: 'absolute',
    bottom: -35,
    right: -35,
    width: 100,
    height: 100,
  },
  playNowRive: {
    width: '100%',
    height: '100%',
  },
  infoText: {
    width: '100%',
    gap: 9,
    paddingTop: 15,
    position: 'absolute',
    bottom: 0,
  },
  overlayStyle: {
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 13,
    maxHeight: 365,
    maxWidth: 359,
  },
  mostPlayed: {
    position: 'absolute',
    right: 0,
    top: 0,
    backgroundColor: '#A44201',
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mostPlayedText: {
    fontFamily: 'Montserrat-500',
    fontSize: 8,
    lineHeight: 15,
    letterSpacing: 1,
    color: 'white',
  },

  flipEffect: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: '100%',
    height: '100%',
    backgroundColor: withOpacity('#ffffff', 0.15),
    borderTopLeftRadius: 12,
    backfaceVisibility: 'hidden',
  },
  overlayWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0,
    backgroundColor: '#242424',
    borderRadius: 12,
    zIndex: 10,
    padding: 16,
    transition: 'opacity 0.05s ease, transform 0.05s ease',
  },
  overlayVisible: {
    opacity: 1,
    transform: [{ translateY: 0 }],
  },
  overlayContent: {
    width: '100%',
    height: '100%',
  },
  mainContent: {
    opacity: 1,
    height: '100%',
  },
  mainContentHovered: {
    opacity: 0,
  },
  lockOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
    borderRadius: 12,
  },
  lockIconContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default styles;
