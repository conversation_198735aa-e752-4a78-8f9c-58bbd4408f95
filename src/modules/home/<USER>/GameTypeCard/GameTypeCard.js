import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { Animated, Pressable, Text, View } from 'react-native';
import { useRouter } from 'expo-router';
import Rive from 'atoms/Rive';
import PlayNowRive from '@/assets/rive/homepage/play_now.riv';
import { Asset } from 'expo-asset';
import dark from 'core/constants/themes/dark';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import {
  GAME_TYPE_COLOURS,
  GAME_TYPE_DETAILS,
  GAME_TYPES,
} from '../../constants/gameTypes';
import styles from './GameTypeCard.style';
import GameConfigsOverlay from '../GameConfigsOverlay';

const GameTypeCard = (props) => {
  const router = useRouter();
  const { gameType, gameCardWidth = 260, isLocked = false } = props;

  const cardWidth = Math.max(gameCardWidth, 220);

  const color = GAME_TYPE_COLOURS[gameType];
  const [isHovered, setIsHovered] = useState(false);
  const overlayOpacity = useState(new Animated.Value(0))[0];
  const playNowScale = useState(new Animated.Value(0))[0];
  const playNowRiv = Asset.fromModule(PlayNowRive).uri;

  const { title, subtitle, animation, animationLink } =
    GAME_TYPE_DETAILS[gameType];

  const handleMouseEnter = useCallback(() => {
    if (isLocked) return;

    setIsHovered(true);
    Animated.parallel([
      Animated.timing(overlayOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(playNowScale, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, [overlayOpacity, playNowScale, isLocked]);

  const handleMouseLeave = useCallback(() => {
    if (isLocked) return;

    setIsHovered(false);
    Animated.parallel([
      Animated.timing(overlayOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(playNowScale, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [overlayOpacity, playNowScale, isLocked]);

  const handleOnPress = useCallback(() => {
    if (isLocked) return;

    if (
      gameType === GAME_TYPES.FLASH_ANZAN ||
      gameType === GAME_TYPES.GROUP_PLAY
    ) {
      router.push(`/play-time/instructions?gameType=${gameType}`);
      return;
    }
    if (gameType === GAME_TYPES.MOST_PLAYED) {
      router.push('/search?timeLimit=1');
    }
    if (gameType === GAME_TYPES.PUZZLE_DUELS) {
      router.push('/puzzle/search-opponent');
    }
  }, [gameType, router, isLocked]);

  return (
    <Pressable
      onHoverIn={isLocked ? null : handleMouseEnter}
      onHoverOut={isLocked ? null : handleMouseLeave}
      onPress={isLocked ? null : handleOnPress}
      style={{
        position: 'relative',
        cursor: isLocked ? 'not-allowed' : 'pointer',
      }}
    >
      <View
        style={[
          styles.container,
          {
            backgroundColor: color,
            width: cardWidth,
            opacity: isLocked ? 0.7 : 1,
          },
        ]}
      >
        <View
          style={[
            styles.mainContent,
            isHovered &&
              gameType === GAME_TYPES.PLAY_ONLINE &&
              styles.mainContentHovered,
          ]}
        >
          <View style={styles.contentContainer}>
            {gameType === GAME_TYPES.MOST_PLAYED && (
              <View style={styles.mostPlayed}>
                <Text style={styles.mostPlayedText}>Most Played</Text>
              </View>
            )}
            {gameType === GAME_TYPES.PUZZLE_DUELS && (
              <View
                style={[
                  styles.mostPlayed,
                  { backgroundColor: dark.colors.crossMathPuzzleNewTextBg },
                ]}
              >
                <Text style={styles.mostPlayedText}>New</Text>
              </View>
            )}
            <View style={styles.animationContainer}>
              <Rive
                url={animationLink ?? animation}
                autoPlay
                style={styles.animation}
              />
            </View>
            <View style={styles.playNowContainer}>
              <View style={styles.infoText}>
                <Text style={styles.titleText}>{title}</Text>
                <Text style={styles.descriptionText}>{subtitle}</Text>
              </View>
              {isHovered &&
                (gameType === GAME_TYPES.MOST_PLAYED ||
                  gameType === GAME_TYPES.GROUP_PLAY ||
                  gameType === GAME_TYPES.FLASH_ANZAN ||
                  gameType === GAME_TYPES.PUZZLE_DUELS ||
                  gameType === GAME_TYPES.ABILITY_DUELS) && (
                  <Animated.View
                    style={[
                      styles.playNowRiveWrapper,
                      {
                        transform: [{ scale: playNowScale }],
                      },
                    ]}
                  >
                    <Rive
                      url={playNowRiv}
                      autoPlay
                      style={styles.playNowRive}
                    />
                  </Animated.View>
                )}
            </View>
          </View>
        </View>

        {gameType !== GAME_TYPES.FLASH_ANZAN &&
          gameType !== GAME_TYPES.PUZZLE_DUELS &&
          gameType !== GAME_TYPES.GROUP_PLAY &&
          gameType !== GAME_TYPES.MOST_PLAYED && (
            <Animated.View
              style={[styles.overlayWrapper, { opacity: overlayOpacity }]}
            >
              <View style={styles.overlayContent}>
                <GameConfigsOverlay gameType={gameType} />
              </View>
            </Animated.View>
          )}

        {isLocked && (
          <View style={styles.lockOverlay}>
            <View style={styles.lockIconContainer}>
              <Icon
                name="lock"
                type={ICON_TYPES.FONT_AWESOME_5}
                color="white"
                size={24}
              />
            </View>
          </View>
        )}
      </View>
    </Pressable>
  );
};

GameTypeCard.propTypes = {
  gameType: PropTypes.string,
  gameCardWidth: PropTypes.number,
  isLocked: PropTypes.bool,
  isFirstLockedCard: PropTypes.bool,
};

export default React.memo(GameTypeCard);
