import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  screen: {
    flex: 1,
  },
  screenContent: {
    backgroundColor: Dark.colors.background,
    padding: 16,
    flex: 1,
    // alignItems: 'center',
  },
  screenItem: {
    flexDirection: 'row',
    paddingHorizontal: 14,
    paddingVertical: 4,
    paddingLeft: 2,
    // borderBottomWidth: 0.4,
    // borderBottomColor: Dark.colors.tertiary,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  screenContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    height: 40,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    width: 36,
    height: 36,
    resizeMode: 'contain',
  },
  screenItemText: {
    color: 'white',
    fontSize: 14,
    marginLeft: 20,
    fontFamily: 'Montserrat-300',
  },
  logoutContainer: {
    marginVertical: 10,
    paddingLeft: 2,
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoutText: {
    marginLeft: 24,
    color: 'white',
    fontSize: 14,
    fontFamily: 'Montserrat-300',
  },
  viewProfileBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  viewProfileText: {
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
    lineHeight: 20,
    textAlign: 'center',
    iconAlign: 'bottom',
    marginTop: 4,
  },
  username: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    lineHeight: 13,
  },
  userInfoCard: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: 20,
    borderBottomColor: dark.colors.tertiary,
    paddingBottom: 24,
    borderBottomWidth: 1.5,
    // alignContent:'center'
  },
  userImage: {
    width: 68,
    height: 68,
    overflow: 'hidden',
    borderRadius: 12,
  },
  name: {
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    color: 'white',
    lineHeight: 20,
  },
  headingText: {
    fontSize: 20,
    fontFamily: 'Montserrat-500',
    textAlign: 'left',
    color: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  newbutton:{
    width: 40,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: dark.colors.red,
    borderRadius: 20
  },
  newText:{
    fontFamily: 'Montserrat-700',
    fontSize: 8,
    color: dark.colors.textLight,
    letterSpacing: 1
  }
   
});

export default styles;
