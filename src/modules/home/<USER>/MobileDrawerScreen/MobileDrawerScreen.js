import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Linking,
  Pressable,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';

import { Text } from '@rneui/themed';

import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { isValidURL } from 'core/utils/general';
import Icon from 'atoms/Icon';
import Analytics from 'core/analytics';
import UserImage from 'atoms/UserImage';
import { Ionicons } from '@expo/vector-icons';
import userReader from '@/src/core/readers/userReader';
import { MOBILE_DRAWER_VISITS_KEY } from '@/src/core/constants/appConstants';
import useLocalCache from 'core/hooks/useLocalCache';
import _forEach from 'lodash/forEach';
import _set from 'lodash/set';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _toNumber from 'lodash/toNumber';
import _toString from 'lodash/toString';
import _isNil from 'lodash/isNil';
import { getHomeNavigatorTabs } from 'navigator/constants/routeConstants';
import dark from 'core/constants/themes/dark';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import styles from './Drawer.style';
import LogoutFooter from './LogoutFooter';
import LogoutOverlay from './LogoutOverlay';
import { useSession } from '../../../auth/containers/AuthProvider';

const NEW_TAG_CACHE_KEY = 'newTag';
const SHOW_NEW_TAG_KEY = 'showNewTag';

let storeVisitInitialized = false;

const MobileDrawerScreen = () => {
  const router = useRouter();
  const { signOut, user } = useSession();
  const { isMobile } = useMediaQuery();
  const ngp = userReader.gamesPlayed(user);
  const { getData, setData } = useLocalCache(NEW_TAG_CACHE_KEY);
  const [newTagCachedData, setNewTagCachedData] = useState();

  useEffect(() => {
    if (!isMobile) {
      router.replace('/home');
    }
  }, [isMobile, router]);

  const HOME_NAVIGATOR_TABS = useMemo(
    () => getHomeNavigatorTabs({ user }),
    [user],
  );

  useEffect(() => {
    const loadValues = async () => {
      if (_isEmpty(HOME_NAVIGATOR_TABS)) return;
      if (storeVisitInitialized) return;
      storeVisitInitialized = true;
      const storedData = await getData();
      setNewTagCachedData(storedData);
      const updatedData = _isNil(storedData) ? {} : storedData;
      _forEach(HOME_NAVIGATOR_TABS, (navigatorTab) => {
        if (navigatorTab?.showNewTag) {
          const prevMoreVisitCount = _toNumber(
            _get(updatedData, [navigatorTab?.key, MOBILE_DRAWER_VISITS_KEY], 0),
          );
          _set(
            updatedData,
            [navigatorTab?.key, MOBILE_DRAWER_VISITS_KEY],
            _toString(prevMoreVisitCount + 1),
          );
        }
      });

      setData(updatedData);
    };
    loadValues();
  }, [HOME_NAVIGATOR_TABS, getData, setData]);

  const [isOverlayVisible, setIsOverlayVisible] = useState(false);

  const toggleOverlay = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.NAV.CLICKED_ON_LOG_OUT);
    setIsOverlayVisible((prev) => !prev);
  }, [setIsOverlayVisible]);

  const confirmLogout = useCallback(() => {
    toggleOverlay();
    signOut();
  }, [signOut, toggleOverlay]);

  const handleNavigation = useCallback(
    (item) => {
      const { route, event } = item;
      Analytics.track(event, {
        [PAGE_NAME_KEY]: PAGE_NAMES.NAV_MORE_OPTIONS,
      });
      if (isValidURL(route)) {
        Linking.openURL(route);
      } else {
        router.push(`/${route}`);
      }
    },
    [router],
  );

  const handleOnPressItem = useCallback(
    (item) => {
      handleNavigation(item);
      if (item?.showNewTag) {
        const dataToCache = newTagCachedData;
        _set(dataToCache, [item?.key, SHOW_NEW_TAG_KEY], 'false');
        setData(dataToCache);
        setNewTagCachedData(dataToCache);
      }
    },
    [handleNavigation, newTagCachedData, setData],
  );

  const shouldShowNewButton = useCallback(
    (item) => {
      const visitCount = _toNumber(
        _get(newTagCachedData, [item?.key, MOBILE_DRAWER_VISITS_KEY], 0),
      );
      const newButtonClicked = _get(
        newTagCachedData,
        [item?.key, SHOW_NEW_TAG_KEY],
        'false',
      );
      return (
        item?.showNewTag &&
        visitCount <= 3 &&
        newButtonClicked === 'false' &&
        ngp > 3
      );
    },
    [newTagCachedData, ngp],
  );

  const renderNavOptions = useCallback(
    () =>
      HOME_NAVIGATOR_TABS.map((item, index) => {
        const showNewButton = shouldShowNewButton(item);

        return (
          <TouchableOpacity
            key={index}
            style={styles.screenItem}
            onPress={() => handleOnPressItem(item)}
          >
            <View style={styles.screenContainer}>
              <View style={styles.iconContainer}>
                <Icon size={18} color="white" {...item.iconConfig} />
              </View>
              <Text style={styles.screenItemText}>{item.text}</Text>
            </View>
            {showNewButton && (
              <View style={styles.newbutton}>
                <Text style={styles.newText}>NEW</Text>
              </View>
            )}
          </TouchableOpacity>
        );
      }),
    [HOME_NAVIGATOR_TABS, shouldShowNewButton, handleOnPressItem],
  );

  const renderUserInfo = useCallback(
    () => (
      <View style={styles.userInfoCard}>
        <UserImage
          user={user}
          size={68}
          rounded={false}
          style={styles.userImage}
        />
        <View style={{ gap: 4 }}>
          <Text style={styles.name}>{user?.name}</Text>
          <Text style={styles.username} dataDetectorType={null}>
            {user?.username}
          </Text>
          <TouchableOpacity
            style={styles.viewProfileBox}
            onPress={() => {
              handleNavigation({
                route: `profile/${user?.username}`,
                event: ANALYTICS_EVENTS.NAV.CLICKED_ON_PROFILE,
              });
            }}
          >
            <Text style={styles.viewProfileText}>View Profile</Text>
            <Icon
              size={15}
              color={dark.colors.secondary}
              name="chevron-right"
            />
          </TouchableOpacity>
        </View>
      </View>
    ),
    [user, handleNavigation],
  );

  const navigateToUserSettings = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.CLICK_ON_PAGE_HEADER_SETTINGS);
    router.push(`/settings`);
  }, [router]);

  const renderSettingsNavOption = useCallback(
    () => (
      <Pressable
        style={({ hovered }) => [
          styles.optionRowContainer,
          hovered && styles.hoveredOptionRowContainer,
        ]}
        onPress={navigateToUserSettings}
      >
        <Ionicons size={16} color="white" name="settings-sharp" />
      </Pressable>
    ),
    [navigateToUserSettings],
  );

  return (
    <View style={styles.screen}>
      <View
        style={{
          paddingVertical: 24,
          paddingHorizontal: 20,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Text style={styles.headingText}>Matiks</Text>
        {renderSettingsNavOption()}
      </View>
      <ScrollView
        style={styles.screenContent}
        showsVerticalScrollIndicator={false}
      >
        {renderUserInfo()}
        {renderNavOptions()}

        <LogoutFooter toggleOverlay={toggleOverlay} />

        <LogoutOverlay
          isVisible={isOverlayVisible}
          toggleOverlay={toggleOverlay}
          confirmLogout={confirmLogout}
        />
      </ScrollView>
    </View>
  );
};

export default React.memo(MobileDrawerScreen);
