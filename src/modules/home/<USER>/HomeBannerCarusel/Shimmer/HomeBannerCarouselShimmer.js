import React from 'react';

import { View } from 'react-native';
import Dark from "core/constants/themes/dark";
import ShimmerView from '../../../../../components/molecules/ShimmerView';

const HomeBannerCarouselShimmer = () => {
    return (
        <View style={{ width: '100%',  }}>
            <View style={{ borderColor: Dark.colors.tertiary, borderWidth: 2, borderRadius: 10,height: 110 }}>
                <View style={{
                    padding: 16,
                    borderRadius: 15
                }}>
                    <ShimmerView
                        style={{ width: 100, height: 18, borderRadius: 8, }}
                        shimmerColors={Dark.colors.placeholderShimmerColors}
                    />
                    <ShimmerView
                        style={{ width: 240, height: 12, borderRadius: 8, marginTop: 8, }}
                        shimmerColors={Dark.colors.placeholderShimmerColors}
                    />
                    <ShimmerView
                        style={{ width: 80, height: 16, borderRadius: 8, marginTop: 12 }}
                        shimmerColors={Dark.colors.placeholderShimmerColors}
                    />
                    <ShimmerView
                        style={{ width: 100, height: 25, borderRadius: 8, marginTop: 4, position: "absolute", bottom: 0, right: 14 }}
                        shimmerColors={Dark.colors.placeholderShimmerColors}
                    />
                </View>
            </View>
        </View>
    );
};

HomeBannerCarouselShimmer.propTypes = {

};

export default React.memo(HomeBannerCarouselShimmer);