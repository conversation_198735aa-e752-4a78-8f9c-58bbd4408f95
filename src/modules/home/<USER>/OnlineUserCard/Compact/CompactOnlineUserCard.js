import React, { useCallback, useMemo, useRef } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import UserImage from 'atoms/UserImage';
import PropTypes from 'prop-types';
import { showPopover } from 'molecules/Popover/Popover';
import Icon, { ICON_TYPES } from '@/src/components/atoms/Icon';
import { useRouter } from 'expo-router';
import OnlineUsersPage from 'modules/home/<USER>/OnlineUsersPage/OnlineUsersPage';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { showRightPane } from 'molecules/RightPane/RightPane';
import userReader from 'core/readers/userReader';
import USER_ACTIVITY from 'core/constants/userActivityConstants';
import dark from 'core/constants/themes/dark';
import OnlineUserBottomSheet from '../../OnlineUserBottomSheet';
import useCompactOnlineUserCardStyles from './CompactOnlineUserCard.style';
import useHaptics from '@/src/core/hooks/useHaptics';

const CompactOnlineUserCard = (props) => {
  const { user, currActivity } = props;
  const styles = useCompactOnlineUserCardStyles();
  const router = useRouter();
  const { triggerHaptic } = useHaptics();

  const touchableRef = useRef(null);

  const { isMobile: isCompactMode } = useMediaQuery();

  const onUserProfilePress = useCallback(() => {
    triggerHaptic();
    const popoverContent = (
      <OnlineUserBottomSheet user={user} currActivity={currActivity} />
    );

    if (isCompactMode) {
      showPopover({
        content: popoverContent,
        style: styles.bottomSheetStyle,
        overlayLook: true,
        animationType: 'slide',
      });
    } else if (touchableRef.current) {
      touchableRef.current.measure((fx, fy, width, height, pageX, pageY) => {
        const popoverTop = pageY + height;
        const popoverLeft = pageX;
        showPopover({
          content: popoverContent,
          style: {
            position: 'absolute',
            top: popoverTop,
            left: popoverLeft,
            maxWidth: 300,
          },
          overlayLook: false,
          animationType: 'fade',
        });
      });
    }
  }, [user, currActivity, styles.bottomSheetStyle, isCompactMode]);

  const isExploring = useMemo(
    () => currActivity === USER_ACTIVITY.EXPLORING,
    [currActivity],
  );

  const navigateToOnlineUsersPage = useCallback(() => {
    if (!isCompactMode) {
      showRightPane({
        content: <OnlineUsersPage />,
      });
      return;
    }
    router.push('/online-users');
  }, [router]);

  if (!user) {
    return (
      <TouchableOpacity
        style={styles.seeMoreIcon}
        onPress={navigateToOnlineUsersPage}
      >
        <View style={styles.iconContainer}>
          <Icon
            name="arrowright"
            type={ICON_TYPES.ANT_DESIGN}
            size={20}
            styles={styles.icon}
            color={dark.colors.textDark}
          />
        </View>
        <View style={{ width: 40, marginTop: 4 }}>
          <Text style={styles.name} numberOfLines={1}>
            See More
          </Text>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      ref={touchableRef}
      style={styles.container}
      onPress={onUserProfilePress}
    >
      <View style={styles.imageContainer}>
        <UserImage
          user={user}
          rounded={false}
          style={styles.userImage}
          size={50}
        />
        <View
          style={[
            styles.activityIndicator,
            isExploring && { backgroundColor: dark.colors.secondary },
          ]}
        />
      </View>
      <View style={{ width: 60, marginTop: 2 }}>
        <Text style={styles.name} numberOfLines={1}>
          {userReader.username(user)}
        </Text>
        <Text style={styles.rating}>{user?.rating}</Text>
      </View>
    </TouchableOpacity>
  );
};

CompactOnlineUserCard.propTypes = {
  user: PropTypes.object,
  currActivity: PropTypes.string,
};

export default React.memo(CompactOnlineUserCard);
