import { useEffect, useMemo } from 'react';
import _filter from 'lodash/filter';
import _orderBy from 'lodash/orderBy';
import { useSession } from '../../auth/containers/AuthProvider';
import _get from 'lodash/get';
import _isNil from 'lodash/isNil';
import useWebsocketStore from 'store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';

const useOnlineUsersEventSubscription = () => {
  const { isConnected, lastMessage, joinChannel, leaveChannel } =
    useWebsocketStore((state) => ({
      isConnected: state.isConnected,
      lastMessage: state.lastMessage,
      joinChannel: state.joinChannel,
      leaveChannel: state.leaveChannel,
    }));
  const { userId: currUserId } = useSession();
  const channel = WEBSOCKET_CHANNELS.OnlineUsers();

  const channelLastMessage = _get(lastMessage, [channel]);

  const isFetchingOnlineUsers = _isNil(channelLastMessage);

  useEffect(() => {
    if (isConnected) {
      joinChannel(channel);
    }
    return () => {
      leaveChannel(channel);
    };
  }, [isConnected, joinChannel, leaveChannel, channel]);

  const onlineUsers = useMemo(() => {
    const onlineUsers = channelLastMessage?.onlineUsers ?? EMPTY_ARRAY;
    return _orderBy(
      _filter(onlineUsers, (user) => user?.userInfo?._id !== currUserId),
      (user) => user?.userInfo?.rating ?? 0,
      'desc',
    );
  }, [channelLastMessage, currUserId]);

  return {
    onlineUsers,
    isFetchingOnlineUsers,
    unsubscribe: () => leaveChannel(channel),
  };
};

export default useOnlineUsersEventSubscription;
