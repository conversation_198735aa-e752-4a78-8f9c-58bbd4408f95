import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  highlightedText: {
    color: dark.colors.streakOrangeColor,
    fontFamily: 'Montserrat-700',
  },
  container: {
    maxWidth: 600,
    paddingTop: 32,
    gap: 32,
  },
  contentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    gap: 16,
  },
  titleText: {
    color: 'white',
    fontSize: 20,
    fontFamily: 'Montserrat-600',
    textAlign: 'center',
    letterSpacing: 0,
    lineHeight: 24,
    marginBottom: 10,
  },
  descriptionText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    textAlign: 'center',
    letterSpacing: 0,
    lineHeight: 20,
  },
  buttonsContainer: {
    padding: 24,
    borderTopWidth: 0.2,
    borderTopColor: dark.colors.tertiary,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButtonLabel: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  confirmButtonLabel: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  buttonContainer: {
    flex: 1,
  },
  cancelButtonContainer: {
    marginRight: 8,
  },
  confirmButtonContainer: {
    marginLeft: 8,
  },
});

export default styles;
