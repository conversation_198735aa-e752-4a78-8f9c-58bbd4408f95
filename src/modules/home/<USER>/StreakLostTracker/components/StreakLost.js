import React, { useCallback, useMemo, useState } from 'react';
import checkIsSameDay from '@/src/core/utils/checkIsSameDay';
import { Image, Platform, Text, View } from 'react-native';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import dark from '@/src/core/constants/themes/dark';
import _map from 'lodash/map';
import ShieldImage from '@/assets/images/shield.png';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import useStreakFreezer from '@/src/modules/profile/hooks/mutations/useStreakFreezer';
import { openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import shieldIcon from '@/assets/images/icons/shield_icon.png';
import { showToast, TOAST_TYPE } from '@/src/components/molecules/Toast';
import StartAFresh from './StartAFresh';
import styles from './StreakLost.style';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

const StreakLost = ({ closeBottomSheet, streakStatus }) => {
  const isWeb = Platform.OS === 'web';
  const { missedDays, lastSevenDays, lostStreakCount, streakFreezers } =
    streakStatus;
  const { applyStreakFreezer, loading: isFreezerLoading } = useStreakFreezer();
  const [isApplyingShield, setIsApplyingShield] = useState(false);
  const { refreshCurrentUser } = useSession();
  const today = new Date(getCurrentTime()).toISOString();

  const renderStreakDate = (day) => {
    const { date, activity } = day;
    const datePart = date.slice(8, 10);
    const isToday = checkIsSameDay({ date1: today, date2: date });
    return (
      <View
        style={[
          styles.dayContainer,
          !activity && { backgroundColor: 'transparent', borderWidth: 0 },
          isToday && styles.todayDayContainer,
        ]}
      >
        <Text
          style={[
            styles.dateText,
            !activity && { color: dark.colors.textDark },
          ]}
        >
          {datePart}
        </Text>
      </View>
    );
  };

  const handleOnPressStartAFresh = useCallback(() => {
    openBottomSheet({
      content: ({ closeBottomSheet }) => (
        <StartAFresh
          closeBottomSheet={closeBottomSheet}
          streakStatus={streakStatus}
        />
      ),
      styles: {
        frame: {
          borderTopColor: dark.colors.streakOrangeColor,
        },
      },
      dismissOnOverlayPress: false,
    });
  }, [streakStatus]);

  const shields = useMemo(() => {
    const result = [];
    for (let i = 0; i < missedDays; i++) {
      result.push(
        <Image
          key={i}
          source={ShieldImage}
          style={styles.shieldImage}
          resizeMode="contain"
        />,
      );
    }
    return result;
  }, [missedDays]);

  const onPressApplyShield = useCallback(async () => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_ON_APPLY_STREAK_FREEZER, {
      streakLostCount: lostStreakCount,
      streakFreezersCount: streakFreezers,
      missedDaysCount: missedDays
    });
    setIsApplyingShield(true);
    try {
      await applyStreakFreezer();
    } catch (error) {
      showToast({
        description: 'Failed to Apply Shield, Please Try Again !',
        type: TOAST_TYPE.INFO,
      });
    } finally {
      setIsApplyingShield(false);
      refreshCurrentUser();
      closeBottomSheet();
      showToast({
        description:"Applied Streak Freezer Successfully !",
        type:TOAST_TYPE.SUCCESS
      })
    }
  }, [applyStreakFreezer, closeBottomSheet]);

  const renderAvailableStreak = useCallback(
    () => (
      <View style={styles.streakAvailableContainer}>
        <Image
          source={shieldIcon}
          style={{ width: 12, height: 12 }}
          resizeMode="contain"
        />
        <Text
          style={styles.streakAvailableText}
        >{`${streakFreezers ?? 0} Available`}</Text>
      </View>
    ),
    [streakFreezers],
  );

  return (
    <View style={styles.container}>
      {renderAvailableStreak()}
      <TextWithShadow
        text="STREAK MISSED"
        containerStyle={styles.headerContainer}
        textStyle={styles.headerTextStyle}
        strokeColor="#000000"
        strokeWidth={4}
        shadowOffsetX={2}
        shadowOffsetY={-5}
      />
      <Text style={styles.subTitle}>
        Save Your{' '}
        <Text
          style={styles.lostStreakHighlight}
        >{`${lostStreakCount} Day Streak`}</Text>{' '}
        by applying
        {` `}
        {missedDays} streak shields
      </Text>
      <View style={styles.dateContainer}>
        {_map(lastSevenDays, (day, index) => (
          <View key={index}>{renderStreakDate(day)}</View>
        ))}
      </View>
      <View style={styles.shieldsContainer}>{shields}</View>
      <View style={styles.footer}>
        <InteractiveSecondaryButton
          label="START-A-FRESH"
          buttonContainerStyle={[styles.buttonContainerStyle]}
          buttonStyle={[isWeb && { paddingHorizontal: 20 }]}
          labelStyle={styles.buttonLabelStyle}
          onPress={handleOnPressStartAFresh}
        />
        <InteractiveSecondaryButton
          label={
            isApplyingShield || isFreezerLoading
              ? 'APPLYING...'
              : 'APPLY SHIELDS'
          }
          disabled={isApplyingShield || isFreezerLoading}
          buttonContainerStyle={[styles.buttonContainerStyle]}
          buttonStyle={[isWeb && { paddingHorizontal: 20 }]}
          borderColor={dark.colors.streak}
          buttonBackgroundStyle={{ backgroundColor: dark.colors.streak }}
          labelStyle={[styles.buttonLabelStyle, { color: dark.colors.streak }]}
          onPress={onPressApplyShield}
        />
      </View>
    </View>
  );
};

export default React.memo(StreakLost);
