import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import StreakLost from './StreakLost';
import styles from './StartAFresh.style';

const StartAFresh = ({ closeBottomSheet, streakStatus }) => {
  const handleCancelPress = useCallback(() => {
    closeBottomSheet();
    openBottomSheet({
      content: ({ closeBottomSheet }) => (
        <StreakLost
          closeBottomSheet={closeBottomSheet}
          streakStatus={streakStatus}
        />
      ),
      styles: {
        frame: {
          borderTopColor: dark.colors.streakOrangeColor,
        },
      },
      dismissOnOverlayPress: false,
    });
  }, [closeBottomSheet, streakStatus]);

  const handleConfirmPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_ON_START_A_FRESH);
    closeBottomSheet();
  }, [closeBottomSheet]);

  const { lostStreakCount } = streakStatus;
  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Text style={styles.titleText}>Start a Fresh ?</Text>
        <Text style={styles.descriptionText}>
          Are you sure you want to{' '}
          <Text style={styles.highlightedText}>
            break
            {lostStreakCount} Day streak
          </Text>
          {` `}and start a fresh?
        </Text>
      </View>
      <View style={styles.buttonsContainer}>
        <InteractiveSecondaryButton
          label="CANCEL"
          borderColor={dark.colors.tertiary}
          onPress={handleCancelPress}
          labelStyle={styles.cancelButtonLabel}
          buttonContainerStyle={[
            styles.buttonContainer,
            styles.cancelButtonContainer,
          ]}
        />
        <InteractiveSecondaryButton
          label="YES, START FRESH"
          borderColor={dark.colors.streakOrangeColor}
          buttonBackgroundStyle={{
            backgroundColor: dark.colors.streakOrangeColor,
          }}
          onPress={handleConfirmPress}
          labelStyle={styles.confirmButtonLabel}
          buttonContainerStyle={[
            styles.buttonContainer,
            styles.confirmButtonContainer,
          ]}
        />
      </View>
    </View>
  );
};

export default React.memo(StartAFresh);
