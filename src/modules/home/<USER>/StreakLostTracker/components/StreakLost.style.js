import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 500,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
    flexDirection: 'column',
  },
  headerContainer: {
    alignSelf: 'center',
  },
  headerTextStyle: {
    fontSize: 34,
    fontFamily: 'Montserrat-800',
    color: dark.colors.red,
  },
  subTitle: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    color: dark.colors.textLight,
    marginTop: 24,
  },
  dateContainer: {
    marginTop: 30,
    flexDirection: 'row',
    gap: 6,
  },
  dayContainer: {
    width: 42,
    height: 34,
    backgroundColor: dark.colors.streakOrangeColor,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    borderColor: dark.colors.streak,
    borderWidth: 2,
  },
  todayDayContainer: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: dark.colors.streakOrangeColor,
  },
  dateText: {
    fontFamily: 'Montserrat-800',
    fontSize: 12,
    color: dark.colors.textLight,
  },
  lostStreakHighlight: {
    fontSize: 12,
    color: dark.colors.streakOrangeColor,
  },
  shieldsContainer: {
    flexDirection: 'row',
    gap: 6,
    marginBottom: 50,
  },
  shieldImage: {
    height: 230,
    width: 150,
  },
  footer: {
    flex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 90,
    width: '100%',
    borderTopColor: dark.colors.tertiary,
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  buttonContainerStyle: {
    maxWidth: 220,
  },
  buttonLabelStyle: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: dark.colors.textDark,
  },
  streakAvailableContainer: {
    width: 100,
    height: 28,
    backgroundColor: dark.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 6,
    borderRadius: 20,
    borderWidth: 0.5,
    borderColor: dark.colors.tertiary,
    alignSelf: 'center',
    marginBottom: 12,
  },
  streakAvailableText: {
    fontSize: 10,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
  },
});

export default styles;
