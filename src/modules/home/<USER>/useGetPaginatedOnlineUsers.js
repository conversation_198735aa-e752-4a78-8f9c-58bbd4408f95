import { gql, useLazyQuery } from '@apollo/client';
import { useCallback, useRef } from 'react';

const GET_ONLINE_USERS = gql`
  query OnlineUsers($page: Int!, $pageSize: Int!) {
    onlineUsers(page: $page, pageSize: $pageSize) {
      users {
        userInfo {
          _id
          username
          profileImageUrl
          rating
        }
        currActivity
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

const DEFAULT_PAGE_SIZE = 20;

const useGetPaginatedOnlineUsers = ({ pageSize = DEFAULT_PAGE_SIZE }) => {
  const [fetchOnlineUsersQuery, { loading, error, refetch }] = useLazyQuery(
    GET_ONLINE_USERS,
    {
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'cache-and-network',
    },
  );

  const loadingRef = useRef(loading);
  loadingRef.current = loading;

  const fetchOnlineUsers = useCallback(
    ({ pageNumber }) => {
      if (loadingRef.current) return;

      return fetchOnlineUsersQuery({
        variables: {
          page: pageNumber,
          pageSize,
        },
      });
    },
    [fetchOnlineUsersQuery, pageSize],
  );

  return {
    loading,
    error,
    fetchOnlineUsers,
  };
};

export default useGetPaginatedOnlineUsers;
