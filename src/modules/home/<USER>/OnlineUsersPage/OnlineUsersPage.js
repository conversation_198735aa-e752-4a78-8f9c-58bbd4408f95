import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import Header from 'shared/Header';
import PlaceholderRow from 'shared/PlaceholderRow';
import PaginatedList from 'shared/PaginatedList';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Entypo from '@expo/vector-icons/Entypo';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import styles from './OnlineUsersPage.style';
import ExpandedOnlineUserCard from '../../components/OnlineUserCard/Expanded/ExpandedOnlineUserCard';
import useGetPaginatedOnlineUsers from '../../hooks/useGetPaginatedOnlineUsers';

const PAGE_SIZE = 50;

const OnlineUsersPage = () => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const { fetchOnlineUsers } = useGetPaginatedOnlineUsers({
    pageSize: PAGE_SIZE,
  });

  const fetchOnlineUsersData = useCallback(
    async ({ pageNumber }) => {
      const response = await fetchOnlineUsers({ pageNumber });
      const { data } = response;
      const { onlineUsers: usersListObject } = data ?? EMPTY_OBJECT;
      const { users, totalResults } = usersListObject;
      return { data: users, totalItems: totalResults };
    },
    [fetchOnlineUsers],
  );

  const renderOnlineUserItem = useCallback(
    ({ item }) => (
      <ExpandedOnlineUserCard
        currActivity={item?.currActivity}
        key={item?.user?._id}
        user={item?.userInfo}
      />
    ),
    [],
  );

  const rendedPlaceHolder = useCallback(
    () => (
      <View>
        <PlaceholderRow />
        <PlaceholderRow />
        <PlaceholderRow />
      </View>
    ),
    [],
  );

  return (
    <View style={{ flex: 1, overflow: 'hidden' }}>
      <Header title="Online Mathletes" />
      {!isCompactMode && (
        <View style={styles.headerRow}>
          <Text style={styles.titleText}>Online Mathletes</Text>
          <Entypo
            name="cross"
            size={20}
            color="white"
            onPress={closeRightPane}
          />
        </View>
      )}
      <View style={{ paddingHorizontal: 16, flex: 1 }}>
        <PaginatedList
          placeholderComponent={rendedPlaceHolder}
          fetchData={fetchOnlineUsersData}
          renderItem={renderOnlineUserItem}
          renderHeader={() => null}
          pageSize={PAGE_SIZE}
        />
      </View>
    </View>
  );
};

export default React.memo(OnlineUsersPage);
