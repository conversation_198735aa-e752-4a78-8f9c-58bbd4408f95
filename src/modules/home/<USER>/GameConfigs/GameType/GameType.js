import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import GameTypeCard from '@/src/components/shared/GameTypeCard';
import { GAME_TYPE_DETAILS, GAME_TYPES } from '../../../constants/gameTypes';
import Analytics from '../../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../../core/analytics/const';
import { PAGE_NAMES } from '../../../../../core/constants/pageNames';

const GameType = (props) => {
  const {
    gameType,
    isSelected = false,
    setSelectedGameType,
    isLocked = false,
  } = props;
  const { icon, title, subtitle, tags, gradientColor } =
    GAME_TYPE_DETAILS[gameType];

  const onPressGameType = useCallback(() => {
    let eventName = '';
    switch (gameType) {
      case GAME_TYPES.MOST_PLAYED:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_MOST_PLAYED;
        break;
      case GAME_TYPES.PLAY_ONLINE:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_ONLINE_DUELS;
        break;
      case GAME_TYPES.PLAY_WITH_FRIEND:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_FRIEND;
        break;
      case GAME_TYPES.PRACTICE:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_PRACTICE;
        break;
      case GAME_TYPES.FLASH_ANZAN:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_FLASH_ANZAN;
        break;
      case GAME_TYPES.FASTEST_FINGER:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_FASTEST_FINGER;
        break;
      case GAME_TYPES.GROUP_PLAY:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_GROUP_PLAY;
        break;
      case GAME_TYPES.PUZZLE_DUELS:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_PUZZLE_DUELS;
        break;
      default:
        eventName = ANALYTICS_EVENTS.ARENA.CLICKED_ON_ARENA_ONLINE_DUELS;
    }
    Analytics.track(eventName, {
      pageName: PAGE_NAMES.ARENA_PAGE,
    });
    setSelectedGameType?.({ gameType });
  }, [gameType, setSelectedGameType]);

  return (
    <GameTypeCard
      title={title}
      subtitle={subtitle}
      tags={tags}
      gradientColor={gradientColor}
      onPress={onPressGameType}
      isLocked={isLocked}
    />
  );
};

GameType.propTypes = {
  gameType: PropTypes.string.isRequired,
  isSelected: PropTypes.bool,
  setSelectedGameType: PropTypes.func,
  isLocked: PropTypes.bool,
};

export default React.memo(GameType);
