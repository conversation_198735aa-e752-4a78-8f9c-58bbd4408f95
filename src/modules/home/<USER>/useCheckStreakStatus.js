import { gql, useQuery } from '@apollo/client';

const EMPTY_OBJECT = {};

export const CHECK_USER_STREAK_STATUS = gql`
  query CheckUserStreakStatus {
    checkUserStreakStatus {
      hasStreak
      streakFreezers
      missedDays
      canSaveStreak
      lostStreakCount
      lastSevenDays {
        date
        activity
      }
    }
  }
`;

const useCheckStreakStatus = () => {
  const { data, loading, error } = useQuery(CHECK_USER_STREAK_STATUS, {
    fetchPolicy: 'cache-and-network',
  });

  const { checkUserStreakStatus } = data ?? EMPTY_OBJECT;

  return {
    streakStatus: checkUserStreakStatus,
    loading,
    error,
  };
};

export default useCheckStreakStatus;
