/* eslint-disable arrow-body-style */
import React, { useCallback, useEffect } from 'react';
import { View } from 'react-native';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import useFeedStore from '@/src/store/useFeedStore';
import { FlashList } from '@shopify/flash-list';
import EmptyComponent from '@/src/components/shared/EmptyComponent';
import _isEmpty from 'lodash/isEmpty';
import Loading from 'atoms/Loading';
import Header from 'shared/Header';
import ErrorView from 'atoms/ErrorView/ErrorView';
import styles from './styles';
import FeedCard from '../../components/FeedCard';

const Feed = () => {
  const { feeds, loading, fetchFeeds, error, updateLastReadFeedId, isRead } =
    useFeedStore((state) => ({
      feeds: state.feeds,
      loading: state.loading,
      fetchFeeds: state.fetchFeeds,
      error: state.error,
      updateLastReadFeedId: state.updateLastReadFeedId,
      isRead: state.isRead,
    }));

  const { isMobile: isCompactMode } = useMediaQuery();

  const renderFeed = useCallback(({ item }: any) => {
    return (
      <FeedCard
        feedId={item?._id}
        imageUrl={item?.imageUrl}
        isLiked={item?.isLiked}
        feedType={item?.feedType}
        feed={item?.feedData}
      />
    );
  }, []);

  useEffect(() => {
    if (feeds.length > 0 && !isRead) {
      updateLastReadFeedId();
    }
  }, [feeds, isRead]);

  if (loading) {
    return <Loading />;
  }
  if (!feeds && error) {
    return (
      <ErrorView
        errorMessage={error?.message ?? 'Failed to load notifications'}
      />
    );
  }

  if (_isEmpty(feeds)) {
    return (
      <View style={{ flex: 1 }}>
        <Header title="Feed" showBackInWeb />
        <EmptyComponent
          title="No Feed!"
          subTitle="There is no feed right now for you"
        />
      </View>
    );
  }

  return (
    <View
      style={[styles.container, !isCompactMode && { paddingHorizontal: 12 }]}
    >
      <Header title="Feed" showBackInWeb />
      <View style={{ paddingHorizontal: 12, flex: 1 }}>
        <FlashList
          estimatedItemSize={100}
          data={feeds}
          renderItem={renderFeed}
          onEndReached={fetchFeeds}
          onEndReachedThreshold={0.5}
          keyExtractor={(_, index) => `feed-${index}`}
          style={{ paddingHorizontal: 16 }}
          showsVerticalScrollIndicator={false}
          decelerationRate="normal"
        />
      </View>
    </View>
  );
};

export default Feed;
