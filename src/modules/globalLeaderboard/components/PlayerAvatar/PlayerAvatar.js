import React, { useCallback} from 'react';
import { View, Image, Text } from 'react-native';
import PropTypes from 'prop-types';
import FirstBGImage from '@/assets/images/backgrounds/leaderboard_first_bg.png';
import { getRatingForTitle } from '../../constants/leaderboardConstants';
import styles from './PlayerAvatar.style';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useRouter } from 'expo-router';
import userReader from '@/src/core/readers/userReader';

const PlayerAvatar = ({ rank, playerData, size = 60, title }) => {
  const user = playerData.user || playerData;
  const rating = title ? getRatingForTitle(title, user) : playerData.rating;
  const router = useRouter();

  const onPressUserCard = useCallback(() => {
    const username = userReader.username(user);
    router.navigate(`/profile/${username}`);
  }, [user, router]);

  return (
    <TouchableOpacity style={[styles.playerContainer]} onPress={onPressUserCard}>
      {rank === 1 && (
        <View style={styles.firstPlaceImageContainer}>
          <Image source={FirstBGImage} style={styles.firstPlaceImage} />
        </View>
      )}
      <View
        style={[
          styles.playerAvatarContainer,
          styles[`rank${rank}Container`],
          { width: size, height: size },
        ]}
      >
        <Image
          source={user.profileImageUrl ? { uri: user.profileImageUrl } : null}
          style={styles.playerImage}
        />
        <View style={[styles.rankBadge, styles[`rank${rank}Badge`]]}>
          <Text style={styles.rankText}>{rank}</Text>
        </View>
      </View>
      
      <Text style={styles.nameText}>{user.username}</Text>
      <Text style={styles.ratingText}>{rating}</Text>
    </TouchableOpacity>
  );
};

PlayerAvatar.propTypes = {
  rank: PropTypes.number.isRequired,
  playerData: PropTypes.oneOfType([
    PropTypes.shape({
      user: PropTypes.shape({
        username: PropTypes.string.isRequired,
        profileImageUrl: PropTypes.string,
      }).isRequired,
      rating: PropTypes.number,
    }),
    PropTypes.shape({
      username: PropTypes.string.isRequired,
      profileImageUrl: PropTypes.string,
      rating: PropTypes.number,
    }),
  ]).isRequired,
  size: PropTypes.number,
  title: PropTypes.string,
};

export default PlayerAvatar;