import React from 'react'
import { View, Text } from 'react-native'

import styles from './LeaderboardHeader.style'

const LeaderboardHeader = ({ headerItems, headerStyle, rankColumnStyle }) => {
    return (
        <View style={headerStyle}>
            <View style={styles.rowContainer}>
                <View style={[styles.rankColumn, rankColumnStyle]}>
                    <Text style={styles.headerLabelStyle}>
                        {headerItems[0]}
                    </Text>
                </View> 
                <View style={styles.profileInfoColumn}>
                    <Text style={styles.headerLabelStyle} numberOfLines={1}>
                        {headerItems[1]}
                    </Text>
                </View>
                <View style={styles.ratingColumn}>
                    <Text style={styles.headerLabelStyle}>
                        {headerItems[2]}
                    </Text>
                </View>
            </View>
        </View>
    )
}

export default React.memo(LeaderboardHeader)
