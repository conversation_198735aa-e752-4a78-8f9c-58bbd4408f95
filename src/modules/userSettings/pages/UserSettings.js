import React from 'react';
import { Switch, Text, View, ScrollView } from 'react-native';
import { useUserSettings } from 'core/contexts/UserSettingsContext';
import dark from 'core/constants/themes/dark';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import styles from './UserSettings.style';
import Header from '../../../components/shared/Header';
import play from '@/app/(app)/game/[id]/play';

const UserSettings = () => {
  const { userSettings, setSettings, useSettingsQueryMeta } = useUserSettings();

  if (useSettingsQueryMeta.loading) {
    return <Loading label="Loading User Settings..." />;
  }

  if (useSettingsQueryMeta.error)
    return <ErrorView errorMessage="Something went wrong." />;

  const handlePlaySound = () => {
    setSettings({
      ...userSettings,
      playSound: !userSettings.playSound
    });
  };

  const handleHapticFeedback = () => {
    setSettings({
      ...userSettings,
      hapticFeedback: !userSettings.hapticFeedback,
    });
  }

  const playSoundValue = Boolean(userSettings.playSound);
  const hapticFeedbackValue = Boolean(
    userSettings.hapticFeedback
  );
  return (
    <View style={styles.container}>
      <Header title="Settings" />
      <ScrollView
        style={{ flex: 1, paddingHorizontal: 16, paddingVertical: 16 }}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.expandedScrollContainer}
      >
        <View style={styles.playSoundContanier}>
          <Text style={styles.settingName}>Sounds</Text>
          <Switch
            thumbColor={dark.colors.textDark}
            trackColor={dark.colors.textDark}
            style={styles.switch}
            value={playSoundValue}
            onValueChange={handlePlaySound}
          />
        </View>
        <View style={styles.playSoundContanier}>
          <Text style={styles.settingName}>Haptic Feedback</Text>
          <Switch
            thumbColor={dark.colors.textDark}
            trackColor={dark.colors.textDark}
            style={styles.switch}
            value={hapticFeedbackValue}
            onValueChange={handleHapticFeedback}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default React.memo(UserSettings);
