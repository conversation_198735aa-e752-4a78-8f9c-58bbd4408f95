import { StyleSheet } from 'react-native';

import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    gap: 20,
  },
  playSoundContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 10,
    width: '100%',
    height: 50,
    backgroundColor: dark.colors.gradientBackground,
    borderRadius: 12,
    paddingHorizontal: 15,
    marginTop: 10,
  },
  settingName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: dark.colors.textDark,
  },
  compactScrollContainer: {
    marginHorizontal: 20,
    gap: 10,
  },
  switch: {
    marginLeft: 'auto',
  },
});

export default styles;
