import React, { useCallback, useEffect, useRef } from 'react';
import { FlatList, Keyboard, SafeAreaView, View } from 'react-native';
import { SearchBar } from '@rneui/base';
import _size from 'lodash/size';
import useSearchUsers from 'shared/users/hooks/useSearchUsers';
import SearchUserCard from 'modules/friendsAndFollowers/components/SearchUserCard';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Header from 'shared/Header';
import styles from './SearchMathLetes.style';

const SearchMathLetes = () => {
  const searchBarRef = useRef(null);
  const { loading, error, users, onSearchQueryChange, searchQuery } =
    useSearchUsers();

  const { isMobile: isCompactMode } = useMediaQuery();

  const renderSeparator = () => <View style={styles.separator} />;

  const renderRow = useCallback(
    ({ item }: { item: any }) => <SearchUserCard infoData={item} />,
    [],
  );

  useEffect(() => {
    if (searchBarRef.current) {
      setTimeout(() => {
        searchBarRef.current?.focus?.();
        searchBarRef.current?.input?.focus();
      }, 150);
    } else {
      Keyboard.dismiss();
    }
  }, []);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Header title="Search Mathlete" />
      <View
        style={{
          height: 40,
          width: '100%',
          marginTop: isCompactMode ? 0 : 16,
          paddingHorizontal: 16,
          marginBottom: 10,
        }}
      >
        <SearchBar
          ref={searchBarRef}
          placeholder="Search Mathlete (atleast 3 char)"
          onChangeText={onSearchQueryChange}
          containerStyle={styles.searchBarContainerStyle}
          inputContainerStyle={styles.inputContainerStyle}
          value={searchQuery}
          inputStyle={styles.inputStyle}
        />
      </View>

      <FlatList
        data={loading || _size(searchQuery) < 3 ? [] : users}
        style={styles.userListStyle}
        keyExtractor={(item) => item.id}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        renderItem={loading || _size(searchQuery) < 3 ? null : renderRow}
        ItemSeparatorComponent={renderSeparator}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      />
    </SafeAreaView>
  );
};

export default React.memo(SearchMathLetes);
