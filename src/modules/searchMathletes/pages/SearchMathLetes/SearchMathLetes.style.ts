import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  separator: {
    height: 1,
    width: '100%',
    backgroundColor: dark.colors.tertiary,
  },
  inputBoxContainer: {
    width: '100%',
    alignItems: 'center',
    height: 70,
  },
  searchBarContainerStyle: {
    flex: 1,
    width: '100%',
    backgroundColor: dark.colors.background,
    borderWidth: 2,
    padding: 0,
    borderRadius: 8,
    borderTopColor: dark.colors.tertiary,
    borderBottomColor: dark.colors.tertiary,
    height: 40,
    borderColor: dark.colors.tertiary,
  },
  inputContainerStyle: {
    outlineStyle: 'none',
    borderRadius: 8,
    backgroundColor: dark.colors.primary,
    height: 38,
  },
  inputStyle: {
    outlineStyle: 'none',
    color: 'white',
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    lineHeight: 20,
  },
  userListStyle: {
    marginTop: 0,
    paddingHorizontal: 16,
  },
});

export default styles;
