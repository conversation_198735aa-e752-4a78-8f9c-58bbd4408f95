import _isEmpty from 'lodash/isEmpty';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Platform } from 'react-native';
import { Asset } from 'expo-asset';
import BrainAnimationRiv from '@/assets/rive/brain_animation.riv';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { Redirect } from 'expo-router';
import Loading from 'atoms/Loading';
import CompactResolutionPledge from './Compact';
import ExpandedResolutionPledge from './Expanded';
import useHandleCommitGoal from '../../hooks/useHandleCommitGoal';
import useCheckIfPledgeTaken from '../../hooks/queries/checkIfPledgeTaken';
import { useSession } from '../../../auth/containers/AuthProvider';

const BRAIN_ANIMATION_RIV =
  'https://storage.googleapis.com/matiks-go/67174261bb5ac6690044e582_brain_animation.riv?timestamp=**********';

const ResolutionPledgePage = () => {
  const [selectedDuration, setSelectedDuration] = useState(10);

  const handleOnPledgeDurationChange = useCallback(
    ({ duration }) => {
      setSelectedDuration(duration);
    },
    [setSelectedDuration],
  );

  const { handleOnCommitGoalPressed, isCommitingGoalFuncInQueue } =
    useHandleCommitGoal();

  const onSubmitPressed = useCallback(() => {
    handleOnCommitGoalPressed({ duration: selectedDuration });
  }, [selectedDuration]);

  const { isMobile: isCompactMode } = useMediaQuery();

  const animationUrl = useMemo(() => {
    if (Platform.OS === 'web') {
      return Asset.fromModule(BrainAnimationRiv).uri;
    }
    return BRAIN_ANIMATION_RIV;
  }, []);

  const ComponentToBeRendered = isCompactMode
    ? CompactResolutionPledge
    : ExpandedResolutionPledge;

  return (
    <ComponentToBeRendered
      animationUrl={animationUrl}
      selectedDuration={selectedDuration}
      onDurationChange={handleOnPledgeDurationChange}
      isSubmiting={isCommitingGoalFuncInQueue}
      onSubmitPress={onSubmitPressed}
    />
  );
};

const ResolutionPledgeContainer = () => {
  const { session, isLoading, user } = useSession();

  const { checkIfPledgeTaken, loading } = useCheckIfPledgeTaken();

  const [isPledgeTaken, setIsPledgeTaken] = useState(false);

  useEffect(() => {
    if (session && !_isEmpty(user)) {
      checkIfPledgeTaken().then((data) => {
        setIsPledgeTaken(!_isEmpty(data?.getUserResolution));
      });
    }
  }, [session, user]);

  if (loading || isLoading) {
    return <Loading label="" />;
  }

  if (session && !_isEmpty(user) && isPledgeTaken) {
    return <Redirect href="/home" />;
  }

  return <ResolutionPledgePage />;
};

export default React.memo(ResolutionPledgeContainer);
