import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback, useEffect } from 'react';

import PrimaryButton from 'atoms/PrimaryButton';
import TertiaryButton from 'atoms/TertiaryButton';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import infoIcon from 'assets/images/infoBulb.png';
import RetakeIcon from 'assets/images/retake_icon.png';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useRouter } from 'expo-router';
import useFixYourRatingStyles from './FixYourRatingResult.style';
import useUpdateRatingBasedOnFixtureResponse from '../../hooks/graphql/useUpdateRatingFixture';
import { useSession } from '../../../auth/containers/AuthProvider';
import WithUserSubmissionFetcher from '../../container/WithUserSubmissionFetcher';
import { USER_RATING_FIXTURE_STANCE } from '../../constants/userRatingFixtureStance';
import {
  PAGE_NAME_KEY,
  PAGE_NAMES,
} from '../../../../core/constants/pageNames';

const FixYourRatingResult = ({ submission = EMPTY_OBJECT }) => {
  const { user } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();
  const styles = useFixYourRatingStyles();
  const { proposedRating } = submission;
  const router = useRouter();
  const { updateRatingBasedOnFixtureResponse, loading, error } =
    useUpdateRatingBasedOnFixtureResponse();

  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.FIX_MY_RATING_RESULT_PAGE);
    Analytics.track(
      ANALYTICS_EVENTS.FIX_MY_RATING.VIEWED_FIX_MY_RATING_RESULT_PAGE,
    );
  }, []);

  const handleRatingUpdate = useCallback(
    async (stance) => {
      try {
        const event =
          stance === USER_RATING_FIXTURE_STANCE.ACCEPTED
            ? ANALYTICS_EVENTS.FIX_MY_RATING
                .CLICKED_ON_MY_RATING_ACCEPT_NEW_RATING
            : ANALYTICS_EVENTS.FIX_MY_RATING
                .CLICKED_ON_MY_RATING_KEEP_OLD_RATING;
        Analytics.track(event, {
          [PAGE_NAME_KEY]: PAGE_NAMES.FIX_MY_RATING_RESULT_PAGE,
          oldRating: user.rating,
          proposedRating,
        });

        const result = await updateRatingBasedOnFixtureResponse(stance);
        // console.log('Rating update result:', result);
        if (result && result.hasFixedRating) {
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description:
              stance === USER_RATING_FIXTURE_STANCE.ACCEPTED
                ? 'Your rating has been successfully updated.'
                : 'Rejected the Proposed Rating.',
          });
          router.replace('/home');
        } else {
          // console.warn('hasFixedRating is not true, not navigating');
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Something went wrong, Try again.',
          });
        }
      } catch (err) {
        console.error('Error updating rating:', err);
        showToast({
          type: TOAST_TYPE.ERROR,
          description: 'Something went wrong, Try again.',
        });
      }
    },
    [updateRatingBasedOnFixtureResponse, router, user],
  );

  const onPressNewRating = useCallback(() => {
    showToast({
      type: TOAST_TYPE.LOADING,
      description: 'Updating New Rating...',
    });
    handleRatingUpdate(USER_RATING_FIXTURE_STANCE.ACCEPTED);
  }, [handleRatingUpdate]);

  const onPressOldRating = useCallback(() => {
    showToast({
      type: TOAST_TYPE.LOADING,
      description: 'Rejecting New Rating...',
    });
    handleRatingUpdate(USER_RATING_FIXTURE_STANCE.REJECTED);
  }, [handleRatingUpdate]);

  const canRetake = false;

  if (error) {
    return <Text>An error occurred: {error.message}</Text>;
  }

  return (
    <View style={styles.container}>
      <View
        style={isCompactMode ? styles.innerContentCompact : styles.innerContent}
      >
        <ScrollView
          style={styles.ratingUpdateInfoContainer}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          <Text style={styles.ratingUpdateLabel}>
            Your Matiks Rating is here!
          </Text>
          <Text style={styles.ratingUpdateValue}>{proposedRating}</Text>

          {canRetake ? (
            <View style={styles.retakeBox}>
              <View style={styles.retakeBoxContainer}>
                <Image source={RetakeIcon} style={styles.infoIcon} />
                <Text style={styles.infoText}>
                  Not satisfied with your attempt? You have one more attempt
                  left.
                </Text>
              </View>
              <View
                style={{ justifyContent: 'flex-end', alignItems: 'flex-end' }}
              >
                <TouchableOpacity>
                  <Text style={styles.retakeText}>Retake</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.infoContainer}>
              <Image source={infoIcon} style={styles.infoIcon} />
              <Text style={styles.infoText}>
                Outscore similar-skilled players in online duels to boost your
                rating!
              </Text>
            </View>
          )}
        </ScrollView>
        {}
        {!isCompactMode ? (
          <View style={styles.buttonsContainer}>
            <TertiaryButton
              label={`Keep old rating (${user.rating})`}
              onPress={onPressOldRating}
              radius={20}
              buttonStyle={{ width: '100%', height: 40 }}
              titleStyle={{ fontSize: 14 }}
            />
            <PrimaryButton
              label="Accept new rating"
              onPress={onPressNewRating}
              radius={20}
              buttonStyle={{ paddingHorizontal: 24 }}
            />
          </View>
        ) : (
          <View
            style={[
              styles.buttonsContainer,
              isCompactMode && styles.compactButtonContainer,
            ]}
          >
            <TertiaryButton
              label={`Keep old rating (${user.rating})`}
              onPress={onPressOldRating}
              radius={20}
              buttonStyle={{ width: '100%', height: 40 }}
              titleStyle={{ fontSize: 14 }}
            />
            <PrimaryButton
              label="Accept new rating"
              onPress={onPressNewRating}
              radius={20}
              buttonStyle={{ paddingHorizontal: 24 }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

export default WithUserSubmissionFetcher(FixYourRatingResult);
