import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import _isNil from 'lodash/isNil';
import { useRouter } from 'expo-router';
import { useIsFocused } from '@react-navigation/native';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import Rive from '@/src/components/atoms/Rive';
import RIVE_ANIMATIONS from '@/src/core/constants/riveAnimations';
import Header from 'shared/Header/Header';
import useWebsocketStore from 'store/useWebSocketStore';
import NetworkErrorOverlay, {
  NetworkErrorOverlayPlaceholder,
} from '@/src/components/shared/NetworkErrorOverlay';
import _toNumber from 'lodash/toNumber';
import { SEARCH_SPAM_BOT_DETECTED } from '@/src/core/constants/errors';
import useGoBack from '../../../../navigator/hooks/useGoBack';
import useAbortSearching from '../../hooks/mutations/useAbortSearching';
import { USER_EVENTS } from '../../constants/userEvents';
import useSearchPlayerMutation from '../../hooks/mutations/useSearchPlayerMutation';
import useSearchUserSubscription from '../../hooks/subscriptions/useSearchUserSubscription';
import styles from './SearchOpponent.style';
import SearchAgain from './SearchAgain';

const TRACKED_FOR_GAME_IDS = {};

const SearchOpponent = (props) => {
  const { timeLimit, gameType } = props;
  const router = useRouter();
  const { game, event } = useSearchUserSubscription();
  const { searchResult, startSearching } = useSearchPlayerMutation();
  const { abortSearching } = useAbortSearching();
  const timeoutIdRef = useRef(null);
  const searchTimeoutIdRef = useRef(null);
  const { isConnected } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
  }));

  const [isSearchTimedout, setIsSearchTimedout] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  const searchStartedRef = useRef(false);
  const gameAbortedRef = useRef(false);

  const { goBack } = useGoBack();

  const onPressAbortSearching = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.SEARCHING_FOR_MATHLETE.CLICKED_ON_ABORT_SEARCH,
    );
    if (!searchStartedRef.current) {
      goBack();
      return;
    }
    gameAbortedRef.current = true;
    abortSearching()
      .then((response) => {
        const { abortSearching: abortSearchingSuccess } =
          response?.data ?? EMPTY_OBJECT;
        if (abortSearchingSuccess) {
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description: 'Successfully aborted searching',
          });
          goBack();
        } else {
          // showToast({
          //     type: TOAST_TYPE.ERROR,
          //     description: 'Can\'t abort searching',
          // })
          goBack();
        }
      })
      .catch(() => {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: 'Something went wrong while aborting the game',
        });
        goBack();
      });
  }, [abortSearching, goBack]);

  const _startSearching = useCallback(
    ({ shouldWait = false } = EMPTY_OBJECT) => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
      setIsLoading(true);
      Analytics.track(ANALYTICS_EVENTS.VIEW_SEARCHING_FOR_OPPONENT, {
        timeLimit,
        gameType,
      });
      const waitTime = shouldWait ? 1500 : 0;
      timeoutIdRef.current = setTimeout(async () => {
        setIsLoading(false);
        setIsSearchTimedout(false);
        startSearching({
          timeLimit: _toNumber(timeLimit) * 60,
          gameType,
        }).catch((error) => {
          if (error?.message === SEARCH_SPAM_BOT_DETECTED) {
            hideToast();
            showToast({
              type: TOAST_TYPE.ERROR,
              description: 'Search spam detected, try again after some time',
            });
            router.replace('/home');
          }
        });
        if (searchTimeoutIdRef.current) {
          clearTimeout(searchTimeoutIdRef.current);
        }
        searchTimeoutIdRef.current = setTimeout(() => {
          setIsSearchTimedout(true);
        }, 15000);
        searchStartedRef.current = true;
      }, waitTime);
    },
    [startSearching, timeLimit, gameType],
  );

  useEffect(() => {
    _startSearching({ shouldWait: true });
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
      setIsLoading(false);
    };
  }, [_startSearching]);

  useEffect(() => {
    if (
      event === USER_EVENTS.USER_MATCHED &&
      !_isNil(game) &&
      searchStartedRef.current
    ) {
      const { _id, config } = game;

      if (!TRACKED_FOR_GAME_IDS[_id]) {
        Analytics.track(ANALYTICS_EVENTS.OPPONENT_FOUND, {
          gameId: _id,
          ...config,
        });
        TRACKED_FOR_GAME_IDS[_id] = true;
      }
      router.replace(`/game/${_id}/play`);
    }
  }, [router, event, game]);

  const abortSearchingRef = useRef(abortSearching);
  abortSearchingRef.current = abortSearching;

  useEffect(
    () => () => {
      if (!gameAbortedRef.current && searchStartedRef.current) {
        abortSearchingRef.current();
      }
    },
    [],
  );

  useEffect(() => {
    if (!isFocused) {
      if (!gameAbortedRef.current && searchStartedRef.current) {
        abortSearchingRef.current();
      }
    }
  }, [isFocused]);

  useEffect(() => {
    if (event === USER_EVENTS.SEARCH_TIMEOUT && !_isNil(searchResult)) {
      setIsSearchTimedout(true);
    }
  }, [event, searchResult]);

  if (isSearchTimedout) {
    return (
      <View style={{ flex: 1 }}>
        <Header />
        <SearchAgain isLoading={isLoading} searchAgain={_startSearching} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        {!isConnected ? (
          <NetworkErrorOverlay />
        ) : (
          <NetworkErrorOverlayPlaceholder />
        )}
        <Rive
          url={RIVE_ANIMATIONS.SEARCH_OPPONENT_ANIMATION}
          autoPlay
          style={{ width: 335, height: 350 }}
        />
      </View>
      <View style={{ position: 'absolute', bottom: 100 }}>
        <InteractivePrimaryButton
          label="Cancel Search"
          labelStyle={styles.cancelLabel}
          buttonStyle={styles.cancelButton}
          buttonBorderBackgroundStyle={styles.cancelBackground}
          onPress={onPressAbortSearching}
        />
      </View>
    </View>
  );
};

export default React.memo(SearchOpponent);
