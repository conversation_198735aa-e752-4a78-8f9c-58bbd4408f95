import { useEffect, useState } from 'react';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import _map from 'lodash/map';

import { USER_EVENTS } from '../../constants/userEvents';
import { useSession } from '../../../auth/containers/AuthProvider';
import { events, listenersNamespace } from '@/src/core/event/constants';
import EventManager from '@/src/core/event';

const useSearchUserSubscription = () => {
  const { user, userId } = useSession();
  const [event, setEvent] = useState(null);
  const [game, setGame] = useState(null);

  useEffect(() => {
    const eventManager = new EventManager();
    const subscription = eventManager.on(
      events.UserMatchedEvent,
      listenersNamespace.UserMatchedEvent,
      (payload) => {
        const _event = payload?.event;
        const _game = payload?.game;
        switch (_event) {
          case USER_EVENTS.USER_MATCHED: {
            const { encryptedQuestions } = _game ?? EMPTY_OBJECT;
            const questions = _map(encryptedQuestions, decryptJsonData);
            const gameData = { ..._game, questions };
            setGame(gameData);
            break;
          }
          case USER_EVENTS.SEARCH_TIMEOUT:
            setGame(null);
            break;
          default:
            setGame(_game);
        }
        setEvent(_event);
      },
    );
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { event, game };
};

export default useSearchUserSubscription;
