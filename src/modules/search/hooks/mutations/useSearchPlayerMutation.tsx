import { gql, useMutation } from '@apollo/client';
import { useCallback, useRef } from 'react';
import { handleAsync } from 'core/utils/asyncUtils';
import { GAME_TYPES } from '../../../home/<USER>/gameTypes';

const START_SEARCHING_MUTATION = gql`
  mutation StartSearching($gameConfig: GameConfigInput) {
    startSearching(gameConfig: $gameConfig)
  }
`;

const useSearchPlayerMutation = () => {
  const [startSearchingMutation, { data, loading, error }] = useMutation(
    START_SEARCHING_MUTATION,
  );
  const abortControllerRef = useRef<AbortController | null>(null);

  const startSearching = useCallback(
    async ({ numPlayers = 2, timeLimit, gameType } = EMPTY_OBJECT) => {
      const variables = {
        gameConfig: {
          numPlayers,
          timeLimit,
          gameType: gameType ?? GAME_TYPES.PLAY_ONLINE,
        },
      };
      if (abortControllerRef.current) {
        abortControllerRef.current?.abort();
      }
      abortControllerRef.current = new AbortController();
      return handleAsync(startSearchingMutation, {
        variables,
        context: {
          fetchOptions: {
            signal: abortControllerRef.current.signal,
          },
        },
      });
    },
    [startSearchingMutation],
  );

  return {
    searchResult: data?.startSearching,
    startSearching,
    loading,
    error,
  };
};

export default useSearchPlayerMutation;
