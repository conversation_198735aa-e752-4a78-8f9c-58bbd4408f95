import Header from '@/src/components/shared/Header';
import React, { useCallback } from 'react';
import { ScrollView, Text, View } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import _map from 'lodash/map';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import _isEmpty from 'lodash/isEmpty';
import PromotionIcon from '@/src/components/svg/Icons/PromotionIcon';
import DemotionIcon from '@/src/components/svg/Icons/DemotionIcon';
import _filter from 'lodash/filter';
import _size from 'lodash/size';
import { WEEKLY_LEAGUE_PROGRESS_STATE } from 'modules/matiksWeeklyLeague/constants/weeklyLeagueTypes';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import useGetWeeklyCoinsEarned from 'modules/profile/hooks/query/useGetWeeklyCoinsEarned';
import WeeklyLeagueHeaderTrailingComponent from 'modules/matiksWeeklyLeague/components/WeeklyLeagueHeaderTrailingComponent';
import useGetUserLeagueGroupLeaderboard from '../../hooks/queries/useGetUserLeagueGroupLeaderboard';
import WeeklyLeagueParticipantRow from '../../components/WeeklyLeagueParticipantRow';
import WeeklyLeagueInfoImages from '../../components/WeeklyLeagueInfoImages';
import styles from './MatiksWeeklyLeagueLeaderboard.style';
import AskToParticipateInWeeklyLeague, {
  MIN_COINS_NEEDED_FOR_PARTICIPATION,
} from '../AskToParticipateInWeeklyLeague';

const MatiksWeeklyLeagueLeaderboard = ({
  participants,
  currentUserLeague,
}: {
  participants: {
    user: {
      name: string;
      username: string;
      profileImageUrl: string;
      rating: number;
    };
    statikCoins: number;
    rank: number;
  }[];
  currentUserLeague: any;
}) => {
  const renderTrailingComponent = useCallback(
    () => <WeeklyLeagueHeaderTrailingComponent />,
    [],
  );

  const promotionZoneParticipants = _filter(
    participants,
    (participant) =>
      participant.progressState === WEEKLY_LEAGUE_PROGRESS_STATE.PROMOTION,
  );

  const regularZoneParticipants = _filter(
    participants,
    (participant) =>
      participant.progressState === WEEKLY_LEAGUE_PROGRESS_STATE.NO_CHANGE,
  );

  const demotionZoneParticipants = _filter(
    participants,
    (participant) =>
      participant.progressState === WEEKLY_LEAGUE_PROGRESS_STATE.DEMOTION,
  );

  return (
    <View style={styles.container}>
      <Header renderTrailingComponent={renderTrailingComponent} />
      <ScrollView
        contentContainerStyle={styles.innerContainer}
        showsVerticalScrollIndicator={false}
      >
        <View
          style={{
            width: '100%',
            borderBottomWidth: 1,
            borderBottomColor: dark.colors.tertiary,
            // paddingHorizontal: 16,
            paddingBottom: 20,
            marginBottom: 12,
          }}
        >
          <WeeklyLeagueInfoImages userLeague={currentUserLeague} />
        </View>

        {_map(promotionZoneParticipants, (participant, index) => (
          <WeeklyLeagueParticipantRow
            key={`promotion-${index}`}
            participant={participant}
            isInPromotionZone
          />
        ))}
        <View style={styles.promotionContainer}>
          <View style={styles.promotionIcon}>
            <PromotionIcon />
          </View>
          <Text style={styles.promotionText}>PROMOTION ZONE</Text>
          <View style={styles.promotionIcon}>
            <PromotionIcon />
          </View>
        </View>
        {_map(regularZoneParticipants, (participant, index) => (
          <WeeklyLeagueParticipantRow
            key={`regular-${index}`}
            participant={participant}
            isInPromotionZone={null}
          />
        ))}
        {_size(demotionZoneParticipants) > 0 && (
          <View style={styles.promotionContainer}>
            <View style={styles.promotionIcon}>
              <DemotionIcon />
            </View>
            <Text
              style={[
                styles.promotionText,
                { color: dark.colors.demotionText },
              ]}
            >
              DEMOTION ZONE
            </Text>
            <View style={styles.promotionIcon}>
              <DemotionIcon />
            </View>
          </View>
        )}
        {_map(demotionZoneParticipants, (participant, index) => (
          <WeeklyLeagueParticipantRow
            key={`demotion-${index}`}
            participant={participant}
            isInPromotionZone={false}
          />
        ))}
      </ScrollView>
    </View>
  );
};

const MatiksWeeklyLeagueLeaderboardContainer = () => {
  const { participants, loading, error, currentUserLeague } =
    useGetUserLeagueGroupLeaderboard();

  if (loading) {
    return <Loading label="Loading Leaderboard..." />;
  }

  if (error) {
    return (
      <AskToParticipateInWeeklyLeague currentUserLeague={currentUserLeague} />
    );
  }

  if (_isEmpty(participants)) {
    return <ErrorView errorMessage="No data available" />;
  }

  return (
    <MatiksWeeklyLeagueLeaderboard
      participants={participants}
      currentUserLeague={currentUserLeague}
    />
  );
};

const CheckIfUserHasParticipatedInWeeklyLeague = () => {
  const { user } = useSession();
  const currentUserLeague = userReader.userCurrLeagueInfo(user);
  const hasParticipated = userReader.userHasParticipatedInLeague(user);
  const { coinsEarned: coinsEarnedInCurrWeek } = useGetWeeklyCoinsEarned();

  const userFulfilledMinCoinsCondition =
    coinsEarnedInCurrWeek >= MIN_COINS_NEEDED_FOR_PARTICIPATION;

  if (_isEmpty(currentUserLeague) && userFulfilledMinCoinsCondition) {
    return (
      <MatiksWeeklyLeagueLeaderboardContainer
        currentUserLeague={currentUserLeague}
      />
    );
  }

  if (!hasParticipated) {
    return (
      <AskToParticipateInWeeklyLeague currentUserLeague={currentUserLeague} />
    );
  }

  return <MatiksWeeklyLeagueLeaderboardContainer />;
};

export default React.memo(CheckIfUserHasParticipatedInWeeklyLeague);
