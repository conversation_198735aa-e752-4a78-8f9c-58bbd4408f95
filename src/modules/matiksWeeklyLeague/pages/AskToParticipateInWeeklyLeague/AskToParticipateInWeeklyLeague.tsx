import React, { useCallback, useEffect } from 'react';
import { Image, Text, View } from 'react-native';
import Header from '@/src/components/shared/Header';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import { GAME_MODE } from 'modules/games/constants/gameModes';
import { router } from 'expo-router';
import useGetWeeklyCoinsEarned from '@/src/modules/profile/hooks/query/useGetWeeklyCoinsEarned';
import { useSession } from 'modules/auth/containers/AuthProvider';
import WeeklyLeagueHeaderTrailingComponent from 'modules/matiksWeeklyLeague/components/WeeklyLeagueHeaderTrailingComponent';
import styles from './AskToParticipateInWeeklyLeague.style';

export const MIN_COINS_NEEDED_FOR_PARTICIPATION = 25;

const AskToParticipateInWeeklyLeague = () => {
  const { refreshCurrentUser } = useSession();
  const { coinsEarned: coinsEarnedInCurrWeek } = useGetWeeklyCoinsEarned();

  const navigateToGamesMenu = useCallback(() => {
    router.push(`/games?gameMode=${GAME_MODE.BLITZ}`);
  }, []);

  const renderTrailingComponent = useCallback(
    () => <WeeklyLeagueHeaderTrailingComponent />,
    [],
  );

  useEffect(() => {
    refreshCurrentUser();
  }, []);

  return (
    <View style={styles.container}>
      <Header renderTrailingComponent={renderTrailingComponent} />
      <View style={{ flex: 1 }}>
        <View style={styles.lockedLeagueContainer}>
          <Image
            source={require('@/assets/images/weeklyLeague/lockedLeague.png')}
            style={styles.image}
          />
          <Text style={styles.matiksLeagueText}>MATIKS LEAGUE</Text>
        </View>

        <View style={styles.howToJoinInfoContainer}>
          <Image
            style={styles.earnCoinInfoImage}
            source={require('@/assets/images/weeklyLeague/kawaii-cat.png')}
          />
          <Text style={styles.howToJoinInfoText}>
            Earn at-least{' '}
            <Text style={styles.xpMinValueText}>
              {MIN_COINS_NEEDED_FOR_PARTICIPATION}XP{' '}
            </Text>
            to join this weeks leaderboard , You have earned{' '}
            <Text style={styles.xpMinValueText}>
              {coinsEarnedInCurrWeek} XP{' '}
            </Text>{' '}
            this week
          </Text>
        </View>

        <View style={styles.playNowContainer}>
          <InteractivePrimaryButton
            onPress={navigateToGamesMenu}
            label="PLAY NOW"
            buttonStyle={styles.playNowButton}
            buttonContainerStyle={styles.playNowButtonContainer}
            labelStyle={styles.playNowButtonText}
            buttonBorderBackgroundStyle={styles.playNowBackground}
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(AskToParticipateInWeeklyLeague);
