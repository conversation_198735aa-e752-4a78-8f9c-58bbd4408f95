import { Text, View } from 'react-native';
import TimerIcon from '@/src/components/svg/Icons/TimerIcon';
import dark from 'core/constants/themes/dark';
import React, { useMemo } from 'react';

const WeeklyLeagueHeaderTrailingComponent = () => {
  const timeLeftInfo = useMemo(() => {
    const now = new Date(getCurrentTime());

    const currentUtcDay = now.getUTCDay();
    const daysUntilEndOfWeek = currentUtcDay === 0 ? 0 : 7 - currentUtcDay;

    const endOfWeek = new Date(
      Date.UTC(
        now.getUTCFullYear(),
        now.getUTCMonth(),
        now.getUTCDate() + daysUntilEndOfWeek,
        23,
        59,
        59,
        999,
      ),
    );

    const timeDiffMs = endOfWeek.getTime() - now.getTime();

    const totalHours = Math.floor(timeDiffMs / (1000 * 60 * 60));
    const days = Math.floor(totalHours / 24);
    const hours = totalHours % 24;
    const minutes = Math.floor((timeDiffMs % (1000 * 60 * 60)) / (1000 * 60));

    return { days, hours, minutes, totalHours };
  }, []);

  const { timeText, unitText } = useMemo(() => {
    const { days, hours, minutes, totalHours } = timeLeftInfo;

    if (totalHours >= 24) {
      return {
        timeText: days,
        unitText: days === 1 ? 'day left' : 'days left',
      };
    }
    if (hours > 0) {
      return {
        timeText: hours,
        unitText: hours === 1 ? 'hour left' : 'hours left',
      };
    }
    return {
      timeText: minutes,
      unitText: minutes === 1 ? 'minute left' : 'minutes left',
    };
  }, [timeLeftInfo]);

  return (
    <View style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }}>
      <TimerIcon color={dark.colors.secondary} height={20} width={20} />
      <Text
        style={{
          color: dark.colors.secondary,
          fontFamily: 'Montserrat-600',
          fontSize: 14,
        }}
      >
        {`${timeText} ${unitText}`}
      </Text>
    </View>
  );
};

export default React.memo(WeeklyLeagueHeaderTrailingComponent);
