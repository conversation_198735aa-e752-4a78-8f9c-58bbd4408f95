import React, { useCallback } from 'react';
import { Image, Text, View } from 'react-native';
import {
  WEEKLY_LEAGUE_INFO,
  WEEKLY_LEAGUE_PROGRESS_STATE,
  WEEKLY_LEAGUE_TYPES_MAP,
} from 'modules/matiksWeeklyLeague/constants/weeklyLeagueTypes';
import dark from 'core/constants/themes/dark';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import { GAME_MODE } from 'modules/games/constants/gameModes';
import { router } from 'expo-router';
import DemotionStateBackground from '@/src/components/svg/BackgroudViews/DemotionStateBackground';
import PromotionStateBackground from '@/src/components/svg/BackgroudViews/PromotionStateBackground';
import RetainedStateBackground from '@/src/components/svg/BackgroudViews/RetainedStateBakground';
import useMediaQuery from 'core/hooks/useMediaQuery';
import BronzeLeagueText from '@/src/components/svg/Texts/BronzeLeagueText';
import SilverLeagueText from '@/src/components/svg/Texts/SilverLeagueText';
import GoldLeagueText from '@/src/components/svg/Texts/GoldLeagueText';
import DiamondLeagueText from '@/src/components/svg/Texts/DiamondLeagueText';
import RubyLeagueText from '@/src/components/svg/Texts/RubyLeagueText';
import styles from './PromotionOrDemotionInfoBottomSheet.style';

const getLeagueText = (leagueType: string) => {
  switch (leagueType) {
    case WEEKLY_LEAGUE_TYPES_MAP.BRONZE:
      return <BronzeLeagueText />;
    case WEEKLY_LEAGUE_TYPES_MAP.SILVER:
      return <SilverLeagueText />;
    case WEEKLY_LEAGUE_TYPES_MAP.GOLD:
      return <GoldLeagueText />;
    case WEEKLY_LEAGUE_TYPES_MAP.DIAMOND:
      return <DiamondLeagueText />;
    case WEEKLY_LEAGUE_TYPES_MAP.MATIKAN:
    case WEEKLY_LEAGUE_TYPES_MAP.RUBY:
      return <RubyLeagueText />;
    default:
      return <BronzeLeagueText />;
  }
};

const PromotionOrDemotionInfoBottomSheet = ({
  league,
  progressState,
  closeBottomSheet,
}: {
  league: string;
  progressState: string;
  closeBottomSheet: () => void;
}) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const leagueInfo = WEEKLY_LEAGUE_INFO[league] ?? EMPTY_OBJECT;

  const getPromotionDemotionOrStandardText = () => {
    if (progressState === WEEKLY_LEAGUE_PROGRESS_STATE.PROMOTION) {
      return 'Promoted to ';
    }
    if (progressState === WEEKLY_LEAGUE_PROGRESS_STATE.DEMOTION) {
      return 'Demoted to';
    }
    return "You're in";
  };

  const navigateToGamesMenu = useCallback(() => {
    closeBottomSheet();
    if (progressState === WEEKLY_LEAGUE_PROGRESS_STATE.DEMOTION) {
      router.push(`/games?gameMode=${GAME_MODE.BLITZ}`);
    } else {
      router.push(`/weekly-league`);
    }
  }, [closeBottomSheet, progressState]);

  const renderProgressStateImage = () => {
    if (progressState === WEEKLY_LEAGUE_PROGRESS_STATE.DEMOTION) {
      return (
        <View style={styles.imageOuterContainer}>
          <Image
            style={[
              styles.image,
              {
                transform: [
                  { rotateX: '45deg' }, // Rotate around the X-axis
                  { rotateY: '20deg' }, // Rotate around the Y-axis
                  { rotateZ: '10deg' }, // Rotate around the Z-axis
                ],
              },
            ]}
            source={leagueInfo?.image}
          />
          <View style={styles.progressStateBgStyle}>
            <DemotionStateBackground color={leagueInfo?.textColor} />
          </View>
        </View>
      );
    }

    if (progressState === WEEKLY_LEAGUE_PROGRESS_STATE.PROMOTION) {
      return (
        <View style={styles.imageOuterContainer}>
          <View style={styles.progressStateBgStyle}>
            <PromotionStateBackground color={leagueInfo?.textColor} />
          </View>
          <Image style={styles.image} source={leagueInfo?.image} />
        </View>
      );
    }

    return (
      <View style={[styles.imageOuterContainer]}>
        <View style={[styles.progressStateBgStyle]}>
          <RetainedStateBackground
            color={leagueInfo?.textColor}
            opacity={0.4}
          />
        </View>
        <Image
          style={[styles.image, { position: 'absolute', top: 55 }]}
          source={leagueInfo?.image}
        />
      </View>
    );
  };

  return (
    <View style={[styles.container, !isCompactMode && { minWidth: 320 }]}>
      {renderProgressStateImage()}
      <View style={{ flex: 1, gap: 40, width: '100%' }}>
        <View
          style={{ gap: 8, justifyContent: 'center', alignItems: 'center' }}
        >
          <Text
            style={[
              styles.promotioDemotionText,
              progressState === WEEKLY_LEAGUE_PROGRESS_STATE.DEMOTION && {
                color: dark.colors.demotionText,
              },
            ]}
          >
            {getPromotionDemotionOrStandardText()}
          </Text>
          {getLeagueText(league)}
        </View>

        <View
          style={{
            paddingHorizontal: 16,
            height: 80,
            paddingVertical: 14,
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
          }}
        >
          <InteractivePrimaryButton
            onPress={navigateToGamesMenu}
            label={
              progressState === WEEKLY_LEAGUE_PROGRESS_STATE.DEMOTION
                ? 'LETS WORK HARDER'
                : 'AWESOME'
            }
            buttonStyle={[
              styles.playNowButton,
              { borderColor: leagueInfo?.textColor },
            ]}
            buttonContainerStyle={styles.playNowButtonContainer}
            labelStyle={styles.playNowButtonText}
            buttonBorderBackgroundStyle={[
              styles.playNowBackground,
              { backgroundColor: leagueInfo?.textColor },
            ]}
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(PromotionOrDemotionInfoBottomSheet);
