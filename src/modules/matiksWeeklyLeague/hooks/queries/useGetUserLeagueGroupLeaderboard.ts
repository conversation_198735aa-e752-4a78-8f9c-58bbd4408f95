import { gql, useQuery } from '@apollo/client';
import _get from 'lodash/get';

const GET_USER_LEAGUE_GROUP_LEADERBOARD = gql`
  query GetUserLeagueGroupLeaderboard($pageSize: Int = 30) {
    getUserLeagueGroupLeaderboard(pageSize: $pageSize) {
      results {
        user {
          name
          username
          profileImageUrl
          rating
        }
        statikCoins
        rank
        progressState
      }
      currentUserLeague {
        league
        groupId
        updatedAt
        hasParticipated
        coinsTillLastWeek
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

const useGetUserLeagueGroupLeaderboard = () => {
  const { data, loading, error, refetch } = useQuery(
    GET_USER_LEAGUE_GROUP_LEADERBOARD,
    {
      fetchPolicy: 'network-only',
    },
  );

  const { getUserLeagueGroupLeaderboard: leaderboard } = data ?? EMPTY_OBJECT;

  const participants = _get(leaderboard, 'results', []);

  return {
    participants,
    currentUserLeague: _get(leaderboard, 'currentUserLeague', EMPTY_OBJECT),
    loading,
    error,
    refetchLeaderboard: refetch,
  };
};

export default useGetUserLeagueGroupLeaderboard;
