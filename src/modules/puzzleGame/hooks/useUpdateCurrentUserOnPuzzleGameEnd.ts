import { useCallback } from 'react';

import _find from 'lodash/find';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isNil';
import _slice from 'lodash/slice';
import _get from 'lodash/get';
import { PuzzleGame } from 'modules/puzzleGame/types/puzzleGame';
import userReader from 'core/readers/userReader';
import { useSession } from '../../auth/containers/AuthProvider';

const rankUpdatedForGames = {};

const useUpdateCurrentUserOnPuzzleGameEnd = () => {
  const { updateCurrentUser, userId: currentUserId, user } = useSession();

  const getCurrentUserStreak = useCallback(() => {
    if (_isEmpty(user)) {
      return;
    }

    const userStreaks = userReader.userStreaks(user) ?? EMPTY_OBJECT;

    const {
      currentStreak = 0,
      longestStreak = 0,
      lastSevenDays = [false, false, false, false, false, false, false],
    } = userStreaks ?? EMPTY_OBJECT;

    if (_size(lastSevenDays) < 7) {
      return;
    }

    if (!lastSevenDays[6]) {
      lastSevenDays[6] = true;
      return {
        currentStreak: currentStreak + 1,
        longestStreak: Math.max(currentStreak + 1, longestStreak),
        lastSevenDays: [..._slice(lastSevenDays, 0, 6), true],
      };
    }

    return userStreaks;
  }, [user]);

  const getUpdatedUserRating = useCallback(
    ({ game }: { game: PuzzleGame }) => {
      const { leaderBoard } = game;

      const currentPlayer = _find(
        leaderBoard,
        (player) => player.userId === currentUserId,
      );

      if (_isEmpty(currentPlayer)) return EMPTY_OBJECT;

      const { ratingChange } = currentPlayer;

      if (!ratingChange || ratingChange === 0) return;
      const updatedStats = { ...user?.stats, ngp: (user?.stats?.ngp ?? 0) + 1 };

      const userNewPuzzleRating =
        _get(user, 'ratingV2.puzzleRating', 1000) + ratingChange;

      return {
        ratingV2: {
          ..._get(user, 'ratingV2'),
          puzzleRating: userNewPuzzleRating,
        },
        stats: updatedStats,
      };
    },
    [currentUserId, user],
  );

  const updateUserOnGameEnd = useCallback(
    ({ game }) => {
      if (_isEmpty(game)) {
        return;
      }

      const { _id: gameId } = game;
      if (rankUpdatedForGames[gameId]) return;

      const updatedUserStreakObj = getCurrentUserStreak();
      const userRatingAndStats = getUpdatedUserRating({ game });

      updateCurrentUser({
        ...user,
        ...userRatingAndStats,
        userStreaks: updatedUserStreakObj,
      });
      rankUpdatedForGames[gameId] = true;
    },
    [updateCurrentUser, getCurrentUserStreak, getUpdatedUserRating, user],
  );

  const updateUserStreak = useCallback(async () => {
    const updatedUserStreakObj = getCurrentUserStreak();
    await updateCurrentUser({ ...user, userStreaks: updatedUserStreakObj });
  }, [getCurrentUserStreak, updateCurrentUser, user]);

  return {
    updateUserOnGameEnd,
    updateUserStreak,
  };
};

export default useUpdateCurrentUserOnPuzzleGameEnd;
