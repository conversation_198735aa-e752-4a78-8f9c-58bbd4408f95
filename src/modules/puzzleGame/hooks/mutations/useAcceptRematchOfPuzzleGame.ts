import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';
import { PUZZLE_GAME_FRAGMENT } from 'core/graphql/fragments/puzzleGame';

const ACCEPT_REMATCH_OF_PUZZLE_GAME_MUTATION = gql`
  ${PUZZLE_GAME_FRAGMENT}
  mutation AcceptRematch($gameId: ID!) {
    acceptRematchOfPuzzleGame(gameId: $gameId) {
      ...CorePuzzleGameFields
    }
  }
`;

const useAcceptRematchRequestOfPuzzleGame = () => {
  const [acceptRematchOfPuzzleGameQuery, { loading }] = useMutation(
    ACCEPT_REMATCH_OF_PUZZLE_GAME_MUTATION,
  );

  const acceptRematchRequestOfPuzzleGame = useCallback(
    ({ gameId }: { gameId: string }) =>
      acceptRematchOfPuzzleGameQuery({
        variables: {
          gameId: _toString(gameId),
        },
      }),
    [acceptRematchOfPuzzleGameQuery],
  );

  return {
    acceptRematchRequestOfPuzzleGame,
    isAcceptingRematch: loading,
  };
};

export default useAcceptRematchRequestOfPuzzleGame;
