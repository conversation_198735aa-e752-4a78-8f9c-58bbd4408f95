import { useMutation, gql } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';

const CANCEL_PUZZLE_GAME_MUTATION = gql`
  mutation Mutation($gameId: ID!) {
    cancelPuzzleGame(gameId: $gameId)
  }
`;

const useCancelPuzzleGameById = () => {
  const [cancelPuzzleGameQuery] = useMutation(CANCEL_PUZZLE_GAME_MUTATION);

  const cancelPuzzleGameById = useCallback(
    async ({ gameId }: { gameId: string }) => {
      const { data } = await cancelPuzzleGameQuery({
        variables: {
          gameId: _toString(gameId),
        },
      });

      return data?.cancelGame;
    },
    [cancelPuzzleGameQuery],
  );

  return {
    cancelPuzzleGameById,
  };
};

export default useCancelPuzzleGameById;
