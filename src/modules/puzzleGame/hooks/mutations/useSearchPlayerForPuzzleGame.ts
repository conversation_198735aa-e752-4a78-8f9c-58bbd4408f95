import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import { PUZZLE_GAME_DEFAULT_TIME_CONFIG } from 'modules/home/<USER>/puzzleGameTypes';
import { handleAsync } from 'core/utils/asyncUtils';

const START_SEARCHING_FOR_PUZZLE_GAME_MUTATION = gql`
  mutation StartSearchingOpponentForPuzzleGame(
    $gameConfig: PuzzleGameConfigInput
  ) {
    startSearchingForPuzzleGame(gameConfig: $gameConfig)
  }
`;

const useSearchPlayerForPuzzleGame = () => {
  const [startSearchingMutation, { data }] = useMutation(
    START_SEARCHING_FOR_PUZZLE_GAME_MUTATION,
  );

  const startSearching = useCallback(async () => {
    const variables = {
      gameConfig: {
        numPlayers: 2,
        timeLimit: PUZZLE_GAME_DEFAULT_TIME_CONFIG,
        gameType: 'CROSS_MATH_PUZZLE_DUEL',
      },
    };

    return handleAsync(startSearchingMutation, {
      variables,
    });
  }, [startSearchingMutation]);

  return {
    ...data?.startSearching,
    startSearching,
  };
};

export default useSearchPlayerForPuzzleGame;
