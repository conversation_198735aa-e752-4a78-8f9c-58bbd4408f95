import { useEffect, useState } from 'react';
import _map from 'lodash/map';
import { getPuzzleFromMinifiedString } from 'modules/puzzleGame/utils/getPuzzleFromMinifiedString';
import { USER_EVENTS } from 'modules/search/constants/userEvents';
import _isNil from 'lodash/isNil';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import { events, listenersNamespace } from '@/src/core/event/constants';
import EventManager from '@/src/core/event';

const useSearchUserForPuzzleGameEventSubscription = () => {
  const [event, setEvent] = useState(null);
  const [game, setGame] = useState(null);

  useEffect(() => {
    const eventManager = new EventManager();
    const subscription = eventManager.on(
      events.PuzzleGameEventWithOpponentOutput,
      listenersNamespace.PuzzleGameEventWithOpponentOutput,
      (payload) => {
        const _event = payload?.event;
        const _game = payload?.puzzleGame ?? EMPTY_OBJECT;
        switch (_event) {
          case USER_EVENTS.USER_MATCHED: {
            const { questions: stringifiedPuzzles } = _game ?? EMPTY_OBJECT;
            if (_isNil(stringifiedPuzzles)) {
              setGame(_game);
              break;
            }
            Analytics.track(ANALYTICS_EVENTS.PUZZLE_GAME.USER_MATCHED);
            const questions = _map(stringifiedPuzzles, (puzzle) =>
              getPuzzleFromMinifiedString(puzzle),
            );
            const gameData = { ..._game, questions };
            setGame(gameData);
            break;
          }
          case USER_EVENTS.SEARCH_TIMEOUT:
            setGame(null);
            break;
          default:
            setGame(_game);
        }
        setEvent(_event);
      },
    );
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { event, game };
};

export default useSearchUserForPuzzleGameEventSubscription;
