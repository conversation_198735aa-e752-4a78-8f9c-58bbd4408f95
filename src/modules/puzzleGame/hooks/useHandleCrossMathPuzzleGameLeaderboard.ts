import { useCallback, useMemo, useRef, useState } from 'react';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _find from 'lodash/find';
import _every from 'lodash/every';
import { useRouter } from 'expo-router';
import useCaptureView from 'core/hooks/useCaptureView';
import { closePopover } from 'molecules/Popover/Popover';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import { useSession } from '../../auth/containers/AuthProvider';
import useRequestRematchForPuzzleGame from './mutations/useRequestRematch';
import useGoBack from '../../../navigator/hooks/useGoBack';
import { PUZZLE_GAME_RESULT_STATUS } from '../constants/puzzleGame';
import usePuzzleGameContext from './usePuzzleGameContext';

const useHandleCrossMathPuzzleGameLeaderboard = () => {
  const { game, players, gameMeta } = usePuzzleGameContext();

  const { user } = useSession();

  const router = useRouter();
  const gameConfigRef = useRef();
  const { captureView } = useCaptureView();
  const { requestRematchForPuzzleGame: requestRematch } =
    useRequestRematchForPuzzleGame();
  const [isRequesting, setIsRequesting] = useState(false);
  const { goBackToHome } = useGoBack();

  const { config: gameConfig, leaderBoard, _id: gameId } = game;
  const { timeLimit } = gameConfig;

  const adaptedPlayers = useMemo(
    () =>
      _map(players, (player) => {
        const { _id: playerId } = player;
        const leaderBoardEntry = _find(
          leaderBoard,
          (entry) => entry?.userId === playerId,
        );
        const { ratingChange, rank, correct, totalPoints, statikCoinsEarned } =
          leaderBoardEntry ?? {};

        return {
          ...player,
          ratingChange,
          statikCoinsEarned,
          score: correct,
          isWinner: rank === 1,
        };
      }),
    [players, leaderBoard],
  );

  const currentPlayer = useMemo(
    () => _find(adaptedPlayers, (player) => player?._id === user?._id),
    [adaptedPlayers, user],
  );
  const isMatchTied = useMemo(() => {
    if (!adaptedPlayers?.length) return false;
    const player1Score = adaptedPlayers[0]?.score;
    return _every(adaptedPlayers, (player) => player?.score === player1Score);
  }, [adaptedPlayers]);

  const currentPlayerStatus = useMemo(() => {
    if (isMatchTied) return PUZZLE_GAME_RESULT_STATUS.TIE;
    return currentPlayer?.isWinner
      ? PUZZLE_GAME_RESULT_STATUS.WIN
      : PUZZLE_GAME_RESULT_STATUS.LOSS;
  }, [currentPlayer?.isWinner, isMatchTied]);

  const currentPlayerOriginalRating = useMemo(
    () =>
      _get(
        _find(game.players, (player) => player?.userId === user?._id),
        'rating',
      ),
    [game.players, user],
  );

  const currentPlayerOriginalStatikCoins = useMemo(
    () =>
      _get(
        _find(game.players, (player) => player?.userId === user?._id),
        'statikCoins',
        0,
      ),
    [game.players, user],
  );

  const player1 = useMemo(() => adaptedPlayers[0], [adaptedPlayers]);
  const player2 = useMemo(() => adaptedPlayers[1], [adaptedPlayers]);

  const navigateToNewGame = useCallback(() => {
    closePopover?.();
    Analytics.track(ANALYTICS_EVENTS.PUZZLE_GAME.CLICKED_ON_NEW_GAME);
    router.replace(`/puzzle/search-opponent`);
  }, [router]);

  const navigateToHome = useCallback(() => {
    closePopover?.();
    goBackToHome();
  }, [goBackToHome]);

  const onPressShareButton = useCallback(() => {
    let winningStatusMessage;
    if (currentPlayerStatus === PUZZLE_GAME_RESULT_STATUS.WIN) {
      winningStatusMessage = 'WON! the Mental Aptitude game on Matiks 😇';
    } else if (currentPlayerStatus === PUZZLE_GAME_RESULT_STATUS.TIE) {
      winningStatusMessage = 'TIED! in the Mental Aptitude game on Matiks 🤝';
    } else {
      winningStatusMessage = 'I am improving my Mental Aptitude 🧠 on Matiks';
    }
    const playWithMeMessage = 'Challenge me on https://www.matiks.in';
    const message = `${winningStatusMessage}, ${playWithMeMessage}`;
    captureView({ viewRef: gameConfigRef, message });
    Analytics.track(ANALYTICS_EVENTS.PUZZLE_GAME.CLICKED_ON_SHARE_RESULT);
  }, [captureView, currentPlayerStatus]);

  const rematchWithSamePlayer = useCallback(async () => {
    closePopover?.();
    Analytics.track(ANALYTICS_EVENTS.PUZZLE_GAME.CLICKED_ON_REMATCH_REQUEST);
    try {
      if (isRequesting) return;
      setIsRequesting(true);
      requestRematch({ gameId });
    } catch (e) {
      // console.log(`ERROR IS :- ${e}`);
    } finally {
      setIsRequesting(false);
    }
  }, [requestRematch, isRequesting, gameId]);

  const isCurrPlayerWinner = useMemo(
    () => currentPlayer?.isWinner,
    [currentPlayer],
  );

  return {
    game,
    currentPlayer,
    player1,
    player2,
    currentPlayerOriginalRating,
    currentPlayerOriginalStatikCoins,
    currentPlayerStatus,
    players,
    adaptedPlayers,
    isCurrPlayerWinner,
    isRequesting,
    timeLimit,
    gameMeta,
    navigateToHome,
    rematchWithSamePlayer,
    navigateToNewGame,
    onPressShareButton,
    isMatchTied,
  };
};

export default useHandleCrossMathPuzzleGameLeaderboard;
