import { useCallback, useEffect, useMemo, useRef } from 'react';
import { hideToast } from 'molecules/Toast';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _toNumber from 'lodash/toNumber';
import { useUserActivityContext } from 'core/contexts/UserActivityContext';
import puzzleGameReader from '@/src/core/readers/puzzleGameReader';
import _includes from 'lodash/includes';

import {
  PUZZLE_GAME_EVENTS,
  PUZZLE_GAME_STATUS,
} from 'modules/puzzleGame/constants/puzzleGame';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useUpdateCurrentUserOnPuzzleGameEnd from './useUpdateCurrentUserOnPuzzleGameEnd';
import useHandlePuzzleGameEvents from './useHandlePuzzleGameEvents';
import { getPuzzleFromMinifiedString } from '../utils/getPuzzleFromMinifiedString';
import useWebsocketStore from '@/src/store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';

const GAME_END_TRIGGERED_FOR_GAME_IDS: any = {};

const useGameEventSubscription = ({ gameId }: { gameId: string }) => {
  const { updateUserOnGameEnd } = useUpdateCurrentUserOnPuzzleGameEnd();
  const userActivityContextData = useUserActivityContext();
  const { updateActivity } = userActivityContextData ?? EMPTY_OBJECT;

  const updateUserOnGameEndRef = useRef(updateUserOnGameEnd);
  updateUserOnGameEndRef.current = updateUserOnGameEnd;

  const { isConnected, lastMessage, sendMessage, joinChannel, leaveChannel } =
    useWebsocketStore((state) => ({
      isConnected: state.isConnected,
      lastMessage: state.lastMessage,
      sendMessage: state.sendMessage,
      joinChannel: state.joinChannel,
      leaveChannel: state.leaveChannel,
    }));

  const channel = WEBSOCKET_CHANNELS.PuzzleGameEvents(gameId);

  const joinChannelRef = useRef(joinChannel);
  joinChannelRef.current = joinChannel;
  useEffect(() => {
    if (isConnected) {
      joinChannelRef?.current?.(channel);
    }
  }, [isConnected]);

  const submitAnswer = useCallback(
    (data: any) =>
      sendMessage?.({
        type: 'submitPuzzleGameAnswer',
        channel,
        data: {
          ...data,
        },
      }),
    [sendMessage],
  );

  const { game, event } = useMemo(() => {
    const data = lastMessage?.[channel];
    const game = data?.puzzleGame;
    const event = data?.event;
    return { event, game };
  }, [lastMessage?.[channel]]);

  const currentGameStatus = puzzleGameReader.gameStatus(game) ?? '';

  const isActiveGame = !_includes(
    [PUZZLE_GAME_STATUS.ENDED, PUZZLE_GAME_STATUS.CANCELLED],
    currentGameStatus,
  );

  const leaveChannelRef = useRef(leaveChannel);
  leaveChannelRef.current = leaveChannel;
  useEffect(() => {
    if (isConnected && isActiveGame) {
      joinChannelRef.current?.(channel);
    }
    if (!isActiveGame) {
      leaveChannelRef.current?.(channel);
    }
  }, [isConnected, isActiveGame]);

  const decryptedGame = useMemo(() => {
    if (_isEmpty(game)) {
      return game;
    }

    const { questions: stringifiedPuzzles } = game ?? EMPTY_OBJECT;

    if (_isNil(stringifiedPuzzles)) {
      return game;
    }

    const questions = _map(stringifiedPuzzles, (puzzle) =>
      getPuzzleFromMinifiedString(puzzle),
    );

    return { ...game, questions };
  }, [game]);

  const { handleUserJoinEvent, handleRemovePlayerEvent } =
    useHandlePuzzleGameEvents();

  const handleUserJoinEventRef = useRef(handleUserJoinEvent);
  handleUserJoinEventRef.current = handleUserJoinEvent;

  const handleRemovePlayerEventRef = useRef(handleRemovePlayerEvent);
  handleRemovePlayerEventRef.current = handleRemovePlayerEvent;

  const updateActivityRef = useRef(updateActivity);
  updateActivityRef.current = updateActivity;

  const gameRef = useRef(game);
  gameRef.current = game;

  useEffect(() => {
    const { config, gameType } = game ?? EMPTY_OBJECT;
    const { timeLimit } = config ?? EMPTY_OBJECT;

    switch (event) {
      case PUZZLE_GAME_EVENTS.USER_JOINED: {
        handleUserJoinEventRef.current({ game: gameRef.current });
        break;
      }
      case PUZZLE_GAME_EVENTS.GAME_STARTED: {
        hideToast();
        break;
      }
      case PUZZLE_GAME_EVENTS.GAME_ENDED: {
        if (GAME_END_TRIGGERED_FOR_GAME_IDS[gameId]) {
          return;
        }
        GAME_END_TRIGGERED_FOR_GAME_IDS[gameId] = true;
        Analytics.track(ANALYTICS_EVENTS.PUZZLE_GAME.GAME_ENDED);
        updateUserOnGameEndRef.current({ game: gameRef.current });
        updateActivityRef.current({
          activityType: `${gameType}`,
          duration: _toNumber(timeLimit) * 1000,
        });
        break;
      }
      case PUZZLE_GAME_EVENTS.PLAYER_REMOVED: {
        handleRemovePlayerEventRef.current({ game });
        break;
      }
      default: {
        // do nothing
      }
    }
  }, [event]);

  return {
    event,
    game: decryptedGame,
    submitAnswer,
  };
};

export default useGameEventSubscription;
