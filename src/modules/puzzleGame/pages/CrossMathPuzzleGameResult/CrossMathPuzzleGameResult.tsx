import React, { useEffect } from 'react';
import { Dimensions, Image, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import StatikCoinsIcon from 'assets/images/game/statikCoinWhite.png';
import RatingIcon from 'assets/images/game/ratingIconWhite.png';
import victoryBg from '@/assets/images/game/victoryGradient.png';
import defeatBg from '@/assets/images/game/defeatGradient.png';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import MetricsCard from '@/src/components/shared/MetricsCard';
import Loading from '@/src/components/atoms/Loading';
import puzzleGameReader from '../../../../core/readers/puzzleGameReader';

import usePuzzleGameContext, {
  WithPuzzleGameContext,
} from '../../hooks/usePuzzleGameContext';
import useHandleCrossMathPuzzleGameLeaderboard from '../../hooks/useHandleCrossMathPuzzleGameLeaderboard';
import { useCrossMathPuzzleGameResultOverlayStyles } from './CrossMathPuzzleGameResult.style';
import { PUZZLE_GAME_STATUS } from '../../constants/puzzleGame';
import CrossMathPuzzleGameResultHeader from '../../components/CrossMathPuzzleGameResultHeader';
import CrossMathPuzzleGameResultFooter from '../../components/CrossMathPuzzleGameResultFooter';
import CrossMathPuzzleGameResultPlayers from '../../components/CrossMathPuzzleGameResultPlayers';

const GAME_RESULT_SHOWN_TRACKED: any = {};

const CrossMathPuzzleGameResultOverlay = WithPuzzleGameContext(() => {
  const { isMobile } = useMediaQuery();
  const {
    game,
    players,
    currentPlayer,
    player1,
    player2,
    adaptedPlayers,
    currentPlayerOriginalRating,
    currentPlayerOriginalStatikCoins,
    isCurrPlayerWinner,
    rematchWithSamePlayer,
    navigateToNewGame,
    isMatchTied,
  } = useHandleCrossMathPuzzleGameLeaderboard();

  const { gameType, config } = game ?? EMPTY_OBJECT;
  const gameId = puzzleGameReader.id(game);

  useEffect(() => {
    if (!gameId) return;
    if (GAME_RESULT_SHOWN_TRACKED[gameId]) return;

    GAME_RESULT_SHOWN_TRACKED[gameId] = true;
    Analytics.track(ANALYTICS_EVENTS.RESULT_PAGE_SHOWN, {
      gameType,
      ...config,
    });
  }, [config, gameId, gameType]);

  const styles = useCrossMathPuzzleGameResultOverlayStyles();

  if (
    _size(players) === 1 ||
    _isEmpty(game) ||
    puzzleGameReader.gameStatus(game) !== PUZZLE_GAME_STATUS.ENDED
  ) {
    return null;
  }

  return (
    <View
      style={[
        {
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
          backgroundColor: 'transparent',
          overflow: 'hidden',
        },
        { flex: 1 },
      ]}
    >
      <View
        style={[
          styles.cardContainer,
          {
            width: Dimensions.get('window').width - 32,
          },
        ]}
      >
        {!isMatchTied && (
          <Image
            source={isCurrPlayerWinner ? victoryBg : defeatBg}
            style={styles.gradientBg}
          />
        )}

        <CrossMathPuzzleGameResultHeader
          gameId={game?.id}
          players={adaptedPlayers}
          isCurrentPlayerWinner={isCurrPlayerWinner}
          isMatchTied={isMatchTied}
        />
        <CrossMathPuzzleGameResultPlayers
          adaptedPlayers={adaptedPlayers}
          player1={player1}
          player2={player2}
          isCurrPlayerWinner={isCurrPlayerWinner}
          isMatchTied={isMatchTied}
        />
        <View style={styles.metricsContainer}>
          <MetricsCard
            title="RATING"
            value={currentPlayerOriginalRating}
            changeValue={currentPlayer.ratingChange}
            imageSource={RatingIcon}
            containerStyle={isMobile && { maxWidth: 200 }}
          />
          <MetricsCard
            title="STATIK COINS"
            value={currentPlayerOriginalStatikCoins}
            changeValue={currentPlayer.statikCoinsEarned}
            imageSource={StatikCoinsIcon}
            containerStyle={isMobile && { maxWidth: 200 }}
          />
        </View>

        <CrossMathPuzzleGameResultFooter
          rematchWithSamePlayer={rematchWithSamePlayer}
          navigateToNewGame={navigateToNewGame}
        />
      </View>
    </View>
  );
});

const CrossMathPuzzleGameResultContainer = () => {
  const gameLeaderboardData = useHandleCrossMathPuzzleGameLeaderboard();
  const { game, players } = usePuzzleGameContext();

  if (
    !gameLeaderboardData?.adaptedPlayers?.length ||
    _isEmpty(gameLeaderboardData) ||
    _isEmpty(gameLeaderboardData.adaptedPlayers)
  ) {
    return <Loading label="Loading game results..." />;
  }

  return <CrossMathPuzzleGameResultOverlay gameId={game?._id} />;
};

export default React.memo(CrossMathPuzzleGameResultContainer);
