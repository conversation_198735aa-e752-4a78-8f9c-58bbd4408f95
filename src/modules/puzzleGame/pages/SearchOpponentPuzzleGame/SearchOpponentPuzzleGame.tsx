import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import _toNumber from 'lodash/toNumber';
import _isNil from 'lodash/isNil';
import { useRouter } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import { useIsFocused } from '@react-navigation/native';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import Rive from '@/src/components/atoms/Rive';
import RIVE_ANIMATIONS from '@/src/core/constants/riveAnimations';
import { USER_EVENTS } from 'modules/search/constants/userEvents';
import useSearchPlayerForPuzzleGame from 'modules/puzzleGame/hooks/mutations/useSearchPlayerForPuzzleGame';
import useAbortSearchingForPuzzleGame from 'modules/puzzleGame/hooks/mutations/useAbortSearchingForPuzzleGame';
import useSearchUserForPuzzleGameEventSubscription from 'modules/puzzleGame/hooks/subscriptions/useSearchUserForPuzzleGameEventSubscription';
import NetworkErrorOverlay, {
  NetworkErrorOverlayPlaceholder,
} from '@/src/components/shared/NetworkErrorOverlay';
import Header from 'shared/Header/Header';
import SearchAgain from 'modules/search/pages/SearchOpponent/SearchAgain';
import { SEARCH_SPAM_BOT_DETECTED } from 'core/constants/errors';
import useWebsocketStore from '@/src/store/useWebSocketStore';
import useGoBack from '../../../../navigator/hooks/useGoBack';
import styles from './SearchOpponentPuzzleGame.style';

const TRACKED_FOR_GAME_IDS: any = {};
const SEARCH_TIMEOUT_DURATION = 15000; // 15 seconds

const SearchOpponentPuzzleGame = (props: any) => {
  const { timeLimit, gameType } = props;
  const router = useRouter();
  const { game, event } = useSearchUserForPuzzleGameEventSubscription();
  const { startSearching, searchResult } = useSearchPlayerForPuzzleGame();
  const { abortSearching } = useAbortSearchingForPuzzleGame();
  const { isConnected } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
  }));
  const isFocused = useIsFocused();

  // State
  const [isSearchTimedout, setIsSearchTimedout] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Refs
  const searchStartedRef = useRef(false);
  const gameAbortedRef = useRef(false);
  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);
  const searchTimeoutIdRef = useRef<NodeJS.Timeout | null>(null);

  const { goBack } = useGoBack();

  const _startSearching = useCallback(
    ({ shouldWait = false } = {}) => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
      setIsLoading(true);
      const waitTime = shouldWait ? 1500 : 0;

      timeoutIdRef.current = setTimeout(async () => {
        setIsLoading(false);
        setIsSearchTimedout(false);
        startSearching({
          timeLimit: _toNumber(timeLimit) * 60,
          gameType,
        }).catch((error) => {
          if (!_isNil(error) && error?.message === SEARCH_SPAM_BOT_DETECTED) {
            hideToast();
            showToast({
              type: TOAST_TYPE.ERROR,
              description: 'Search spam detected, try again after some time',
            });
            router.replace('/home');
          }
        });

        if (searchTimeoutIdRef.current) {
          clearTimeout(searchTimeoutIdRef.current);
        }
        searchTimeoutIdRef.current = setTimeout(() => {
          setIsSearchTimedout(true);
        }, SEARCH_TIMEOUT_DURATION);

        searchStartedRef.current = true;
      }, waitTime);
    },
    [startSearching, timeLimit, gameType],
  );

  const onPressAbortSearching = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PUZZLE_GAME.CLICKED_ON_ABORT_SEARCH);
    if (!searchStartedRef.current) {
      goBack();
      return;
    }
    gameAbortedRef.current = true;
    abortSearching()
      .then((response: any) => {
        const { abortSearching: abortSearchingSuccess } =
          response?.data ?? EMPTY_OBJECT;
        if (abortSearchingSuccess) {
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description: 'Successfully aborted searching',
          });
          goBack();
        } else {
          goBack();
        }
      })
      .catch(() => {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: 'Something went wrong while aborting the game',
        });
        goBack();
      });
  }, [abortSearching, goBack]);

  // Initial search start
  useEffect(() => {
    _startSearching({ shouldWait: true });
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
      if (searchTimeoutIdRef.current) {
        clearTimeout(searchTimeoutIdRef.current);
      }
      setIsLoading(false);
    };
  }, [_startSearching]);

  // Handle user matching
  useEffect(() => {
    if (
      event === USER_EVENTS.USER_MATCHED &&
      !_isNil(game) &&
      searchStartedRef.current
    ) {
      const { _id } = game;

      if (!TRACKED_FOR_GAME_IDS[_id]) {
        Analytics.track(ANALYTICS_EVENTS.PUZZLE_GAME_OPPONENT_FOUND, {
          gameId: _id,
        });
        TRACKED_FOR_GAME_IDS[_id] = true;
      }
      router.replace(`/puzzle-game/game/${_id}`);
    }
  }, [router, event, game]);

  // Handle search timeout
  useEffect(() => {
    if (event === USER_EVENTS.SEARCH_TIMEOUT && !_isNil(searchResult)) {
      setIsSearchTimedout(true);
    }
  }, [event, searchResult]);

  // Cleanup on unmount
  const abortSearchingRef = useRef(abortSearching);
  abortSearchingRef.current = abortSearching;

  useEffect(
    () => () => {
      if (!gameAbortedRef.current && searchStartedRef.current) {
        abortSearchingRef.current();
      }
    },
    [],
  );

  // Handle focus change
  useEffect(() => {
    if (!isFocused) {
      if (!gameAbortedRef.current && searchStartedRef.current) {
        abortSearchingRef.current();
      }
    }
  }, [isFocused]);

  if (isSearchTimedout) {
    return (
      <View style={{ flex: 1 }}>
        <Header />
        <SearchAgain isLoading={isLoading} searchAgain={_startSearching} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        {!isConnected ? (
          <NetworkErrorOverlay />
        ) : (
          <NetworkErrorOverlayPlaceholder />
        )}
        <Rive
          url={RIVE_ANIMATIONS.SEARCH_OPPONENT_ANIMATION}
          autoPlay
          style={{ width: 335, height: 350 }}
        />
      </View>
      <View style={{ position: 'absolute', bottom: 100 }}>
        <InteractivePrimaryButton
          label="Cancel Search"
          labelStyle={styles.cancelLabel}
          buttonStyle={styles.cancelButton}
          buttonBorderBackgroundStyle={styles.cancelBackground}
          onPress={onPressAbortSearching}
        />
      </View>
    </View>
  );
};

export default React.memo(SearchOpponentPuzzleGame);
