import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 18,
    width: '100%',
  },
  puzzlesSolvedText: {
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    color: 'white',
  },
  scoreText: {
    fontSize: 60,
    fontFamily: 'Montserrat-700',
    color: dark.colors.puzzle.primary,
  },
});

export default styles;
