import React, { useCallback, useEffect, useRef } from 'react';
import { Animated, Platform, View } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import victory from '@/assets/images/game/victory.png';
import defeat from '@/assets/images/game/defeat.png';
import tie from '@/assets/images/game/tie.png';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import _get from 'lodash/get';
import userReader from 'core/readers/userReader';
import useGoBack from '@/src/navigator/hooks/useGoBack';
import { useRouter } from 'expo-router';
import { openShareableCardFlow } from 'shared/ShareResultModal';
import ResultShareCard from 'shared/ResultShareCard';
import useHandleCrossMathPuzzleGameLeaderboard from 'modules/puzzleGame/hooks/useHandleCrossMathPuzzleGameLeaderboard';
import puzzleGameReader from 'core/readers/puzzleGameReader';
import styles from './CrossMathPuzzleGameResultHeader.style';

const CrossMathPuzzleGameResultHeader = ({
  isCurrentPlayerWinner,
  isMatchTied = false,
}: {
  isCurrentPlayerWinner: boolean;
  isMatchTied?: boolean;
}) => {
  const {
    game,
    players,
    player1,
    player2,
    adaptedPlayers,
    isCurrPlayerWinner,
  } = useHandleCrossMathPuzzleGameLeaderboard();

  const { gameType } = game ?? EMPTY_OBJECT;
  const puzzleConfig = puzzleGameReader.config(game);

  const timeLimit = _get(puzzleConfig, 'timeLimit', 120);

  const router = useRouter();

  const currentUserUsername = userReader.username(_get(players, [0]));
  const { goBack } = useGoBack();

  const linkToShare = `https://www.matiks.com/apps?utm_source=matiks_apps&utm_campaign=game_result_share&utm_referrer=${currentUserUsername}`;

  const { handleShare: handleShareLink } = useNativeUrlSharing({
    url: linkToShare,
  });

  const opponentPlayerUserName = userReader.username(_get(players, [1]));

  const handleShareInNative = useCallback(
    ({ message = '' } = EMPTY_OBJECT) => {
      openShareableCardFlow({
        renderResultCard: () => (
          <ResultShareCard
            gameType={gameType}
            adaptedPlayers={adaptedPlayers}
            isCurrPlayerWinner={isCurrPlayerWinner}
            player1Score={player1?.score}
            player2Score={player2?.score}
            timeLimit={timeLimit}
          />
        ),
        message,
        storyBackgroundColors: {
          backgroundBottomColor:
            dark.colors.puzzle.share.storyBackgroundColorBottom,
          backgroundTopColor: dark.colors.puzzle.share.storyBackgroundColorTop,
        },
      });
    },
    [
      adaptedPlayers,
      gameType,
      isCurrPlayerWinner,
      player1?.score,
      player2?.score,
      timeLimit,
    ],
  );

  const handleShareGameResult = useCallback(() => {
    const player1Score = _get(adaptedPlayers, [0, 'score']);
    const player2Score = _get(adaptedPlayers, [1, 'score']);
    let label;
    if (isMatchTied) {
      label = `What a match! 🤝 ${opponentPlayerUserName} and I tied with a score of ${player1Score} - ${player2Score}. Think you can break the tie? Challenge me at`;
    } else {
      label = isCurrentPlayerWinner
        ? `Victory is mine! 🎉 I beat ${opponentPlayerUserName} with a score of ${player1Score} - ${player2Score}. Think you can do better? Challenge me at`
        : `Well played ${opponentPlayerUserName}! 💪 Close match!. Wanna play a game with me? Challenge me at`;
    }
    if (Platform.OS === 'web') {
      handleShareLink({ label });
      return;
    }
    handleShareInNative({ message: `${label} ${linkToShare}` });
  }, [
    adaptedPlayers,
    isCurrentPlayerWinner,
    isMatchTied,
    opponentPlayerUserName,
    handleShareInNative,
    linkToShare,
    handleShareLink,
  ]);

  const imageOpacity = useRef(new Animated.Value(0.2)).current;

  useEffect(() => {
    Animated.timing(imageOpacity, {
      toValue: 1,
      duration: 800,
      delay: 200,
      useNativeDriver: true,
    }).start();
  }, [isCurrentPlayerWinner]);

  return (
    <View style={[styles.container]}>
      <InteractiveSecondaryButton
        onPress={goBack}
        iconConfig={{
          type: ICON_TYPES.MATERIAL_ICONS,
          name: 'close',
          size: 16,
          color: dark.colors.textLight,
        }}
        buttonContainerStyle={{ width: 40, height: 40 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
      <View
        style={[styles.imageContainer, (isCurrentPlayerWinner || isMatchTied) && { width: 180 }]}
      >
        <Animated.Image
          source={isMatchTied ? tie : (isCurrentPlayerWinner ? victory : defeat)}
          style={[styles.image, { opacity: imageOpacity }]}
          resizeMode="contain"
        />
      </View>
      <InteractiveSecondaryButton
        onPress={handleShareGameResult}
        iconConfig={{
          type: ICON_TYPES.ENTYPO,
          name: 'share',
          size: 16,
          color: dark.colors.textLight,
        }}
        buttonContainerStyle={{ width: 40, height: 40 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
    </View>
  );
};

export default React.memo(CrossMathPuzzleGameResultHeader);
