import React, { useMemo } from "react"
import { Platform, StyleSheet, View } from "react-native"
import useMediaQuery from "core/hooks/useMediaQuery"
import { PAGE_NAME_KEY, PAGE_NAMES } from "core/constants/pageNames"
import LeaguesListTab from "./components/LeaguesListTab"

const LeaguesHomePage = () => {
    const { isMobile: isCompactMode } = useMediaQuery()

    const styles = StyleSheet.create({
        container: {
            flex: 1,
        },
    })

    const eventProperties = useMemo(
        () => ({
            [PAGE_NAME_KEY]: PAGE_NAMES.LEAGUES,
        }),
        []
    )

    return (
        <View style={styles.container}>
            <LeaguesListTab />
        </View>
    )
}

export default React.memo(LeaguesHomePage)