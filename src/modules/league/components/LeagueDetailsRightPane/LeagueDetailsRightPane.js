import React, { useCallback, useMemo, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import groupIcon from 'assets/images/group.png';
import timerIcon from 'assets/images/timer.png';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { showRightPane } from 'molecules/RightPane/RightPane';
import userReader from 'core/readers/userReader';
import styles from './LeagueDetailsRightPane.style';
import { useSession } from '../../../auth/containers/AuthProvider';
import LeagueLeaderboardPage from '../LeagueLeaderboard';
import useJoinLeague from '../../hooks/mutation/useJoinLeague';
import FormModal from '../../../contest/components/FormModal';

const LeagueDetailsRightPane = ({ leagueDetails, refetchLeagueDetails }) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentTime = getCurrentTimeWithOffset();
  const { user } = useSession();
  const isGuest = _get(user, 'isGuest', false);

  const startTime = new Date(_get(leagueDetails, 'leagueStart', 0)).getTime();
  const endTime = new Date(_get(leagueDetails, 'leagueEnd', 0)).getTime();
  const leagueID = _get(leagueDetails, 'id');

  const canParticipateInTournament =
    userReader.canParticipateInTournaments(user);

  const currentUserParticipation = _get(
    leagueDetails,
    'currentUserParticipation',
    null,
  );
  const hasUserRegistered = !_isNil(currentUserParticipation);
  const isLive = currentTime >= startTime && currentTime <= endTime;
  const hasEnded = currentTime >= endTime;
  const shouldShowRegisterButton = !hasEnded && !hasUserRegistered;

  const leagueEndedAndUserIsRegistered = hasUserRegistered && hasEnded;
  const showLockedRegisterButtonForGuest =
    !canParticipateInTournament && !hasEnded;
  const leagueIsLiveAndNotEnded = !hasEnded && isLive;

  const formFields = _get(leagueDetails, ['registrationForm', 'fields']);

  const { joinLeague } = useJoinLeague();

  const onPressRegisterGuestUser = useCallback(() => {
    showToast({
      type: TOAST_TYPE.ERROR,
      description:
        'Guest Users or Users with less than 3 games played are not allowed to participate in League!',
    });
  }, []);

  const handleFormSubmit = useCallback(
    async (formData) => {
      if (isSubmitting) {
        return;
      }
      try {
        setIsSubmitting(true);
        await joinLeague({
          leagueId: leagueID,
          formData,
        });
        refetchLeagueDetails?.();
      } catch (e) {
        setIsSubmitting(false);
        showToast({
          type: TOAST_TYPE.ERROR,
          description: e.toString(),
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [leagueID, refetchLeagueDetails],
  );

  const handleJoinOrSubmitButtonPress = useCallback(async () => {
    if (!_isEmpty(formFields)) {
      setModalVisible(true);
      return;
    }
    await handleFormSubmit();
  }, [handleFormSubmit, formFields]);

  const navigateToLeagueLeaderboard = useCallback(() => {
    showRightPane({
      content: (
        <LeagueLeaderboardPage
          PAGE_SIZE={50}
          leagueDetails={{ id: leagueID }}
          key="League-Leaderboard-Page"
        />
      ),
    });
  }, [leagueID]);

  const renderJoinNowOrRegisterButton = useCallback(() => {
    if (showLockedRegisterButtonForGuest) {
      return (
        <TouchableOpacity
          style={styles.lockedRegisterButton}
          onPress={onPressRegisterGuestUser}
        >
          <FontAwesome name="lock" size={20} color={dark.colors.text} />
          <Text style={styles.lockedRegisterText}>Register now</Text>
        </TouchableOpacity>
      );
    }

    if (shouldShowRegisterButton) {
      return (
        <TouchableOpacity
          onPress={handleJoinOrSubmitButtonPress}
          style={styles.registerButton}
        >
          <Text style={styles.registerText}>
            {isSubmitting ? 'Registering...' : 'Register Now'}
          </Text>
        </TouchableOpacity>
      );
    }

    // if (isLive || hasEnded) {
    //     return (
    //         <TouchableOpacity style={styles.registerButton} onPress={navigateToLeagueLeaderboard}>
    //             <Text style={styles.registerText}>View Leaderboard</Text>
    //         </TouchableOpacity>
    //     )
    // }
    return null;
  }, [
    isGuest,
    leagueEndedAndUserIsRegistered,
    shouldShowRegisterButton,
    isLive,
    hasEnded,
  ]);

  const LeagueTimer = useCallback(() => {
    const timeLeftToStart = useCountDownTimer({ targetTime: startTime });
    const timeLeftToEnd = useCountDownTimer({ targetTime: endTime });
    const leagueStartOrEndTimeLeftLabel = leagueIsLiveAndNotEnded
      ? 'League Ends in '
      : 'League Starts in ';
    const timeToShow = leagueIsLiveAndNotEnded
      ? timeLeftToEnd
      : timeLeftToStart;

    if (hasEnded) {
      return null;
    }

    return (
      <View style={styles.infoItem}>
        <View style={styles.iconContainer}>
          <Image source={timerIcon} style={styles.iconStyle} />
        </View>
        <View style={styles.info}>
          <Text style={styles.infoText}>{leagueStartOrEndTimeLeftLabel}</Text>
          <Text style={styles.infoNumber}>{timeToShow}</Text>
        </View>
      </View>
    );
  }, [startTime, endTime, leagueIsLiveAndNotEnded, hasEnded]);

  const RegistrationInfo = useMemo(
    () => (
      <View style={styles.infoContainer}>
        <View style={styles.infoItem}>
          <View style={styles.iconContainer}>
            <Image source={groupIcon} style={styles.iconStyle} />
          </View>
          <View style={styles.info}>
            <Text style={styles.infoText}>Registered</Text>
            <Text style={styles.infoNumber}>
              {leagueDetails?.registrationCount}
            </Text>
          </View>
        </View>
        <LeagueTimer />
        <View style={{ marginTop: 20, width: '100%' }}>
          {renderJoinNowOrRegisterButton()}
        </View>
        {!_isEmpty(formFields) && (
          <FormModal
            modalVisible={isModalVisible}
            setModalVisible={setModalVisible}
            fields={formFields}
            onSubmit={handleFormSubmit}
            isLoading={isSubmitting}
          />
        )}
      </View>
    ),
    [
      leagueDetails,
      renderJoinNowOrRegisterButton,
      isModalVisible,
      isSubmitting,
      isLive,
      hasEnded,
    ],
  );

  return <View style={{ gap: 16 }}>{RegistrationInfo}</View>;
};

export default React.memo(LeagueDetailsRightPane);
