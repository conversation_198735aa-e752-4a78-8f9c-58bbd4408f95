import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Text, View } from 'react-native'
import useGetLeagueLeaderboard from 'modules/league/hooks/queries/useGetLeagueLeaderboard'
import useMediaQuery from 'core/hooks/useMediaQuery'
import LeaderboardParticipationRow from 'modules/league/components/LeaderboardParticipationRow'
import styles from 'modules/league/components/LeagueLeaderboard/LeagueLeaderboardPage.style'
import dark from 'core/constants/themes/dark'
import PlaceholderRow from 'shared/PlaceholderRow'
import _map from 'lodash/map'
import { useLocalSearchParams } from 'expo-router'
import { TAB_KEYS } from 'modules/league/constants/leagueDetails'
import _isEmpty from 'lodash/isEmpty'
import _get from 'lodash/get'
import { ScrollView } from 'tamagui'

const PAGE_SIZE = 10

const LeagueTop10Leaderboard = ({
  leagueId,
  leagueDetails,
}: {
  leagueId: string
  leagueDetails: any
}) => {
  const [expandedRowId, setExpandedRowId] = useState(null)

  const { fetchLeagueLeaderboard } = useGetLeagueLeaderboard({
    leagueId,
    pageSize: PAGE_SIZE,
  })

  const [isLoadingTop10Leaderboard, setIsLoadingTop10Leaderboard] =
    useState(true)

  const [participants, setParticipants] = useState([])

  const { tab: activeTab } = useLocalSearchParams() ?? EMPTY_OBJECT
  const isLeagueLeaderboardTabActive = activeTab === TAB_KEYS.RESULT

  const loadData = useCallback(async () => {
    if (!isLeagueLeaderboardTabActive) {
      return
    }
    setIsLoadingTop10Leaderboard(true)
    const response = await fetchLeagueLeaderboard({ pageNumber: 1 })
    const { data } = response
    const { getLeagueLeaderboard: participantsObject } = data ?? EMPTY_OBJECT
    const { participants, totalCount } = participantsObject ?? EMPTY_OBJECT
    setParticipants(participants ?? EMPTY_ARRAY)
    setIsLoadingTop10Leaderboard(false)
  }, [fetchLeagueLeaderboard, isLeagueLeaderboardTabActive])

  const { isMobile: isCompactMode } = useMediaQuery()

  const loadTop10DataRef = useRef(loadData)
  loadTop10DataRef.current = loadData

  useEffect(() => {
    loadTop10DataRef.current()
  }, [activeTab])

  const handleRowExpand = useCallback((rowId) => {
    setExpandedRowId((prevId) => (prevId === rowId ? null : rowId))
  }, [])

  const renderParticipant = ({ item, index, containerStyle }) => {
    const rowId = `${item?.user?.username.toString()}-${index}`
    const isExpanded = expandedRowId === rowId

    return (
      <LeaderboardParticipationRow
        item={item}
        isExpanded={isExpanded}
        onToggle={() => handleRowExpand(rowId)}
        containerStyle={containerStyle}
      />
    )
  }

  const renderHeader = () => (
    <View style={styles.row}>
      <Text style={[styles.rank, { color: dark.colors.textDark }]}>#</Text>
      <Text
        style={[styles.name, { color: dark.colors.textDark, flex: 3 }]}
        numberOfLines={1}
      >
        Name
      </Text>
      <Text style={[styles.score, { color: dark.colors.textDark, flex: 3 }]}>
        Statik Coins
      </Text>
    </View>
  )

  if (!isLeagueLeaderboardTabActive || isLoadingTop10Leaderboard) {
    return (
      <View style={{ paddingHorizontal: 16 }}>
        <PlaceholderRow />
        <PlaceholderRow />
        <PlaceholderRow />
      </View>
    )
  }

  if (_isEmpty(participants)) {
    return (
      <View>
        <Text style={{ color: 'white', fontFamily: 'Montserrat-400' }}>
          {' '}
          No data available
        </Text>
      </View>
    )
  }

  return (
    <View style={{ flex: 1, width: '100%', paddingHorizontal: 16 }}>
      {renderHeader?.()}
      {renderParticipant({
        item: _get(leagueDetails, 'currentUserResult'),
        index: 0,
        containerStyle: {
          backgroundColor: dark.colors.gradientBackground,
        },
      })}
      <ScrollView
        showsVerticalScrollIndicator={false}
      >
        {_map(participants, (item, index) =>
          renderParticipant({ item, index, containerStyle: {} })
        )}
        <View style={{ height: 80 }} />
      </ScrollView>
    </View>
  )
}

export default React.memo(LeagueTop10Leaderboard)
