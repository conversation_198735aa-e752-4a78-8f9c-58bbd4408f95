import _get from 'lodash/get';
import React, { useCallback, useState } from 'react';
import { Text, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import PaginatedList from 'shared/PaginatedList';
import Header from 'shared/Header';
import PlaceholderRow from 'shared/PlaceholderRow';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useLocalSearchParams } from 'expo-router';
import { TAB_KEYS } from 'modules/league/constants/leagueDetails';
import useGetLeagueLeaderboard from '../../hooks/queries/useGetLeagueLeaderboard';
import styles from './LeagueLeaderboardPage.style';
import LeaderboardParticipationRow from '../LeaderboardParticipationRow';

const LeagueLeaderboardPage = ({
  leagueDetails,
  PAGE_SIZE,
  refetchLeagueDetails,
}) => {
  const leagueId = _get(leagueDetails, 'id');
  const [expandedRowId, setExpandedRowId] = useState(null);

  const { fetchLeagueLeaderboard } = useGetLeagueLeaderboard({
    leagueId,
    pageSize: PAGE_SIZE,
  });

  const { isMobile: isCompactMode } = useMediaQuery();
  const { tab: activeTab } = useLocalSearchParams() ?? EMPTY_OBJECT;
  const isLeagueLeaderboardTabActive = activeTab === TAB_KEYS.RESULT;

  const fetchData = useCallback(
    async ({ pageNumber }) => {
      const response = await fetchLeagueLeaderboard({ pageNumber });
      const { data } = response;
      const { getLeagueLeaderboard: participantsObject } = data;
      const { participants, totalCount } = participantsObject;
      return { data: participants, totalItems: totalCount };
    },
    [fetchLeagueLeaderboard],
  );

  const handleRowExpand = useCallback((rowId) => {
    setExpandedRowId((prevId) => (prevId === rowId ? null : rowId));
  }, []);

  const renderParticipant = useCallback(
    ({ item, index, containerStyle }) => {
      const rowId = `${item?.user?.username.toString()}-${index}`;
      const isExpanded = expandedRowId === rowId;

      return (
        <LeaderboardParticipationRow
          item={item}
          isExpanded={isExpanded}
          onToggle={() => handleRowExpand(rowId)}
          containerStyle={containerStyle}
        />
      );
    },
    [expandedRowId, handleRowExpand],
  );

  const renderHeader = useCallback(
    () => (
      <View style={styles.row}>
        <Text style={[styles.rank, { color: dark.colors.textDark }]}>#</Text>
        <Text
          style={[styles.name, { color: dark.colors.textDark, flex: 3 }]}
          numberOfLines={1}
        >
          Name
        </Text>
        <Text style={[styles.score, { color: dark.colors.textDark, flex: 3 }]}>
          Statik Coins
        </Text>
      </View>
    ),
    [],
  );

  const renderHeaderWithCurrUserResult = useCallback(
    ({ page }) => {
      if (page === 1) {
        return (
          <View>
            {renderHeader?.()}
            {renderParticipant({
              item: _get(leagueDetails, 'currentUserResult'),
              index: 0,
              containerStyle: {
                backgroundColor: dark.colors.gradientBackground,
              },
            })}
          </View>
        );
      }
      return renderHeader?.();
    },
    [renderParticipant, leagueDetails],
  );

  const rendedPlaceHolder = useCallback(
    () => (
      <View>
        <PlaceholderRow />
        <PlaceholderRow />
        <PlaceholderRow />
      </View>
    ),
    [],
  );

  if (!isLeagueLeaderboardTabActive && !isCompactMode) {
    return <View style={{ paddingHorizontal: 16 }}>{rendedPlaceHolder()}</View>;
  }

  return (
    <View style={{ flex: 1, width: '100%' }}>
      {PAGE_SIZE > 10 && isCompactMode && <Header title="Leaderboard" />}
      <View style={{ paddingHorizontal: 16, flex: 1 }}>
        <PaginatedList
          placeholderComponent={rendedPlaceHolder}
          fetchData={fetchData}
          renderItem={renderParticipant}
          renderHeader={renderHeaderWithCurrUserResult}
          pageSize={PAGE_SIZE}
          keyExtractor={(item, index) =>
            `${item.user?.username.toString()}-${index}`
          }
          contentContainerStyle={styles.list}
          listFooterComponent={<View style={{ height: 80 }} />}
          pullToRefreshEnabled
        />
      </View>
    </View>
  );
};

export default React.memo(LeagueLeaderboardPage);
