import React, { useCallback, useState } from 'react';
import { Dimensions, Text, TouchableOpacity, View } from 'react-native';
import dark from 'core/constants/themes/dark';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { router } from 'expo-router';
import userReader from 'core/readers/userReader';
import useJoinLeague from '../../hooks/mutation/useJoinLeague';
import FormModal from '../../../contest/components/FormModal';
import styles from './CompactLeagueButtonView.style';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from '@/src/components/atoms/Icon';

const CompactLeagueButtonView = ({ leagueDetails, refetchLeagueDetails }) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentTime = getCurrentTimeWithOffset();
  const { user } = useSession();
  const isGuest = _get(user, 'isGuest', false);
  const leagueID = _get(leagueDetails, 'id');

  const startTime = new Date(_get(leagueDetails, 'leagueStart', 0)).getTime();
  const endTime = new Date(_get(leagueDetails, 'leagueEnd', 0)).getTime();

  const canParticipateInTournament =
    userReader.canParticipateInTournaments(user);

  const currentUserParticipation = _get(
    leagueDetails,
    'currentUserParticipation',
    null,
  );
  const hasUserRegistered = !_isNil(currentUserParticipation);
  const isLive = currentTime >= startTime && currentTime <= endTime;
  const hasEnded = currentTime >= endTime;
  const shouldShowRegisterButton = !hasEnded && !hasUserRegistered;

  const leagueEndedAndUserIsRegistered = hasUserRegistered && hasEnded;
  const showLockedRegisterButtonForGuest =
    !canParticipateInTournament && !hasEnded;

  const formFields = _get(leagueDetails, ['registrationForm', 'fields']);

  const { joinLeague } = useJoinLeague();

  const onPressRegisterGuestUser = useCallback(() => {
    showToast({
      type: TOAST_TYPE.ERROR,
      description:
        'Guest Users or Users with less than 3 games played are not allowed to participate in League!',
    });
  }, []);

  const handleFormSubmit = useCallback(
    async (formData) => {
      if (isSubmitting) {
        return;
      }
      try {
        setIsSubmitting(true);
        await joinLeague({
          leagueId: leagueID,
          formData,
        });
        refetchLeagueDetails?.();
      } catch (e) {
        setIsSubmitting(false);
        showToast({
          type: TOAST_TYPE.ERROR,
          description: e.toString(),
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [leagueID, refetchLeagueDetails],
  );

  const handleJoinOrSubmitButtonPress = useCallback(async () => {
    if (!_isEmpty(formFields)) {
      setModalVisible(true);
      return;
    }
    await handleFormSubmit();
  }, [handleFormSubmit, formFields]);

  const navigateToLeagueLeaderboard = useCallback(() => {
    router.push(`/league/leaderboard?id=${leagueID}`);
  }, [leagueID]);

  const renderJoinNowOrRegisterButton = useCallback(() => {
    if (showLockedRegisterButtonForGuest) {
      return (
        <InteractiveSecondaryButton
          label='Register Now'
          onPress={onPressRegisterGuestUser}
          iconConfig={{
            name: 'lock',
            type: ICON_TYPES.FONT_AWESOME,
            size: 20,
            color: dark.colors.text
          }}
          labelStyle={styles.lockedRegisterText}
          buttonStyle={styles.lockedRegisterButton}
          borderColor={dark.colors.primary}
          buttonBackgroundStyle={{backgroundColor: dark.colors.primary}}
          buttonContentStyle={{height: 50, width: '100%'}}
        />
      );
    }

    if (shouldShowRegisterButton) {
      return (
         <InteractiveSecondaryButton
          label={isSubmitting ? 'Registering...' : 'Register Now'}
          labelStyle={styles.registerText}
          buttonStyle={styles.registerButton}
          borderColor={dark.colors.secondary}
          buttonBackgroundStyle={{backgroundColor: dark.colors.tertiary, height: 20}}
          buttonContentStyle={{height: 50, width: '100%'}}
          buttonContainerStyle={{height: 40}}
          onPress={handleJoinOrSubmitButtonPress}
        />
      );
    }

    if (isLive || hasEnded) {
      return (
        <TouchableOpacity
          style={styles.registerButton}
          onPress={navigateToLeagueLeaderboard}
        >
          <Text style={styles.registerText}>View Leaderboard</Text>
        </TouchableOpacity>
      );
    }

    return null;
  }, [
    isGuest,
    leagueEndedAndUserIsRegistered,
    shouldShowRegisterButton,
    isLive,
    hasEnded,
  ]);

  return (
    <View style={{ marginTop: 20, width: Dimensions.get('window').width - 32 }}>
      {renderJoinNowOrRegisterButton()}
      {!_isEmpty(formFields) && (
        <FormModal
          modalVisible={isModalVisible}
          setModalVisible={setModalVisible}
          fields={formFields}
          onSubmit={handleFormSubmit}
          isLoading={isSubmitting}
        />
      )}
    </View>
  );
};

export default React.memo(CompactLeagueButtonView);
