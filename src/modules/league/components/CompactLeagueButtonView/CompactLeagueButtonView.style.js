import { StyleSheet } from "react-native"
import dark from "core/constants/themes/dark"

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'transparent',
        padding: 20,
    },
    iconContainer: {
        backgroundColor: dark.colors.tertiary,
        padding: 10,
        borderRadius: 10
    },
    iconStyle: {
        height: 20,
        width: 20
    },
    infoContainer: {
        flexDirection: 'column',
        alignItems: 'flex-start',
        gap: 15,
        paddingHorizontal: 20,
        marginLeft:31,
        backgroundColor: 'transparent',
        paddingVertical: 20,
        borderWidth: 2,
        borderColor: dark.colors.tertiary,
        borderRadius: 10,
    },
    infoItem: {
        display: 'flex',
        gap: 10,
        flexDirection: 'row',
        alignItems: 'center',
    },
    icon: {
        fontSize: 24,
        color: '#FFF',
    },
    info: {
        gap: 4,
        display: 'flex',
        alignContent: 'center',
        justifyContent:'center',
        flexDirection: 'column'
    },
    infoText: {
        fontFamily: 'Montserrat-500',
        lineHeight: 12,
        color: dark.colors.textDark,
    },
    eligibilityText: {
        fontFamily: 'Montserrat-500',
        lineHeight: 16,
        fontSize:14,
        color: dark.colors.textDark,
    },
    eligibilityTextHeading: {
        fontFamily: 'Montserrat-500',
        lineHeight: 12,
        fontSize:16,
        color: dark.colors.textDark,
    },
    infoNumber: {
        lineHeight: 12,
        color: '#FFF',
        fontSize: 14,
        marginTop: 5,
    },
    eligibilityContainer: {
        marginTop: 20,
        backgroundColor: 'transparent',
        padding: 15,
        borderRadius: 10,
        alignItems: 'center',
    },
    unRegisterButton: {
        backgroundColor: dark.colors.primary,
        paddingVertical: 10,
        borderRadius: 10,
        alignItems: 'center',
    },
    unregisterText: {
        fontFamily: 'Montserrat-600',
        fontSize: 13,
        lineHeight: 20,
        color: '#FF7777'
    },
    buttonText: {
        color: '#000',
        fontSize: 16,
        fontFamily: 'Montserrat-700',
    },
    registerButton: {
        backgroundColor: dark.colors.secondary,
        padding: 10,
        height: 40,
        width: '100%',
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    lockedRegisterButton: {
        backgroundColor: dark.colors.tertiary,
        padding: 12,
        width: '100%',
        height: 50,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        gap: 8,  
    },
    lockedRegisterText: {
        color: dark.colors.text,
        fontSize: 16,
        fontFamily: 'Montserrat-600',
    },
    buttonBox: {
        width: '100%',
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
    registerText: {
        color: dark.colors.card,
        fontSize: 16,
        fontFamily: 'Montserrat-600',
    },

});

export default styles;
