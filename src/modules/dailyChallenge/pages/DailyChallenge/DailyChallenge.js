import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';

import useMediaQuery from 'core/hooks/useMediaQuery';
import _isEmpty from 'lodash/isEmpty';
import Loading from 'atoms/Loading';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { Redirect, useRouter } from 'expo-router';
import PropTypes from 'prop-types';
import Analytics from 'core/analytics';
import useDailyChallengeQuestionsState from '../../hooks/useDailyChallengeQuestionsState';
import styles from './DailyChallenge.style';
import Header from './components/Header';
import Question from '../../../game/pages/PlayGame/Question';

import Footer from '../../../game/pages/PlayGame/Footer';

import useAttemptDailyChallenge from '../../hooks/mutations/useAttemptDailyChallenge';
import ErrorView from '../../../../components/atoms/ErrorView/ErrorView';
import useGetEligibleDailyChallenges from '../../hooks/useGetEligibleDailyChallenges';

const TRACKED_FOR_DAILY_CHALLENGE = {};

const DailyChallenge = (props) => {
  const { dailyChallengeId, dailyChallenge } = props;
  const router = useRouter();

  const onSubmitDailyChallengeSuccess = useCallback(() => {}, [
    router,
    dailyChallengeId,
  ]);

  const onSubmitDailyChallengeFailure = useCallback(() => {
    showToast({
      type: TOAST_TYPE.ERROR,
      description: 'Something went wrong while submitting the daily challenge.',
    });
  }, []);

  const {
    currentQuestion,
    submitAnswer,
    onSubmitAllAnswers,
    currentScore,
    submittedDailyChallenge,
    submittingDailyChallenge,
    dailyChallengeSubmissionError,
  } = useDailyChallengeQuestionsState({
    onSubmitDailyChallengeSuccess,
    onSubmitDailyChallengeFailure,
    dailyChallengeId,
  });

  const { attemptDailyChallenge } = useAttemptDailyChallenge({
    challengeId: dailyChallengeId,
  });

  const { challengeNumber, division } = dailyChallenge;

  const { checkIsUserEligibleforDCByDivision } =
    useGetEligibleDailyChallenges();

  const isEligible = useMemo(
    () => checkIsUserEligibleforDCByDivision({ division }),
    [division],
  );

  const { isMobile } = useMediaQuery();

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer question={currentQuestion} submitAnswer={submitAnswer} />
      </View>
    ),
    [currentQuestion, submitAnswer],
  );

  useEffect(() => {
    if (TRACKED_FOR_DAILY_CHALLENGE[challengeNumber] || !challengeNumber)
      return;

    Analytics.track(ANALYTICS_EVENTS.DAILY_CHALLENGE.VIEWED_DC, {
      challengeNumber,
      challengeId: dailyChallengeId,
      division,
    });
    TRACKED_FOR_DAILY_CHALLENGE[challengeNumber] = true;
  }, [challengeNumber, division]);

  useEffect(() => {
    if (challengeNumber && dailyChallengeId) {
      attemptDailyChallenge?.({
        challengeId: dailyChallengeId,
        challengeNumber,
        division,
      }).catch((error) => {
        // console.log('error', error);
      });
    }
  }, [dailyChallengeId, division, challengeNumber]);

  if (_isEmpty(dailyChallenge)) {
    return <Loading />;
  }

  if (!isEligible) {
    return (
      <ErrorView
        errorMessage={`You're not Eligible for ${division} Category . Aim High! Keep climbing to compete at this level and join the elite.`}
      />
    );
  }

  if (submittingDailyChallenge) {
    return <Loading label="Submitting your responses..." />;
  }

  if (dailyChallengeSubmissionError) {
    return (
      <ErrorView
        errorMessage="Something went wrong while submitting the daily challenge."
        onRetry={onSubmitAllAnswers}
      />
    );
  }

  if (submittedDailyChallenge) {
    return <Redirect href={`daily-challenge/result/${dailyChallengeId}`} />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={isMobile && styles.mobileHeader}>
            <Header
              dailyChallenge={dailyChallenge}
              currentScore={currentScore}
            />
          </View>
          <View style={[styles.question]}>
            <Question question={currentQuestion} />
          </View>
          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
};

DailyChallenge.propTypes = {
  dailyChallengeId: PropTypes.string,
  dailyChallenge: PropTypes.object,
};

const DailyChallengeLoader = (props) => {
  const { dailyChallengeId, dailyChallenge } = props;

  const { hasAttempted } = dailyChallenge ?? EMPTY_OBJECT;

  const initialHasAttemptedValue = useRef(hasAttempted);

  if (initialHasAttemptedValue.current) {
    return (
      <ErrorView
        errorMessage={`You have already attempted today's Daily Challenge, Try again tomorrow.`}
      />
    );
  }

  return (
    <DailyChallenge
      dailyChallenge={dailyChallenge}
      dailyChallengeId={dailyChallengeId}
    />
  );
};

export default DailyChallengeLoader;
