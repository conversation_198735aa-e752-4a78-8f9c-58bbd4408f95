import React, { useCallback, useState } from "react"
import { Tab<PERSON><PERSON>, TabView } from "react-native-tab-view";
import _isNil from 'lodash/isNil'
import { DAILY_CHALLENGE_LEADERBOARD_TABS } from "../../../../core/constants/dailyChallenge";
import styles from "./DailyChallengeLeaderboardPage.style";
import useLeaderboardSwitcher from "../../hooks/useLeaderboardSwitcher";
import { Dimensions, View, Text } from "react-native";
import dark from "../../../../core/constants/themes/dark";
import DailyChallengeLeaderboard from "../../components/DailyChallengeLeaderboard/DailyChallengeLeaderboard";
import useMediaQuery from "core/hooks/useMediaQuery";
import DayHorizontalTab from "../../components/DayHorizontalTab";
import PropTypes from "prop-types";

const initialLayout = { width: Dimensions.get('window').width }

const DailyChallengeLeaderboardPage = (props) => {
    
    const { isMobile: isCompactMode } = useMediaQuery()

    const { dateStr, dayFilterProps } = useLeaderboardSwitcher();

    const routes = DAILY_CHALLENGE_LEADERBOARD_TABS

    const [index, setIndex] = useState(0)

    const onIndexChange = useCallback((updatedIndex) => {
        setIndex(updatedIndex);
    }, [setIndex, routes]);

    const renderTabBar = (props) => (
        routes.length > 1 ?
            (<View style={styles.tabBarContainer}>
                <TabBar
                    {...props}
                    indicatorStyle={styles.indicator}
                    style={styles.tabBar}
                    tabStyle={styles.tabStyle}
                    labelStyle={styles.label}
                    activeColor={dark.colors.secondary}
                    inactiveColor={'white'}
                    renderLabel={({ route, focused, color }) => (
                        <Text style={{ ...styles.label, color: color }}>
                            {route.title}
                        </Text>
                    )}
                />
            </View>) :
            (<View style={{ height: 20 }}></View>)
    )

    const renderScene = useCallback(({ route, jumpTo }) => {
        return (<DailyChallengeLeaderboard key={dateStr} dateStr={dateStr} division={route.key} />)
    }, [dateStr])

    return (
        <View style={{ flex: 1, width: isCompactMode ? '100%' : "70%", }}>

            <DayHorizontalTab {...dayFilterProps} />

            <TabView
                navigationState={{ index, routes }}
                renderScene={renderScene}
                onIndexChange={onIndexChange}
                initialLayout={initialLayout}
                renderTabBar={renderTabBar}
            />

            
        </View>
    )
}

DailyChallengeLeaderboardPage.propTypes = {}

export default React.memo(DailyChallengeLeaderboardPage)