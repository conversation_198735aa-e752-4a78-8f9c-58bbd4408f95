import React, { useCallback, useMemo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Text } from '@rneui/themed';

import _slice from 'lodash/slice';

import UserImage from 'atoms/UserImage';
import { getFormattedTimeWithMS } from 'core/utils/general';
import ErrorView from 'atoms/ErrorView';
import _isNil from 'lodash/isNil';

import { VIEW_MODES } from 'core/constants/viewMode';
import { useRouter } from 'expo-router';
import PaginatedList from 'shared/PaginatedList';

import PlaceholderRow from 'shared/PlaceholderRow';
import Podium from 'shared/Podium/Podium';

import useMediaQuery from 'core/hooks/useMediaQuery';
import _find from 'lodash/find';
import DCCurrentUserResult from '../DCCurrentUserResult';
import useDCLeaderboardController from '../../hooks/useDCLeaderboardController';
import LeaderboardHeader from '../../../globalLeaderboard/components/LeaderboardHeader/LeaderboardHeader';
import checkIsSameDay from '../../../../core/utils/checkIsSameDay';
import useGetEligibleDailyChallenges from '../../hooks/useGetEligibleDailyChallenges';
import EmptyDCLeaderboard from '../EmptyLeaderboard/EmptyLeaderboard';
import useFetchDailyChallengesQuery from '../../../../core/graphql/queries/useGetDailyChallenges';
import styles from './DailyChallengeLeaderboard.style';
import userReader from '../../../../core/readers/userReader';

const DEFAULT_HEADER_ITEMS = ['#', 'Mathlete', 'Time (min:sec:ms)'];

const LEADER_BOARD_PAGE_SIZE = 50;

const DailyChallengeLeaderboard = ({
  dateStr,
  division,
  size = LEADER_BOARD_PAGE_SIZE,
  styleProps = EMPTY_OBJECT,
  mode,
  headerItems = DEFAULT_HEADER_ITEMS,
  renderEmptyLeaderboard,
}) => {
  const todayDateStr = new Date().toISOString();
  const router = useRouter();

  const { contentContainerStyle, rankColumnStyle, headerContainerStyle } =
    styleProps;

  const { loading, error, fetchLeaderboard } = useDCLeaderboardController({
    dateStr,
    pageSize: size,
    division,
  });

  const { challenges } = useFetchDailyChallengesQuery();

  const challengeId = useMemo(
    () =>
      _find(challenges, (challenge) => challenge.division === division)?._id,
    [division, challenges],
  );

  const { checkIsUserEligibleforDCByDivision } =
    useGetEligibleDailyChallenges();

  const isToday = checkIsSameDay({ date1: dateStr, date2: todayDateStr });

  const isCompactMode = mode === VIEW_MODES.COMPACT;

  const isEligible = useMemo(
    () => checkIsUserEligibleforDCByDivision({ division }),
    [division],
  );

  const { isMobile } = useMediaQuery();

  const navigateToUserProfile = useCallback(
    ({ username }) => {
      router.push(`/profile/${username}`);
    },
    [router],
  );

  const renderRow = useCallback(
    ({ item, index }) => {
      const { user } = item;
      // const { badge } = user ?? EMPTY_OBJECT;
      // const { color = '#C7249E' } = BADGES_DETAILS[badge] ?? EMPTY_OBJECT;
      return (
        <View>
          <TouchableOpacity
            style={[
              styles.rowContainer,
              isCompactMode && styles.compactRowContainer,
              isMobile && styles.mobileRowContainer,
            ]}
            key={item?._id}
            onPress={() => navigateToUserProfile({ username: user?.username })}
          >
            <View style={[styles.rankColumn, rankColumnStyle]}>
              <Text style={[styles.rowLabel]}>{item?.rank}</Text>
            </View>
            <View style={styles.profileInfoColumn}>
              {!isCompactMode && (
                <View style={{ width: 32 }}>
                  <UserImage
                    user={item?.user}
                    size={isCompactMode ? 24 : 30}
                    rounded={!isMobile}
                    style={
                      isMobile
                        ? {
                            height: 32,
                            width: 32,
                            borderRadius: 4,
                            overflow: 'hidden',
                          }
                        : null
                    }
                  />
                </View>
              )}
              <View
                style={[
                  styles.usernameContainer,
                  (isCompactMode || isMobile) &&
                    styles.usernameContainerCompact,
                ]}
              >
                <Text
                  style={[
                    styles.rowLabel,
                    (isCompactMode || isMobile) && styles.rowLabelCompact,
                  ]}
                  numberOfLines={1}
                >
                  {userReader.username(item?.user)}
                </Text>
                {!isCompactMode && (
                  <Text
                    style={[styles.rating, isMobile && styles.ratingCompact]}
                  >
                    {item?.user.rating}
                  </Text>
                )}
              </View>
            </View>
            <View style={styles.timeColumn}>
              <Text
                style={[
                  styles.rowLabel,
                  isCompactMode && styles.rowLabelCompact,
                ]}
              >
                {getFormattedTimeWithMS(item.score)}
              </Text>
            </View>
          </TouchableOpacity>
          {index !== size - 1 && renderSeparator()}
        </View>
      );
    },
    [isCompactMode, isMobile, size],
  );

  const renderSeparator = useCallback(
    () => (
      <View
        style={[styles.separator, isCompactMode && { marginVertical: 12 }]}
      />
    ),
    [],
  );

  const renderPlaceholderRow = useCallback(
    () => (
      <View>
        <PlaceholderRow />
        <PlaceholderRow />
        <PlaceholderRow />
      </View>
    ),
    [],
  );

  const renderEmptyLeaderboardComponent = useCallback(() => {
    if (renderEmptyLeaderboard) {
      return renderEmptyLeaderboard();
    }
    return (
      <EmptyDCLeaderboard
        isEligible={isEligible}
        isToday={isToday}
        challengeId={challengeId}
      />
    );
  }, [isToday, renderEmptyLeaderboard, isEligible, challengeId]);

  const renderPodiumHeader = useCallback(
    ({ page: pageNumber, data, itemsList }) => {
      const isFirstPage = pageNumber === 1;
      const podiumUsers = _slice(itemsList, 0, 3);
      return (
        <View>
          {!isCompactMode && (
            <DCCurrentUserResult
              dateStr={dateStr}
              key={dateStr}
              division={division}
              challengeId={challengeId}
            />
          )}
          {!isToday && isFirstPage && !isCompactMode && (
            <Podium podiumUsers={podiumUsers} />
          )}
          <LeaderboardHeader
            headerItems={headerItems}
            headerStyle={[styles.headerContainer, headerContainerStyle]}
            rankColumnStyle={rankColumnStyle}
          />
        </View>
      );
    },
    [
      headerItems,
      isToday,
      isCompactMode,
      rankColumnStyle,
      headerContainerStyle,
      dateStr,
      division,
      challengeId,
    ],
  );

  const fetchData = useCallback(
    async ({ pageNumber }) => {
      const response = await fetchLeaderboard({ pageNumber });
      const { data } = response;
      const { dailyChallengeLeaderboard: participantsObject } = data;

      const { results, totalResults } = participantsObject;

      if (pageNumber == 1 && !isToday) {
        return {
          data:
            results.length > 3 ? _slice(results, 3, results.length) : results,
          totalItems: totalResults,
          itemsList: results,
        };
      }
      return { data: results, totalItems: totalResults, itemsList: results };
    },
    [size],
  );

  if (error) return <ErrorView errorMessage={error.message} />;

  return (
    <View style={[styles.container, styleProps.contentContainerStyle]}>
      <PaginatedList
        hidePaginaton={isCompactMode}
        makeHeaderScrollable={!isToday}
        placeholderComponent={renderPlaceholderRow}
        emptyListComponent={renderEmptyLeaderboardComponent}
        fetchData={fetchData}
        renderItem={renderRow}
        renderHeader={renderPodiumHeader}
        pageSize={size}
        keyExtractor={(item, index) =>
          `${item.user?._id.toString()} - ${index}`
        }
        contentContainerStyle={{ flexGrow: 1 }}
        listFooterComponent={
          isCompactMode ? null : <View style={{ height: 80 }} />
        }
        pullToRefreshEnabled
      />
    </View>
  );
};

const DailyChallengeLeaderboardContainer = (props) => {
  const { dateStr } = props;

  if (_isNil(dateStr)) {
    return null;
  }

  return <DailyChallengeLeaderboard {...props} />;
};

export default React.memo(DailyChallengeLeaderboardContainer);
