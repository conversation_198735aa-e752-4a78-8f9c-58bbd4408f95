import React, { useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import _map from 'lodash/map';
import useFetchDailyChallengesQuery from 'core/graphql/queries/useGetDailyChallenges';
import DailyPuzzleCard from 'modules/puzzles/components/DailyPuzzleCard';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';
import ChallengeCardShimmer from 'shared/shimmers/ChallengeCardShimmer';
import styles from './DCByCategory.style';
import LockedDCCard from '../LockedDCCard';
import CompactDailyChallengeCard from '../CompactDailyChallengeCard';

const getTimeLeftUntilEndOfDayUTC = () => {
  const now = new Date();
  const utcNow = new Date(now.toISOString());

  const midnightUTC = new Date(
    Date.UTC(
      utcNow.getUTCFullYear(),
      utcNow.getUTCMonth(),
      utcNow.getUTCDate(),
      23,
      59,
      59,
      999,
    ),
  );

  return midnightUTC - utcNow;
};

const DCByCategory = (props) => {
  const { isTablet, isMobile, isDesktopBrowser } = useMediaQuery();
  const isCompactDevice = isTablet || isMobile;

  const { challenges, loading, error } = useFetchDailyChallengesQuery();

  const { user } = useSession();
  const { isGuest = false } = user ?? EMPTY_OBJECT;

  const [timeLeft, setTimeLeft] = useState(getTimeLeftUntilEndOfDayUTC());

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTimeLeft(getTimeLeftUntilEndOfDayUTC());
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  if (loading || error) {
    return (
      <View
        style={[
          styles.container,
          isCompactDevice && {
            backgroundColor: dark.colors.background,
            paddingHorizontal: 0,
          },
        ]}
      >
        <View
          style={[
            isCompactDevice
              ? styles.contentContainer
              : styles.webContentContainer,
          ]}
        >
          <View
            style={[
              styles.headerContainer,
              isCompactDevice && {
                marginLeft: 16,
                justifyContent: 'space-between',
              },
            ]}
          >
            <Text style={[styles.dcText]}> DAILY CHALLENGES </Text>
          </View>
          <View
            style={{
              flexDirection: isCompactDevice ? 'row' : 'column',
              gap: 12,
              paddingHorizontal: isCompactDevice ? 16 : 0,
              marginTop: 4,
            }}
          >
            <ChallengeCardShimmer key="shimmer-1" />
            <ChallengeCardShimmer key="shimmer-2" />
            <ChallengeCardShimmer key="shimmer-3" />
          </View>
        </View>
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        isCompactDevice && {
          backgroundColor: dark.colors.background,
          paddingHorizontal: 0,
        },
        isDesktopBrowser && {
          borderWidth: 1,
          borderColor: dark.colors.tertiary,
        },
      ]}
    >
      <View
        style={[
          isCompactDevice
            ? styles.contentContainer
            : styles.webContentContainer,
        ]}
      >
        <View
          style={[
            styles.headerContainer,
            isCompactDevice && {
              justifyContent: 'space-between',
              marginLeft: 16,
            },
          ]}
        >
          <Text style={[styles.dcText]}> DAILY CHALLENGES </Text>
        </View>
        <ScrollView
          contentContainerStyle={{
            flexDirection: isCompactDevice ? 'row' : 'column',
            gap: 12,
            paddingHorizontal: isCompactDevice ? 16 : 0,
            marginTop: 4,
          }}
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
        >
          <View key="puzzle-daily-challenge">
            <DailyPuzzleCard />
          </View>
          {_map(challenges, (challenge, index) => (
            <View key={`${challenge?._id}-${index}`}>
              <CompactDailyChallengeCard dailyChallenge={challenge} />
            </View>
          ))}

          {isGuest && <LockedDCCard />}
        </ScrollView>
      </View>
    </View>
  );
};

DCByCategory.propTypes = {};

export default React.memo(DCByCategory);
