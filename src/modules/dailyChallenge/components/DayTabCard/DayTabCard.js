import PropTypes from "prop-types";
import React, { useCallback } from "react";
import { View, Text, Image } from "react-native";
import dark from "../../../../core/constants/themes/dark";

import { TouchableOpacity } from "react-native";
import styles from "./DayTabCard.style";


const DayTabCard = (props) => {
    const { isSelected, onPress, label, dayLabel, icon } = props;

    return (
        <TouchableOpacity
            onPress={onPress}
            style={[styles.container, isSelected && { borderColor: 'white' }]}>
            <Text style={{ fontSize: 14, fontFamily: "Montserrat-600", lineHeight: 18,color:'white' }}>
                {label}
            </Text>
            <Text style={{ fontSize: 10, fontFamily: "Montserrat-500", lineHeight: 13,color:dark.colors.textDark }}>
                {dayLabel}
            </Text>
            <Image source={icon} style={{ height: 60, width: 60, position: "absolute", right: -15, }} />
        </TouchableOpacity>
    );
};

DayTabCard.propTypes = {
    isSelected: PropTypes.bool,
    onPress: PropTypes.func,
    dayLabel: PropTypes.string,
    icon: PropTypes.any,
    label : PropTypes.string
};

export default React.memo(DayTabCard);
