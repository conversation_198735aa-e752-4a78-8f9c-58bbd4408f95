import React, { useCallback, useMemo } from 'react';

import { Text, View } from 'react-native';

import Shi<PERSON>View from 'molecules/ShimmerView';

import EvilIcons from '@expo/vector-icons/EvilIcons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from 'core/constants/themes/dark';
import dark from 'core/constants/themes/dark';
import useGetUserDailyChallengeResult from 'core/graphql/queries/useGetUserDailyChallengeResult';
import _isNil from 'lodash/isNil';
import takeChallenge from '@/assets/images/take_challenge.png';

import loginIcon from '@/assets/images/dc/login.png';

import { useRouter } from 'expo-router';
import CardWithCTA from 'shared/CardWithCTA/CardWithCTA';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import YesterdayUserResult from './YesterdayUserResult/YesterdayDCUserResult';
import LiveDC<PERSON>serResult from './LiveDCUserResult/LiveDCUserResult';
import styles from './DCCurrentUserResult.style';
import { DAILY_CHALLENGE_RESULT_STATUS } from '../../../../core/constants/dailyChallenge';
import checkIsSameDay from '../../../../core/utils/checkIsSameDay';
import { useSession } from '../../../auth/containers/AuthProvider';
import CardWithoutCTA from '../../../../components/shared/CardWithoutCTA/CardWithoutCTA';
import useGetEligibleDailyChallenges from '../../hooks/useGetEligibleDailyChallenges';
import getCTATextForNotEligibleInDC from '../../utils/getCTATextforNotEligibleInDC';
import GoogleLoginButton from '../../../../core/oauth/components/GoogleLoginButton';

const DcCurrentUserResult = ({ dateStr, division, challengeId }) => {
  const todayDateStr = new Date().toISOString();
  const { user } = useSession();

  const { isGuest } = user ?? EMPTY_OBJECT;

  const { result, error, loading } = useGetUserDailyChallengeResult({
    dateStr,
    division,
  });

  const router = useRouter();

  const isTodayDC = checkIsSameDay({ date1: dateStr, date2: todayDateStr });

  const { checkIsUserEligibleforDCByDivision, checkHasUserCrossedDivision } =
    useGetEligibleDailyChallenges();

  const isEligible = useMemo(
    () => checkIsUserEligibleforDCByDivision({ division }),
    [division],
  );

  const isUserCrossedDiv = useMemo(
    () => checkHasUserCrossedDivision({ division }),
    [division],
  );

  const onPressTakeChallenge = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.DAILY_CHALLENGE.CLICKED_ON_TAKE_DAILY_CHALLENGE,
      {
        dateStr,
        [PAGE_NAME_KEY]: PAGE_NAMES.DAILY_CHALLENGE_LEADERBOARD,
        division,
        id: challengeId,
      },
    );
    router.push(`/daily-challenge/${challengeId}`);
  }, [router, challengeId, division]);

  const hasCompleted = !_isNil(result);

  const renderButtonComponent = useCallback(
    () => (
      <Text
        style={{
          fontSize: 13,
          lineHeight: 20,
          fontFamily: 'Montserrat-500',
          color: dark.colors.secondary,
        }}
      >
        Sign in
      </Text>
    ),
    [],
  );

  const onPressGoogleSignIn = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.DAILY_CHALLENGE.CLICKED_ON_UNLOCK_DAILY_CHALLENGE,
      {
        [PAGE_NAME_KEY]: PAGE_NAMES.DAILY_CHALLENGE_LEADERBOARD,
      },
    );
  }, []);

  const renderGoogleSignInButton = useCallback(
    () => (
      <GoogleLoginButton
        renderButtonComponent={renderButtonComponent}
        onPress={onPressGoogleSignIn}
      />
    ),
    [renderButtonComponent, onPressGoogleSignIn],
  );

  if (loading) {
    return (
      <View
        style={{
          borderRadius: 10,
          borderColor: dark.colors.tertiary,
          borderWidth: 2,
        }}
      >
        <View style={styles.contentContainer}>
          <ShimmerView
            style={{ width: 20, height: 20, borderRadius: 8 }}
            shimmerColors={Dark.colors.placeholderShimmerColors}
          />
        </View>
      </View>
    );
  }

  if (!isEligible && isGuest) {
    return (
      <View style={{ marginVertical: 15 }}>
        <CardWithCTA
          imageSource={loginIcon}
          titleText="Please sign in to take challenge"
          renderButtonComponent={renderGoogleSignInButton}
        />
      </View>
    );
  }

  if (!isEligible) {
    return (
      <View style={{ marginVertical: 15 }}>
        <CardWithoutCTA
          renderInfoIcon={() => (
            <EvilIcons name="lock" size={15} color={dark.colors.textDark} />
          )}
          infoText={getCTATextForNotEligibleInDC({
            isUserCrossedDiv,
            division,
            user,
          })}
        />
      </View>
    );
  }

  if (!hasCompleted && isTodayDC) {
    return (
      <View style={{ marginVertical: 15 }}>
        <CardWithCTA
          buttonText="Take Challenge"
          imageSource={takeChallenge}
          onButtonPress={onPressTakeChallenge}
          titleText={"Haven't taken today's challenge yet?"}
        />
      </View>
    );
  }

  if (!hasCompleted && !isTodayDC) {
    return null;
  }

  const { score, rank, resultStatus } = result;

  const hasAttemptedButNotCompleted =
    resultStatus === DAILY_CHALLENGE_RESULT_STATUS.ATTEMPTED;

  if (hasAttemptedButNotCompleted && isTodayDC) {
    return (
      <View style={{ marginVertical: 15 }}>
        <CardWithoutCTA
          renderInfoIcon={() => (
            <MaterialIcons
              name="info-outline"
              size={15}
              color={dark.colors.textDark}
            />
          )}
          infoText={
            "Today's attempt wasn't submitted. Please try again tomorrow!"
          }
        />
      </View>
    );
  }

  if (hasAttemptedButNotCompleted && !isTodayDC) {
    return null;
  }

  if (isTodayDC) {
    return (
      <LiveDCUserResult
        currentRank={rank}
        score={score}
        challengeId={challengeId}
      />
    );
  }

  if (!isTodayDC) {
    return <YesterdayUserResult rank={rank} score={score} />;
  }

  return null;
};

DcCurrentUserResult.propTypes = {};

export default React.memo(DcCurrentUserResult);
