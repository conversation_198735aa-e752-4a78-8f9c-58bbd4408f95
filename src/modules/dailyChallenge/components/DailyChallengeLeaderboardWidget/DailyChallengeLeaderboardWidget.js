import React, { useCallback, useMemo } from 'react';
import { View, Text } from 'react-native';

import useDailyChallengeNumber from "core/hooks/dailyChallenge/useDailyChallengeNumber";
import AntDesign from '@expo/vector-icons/AntDesign';
import styles from './DailyChallengeLeaderboardWidget.style';
import { VIEW_MODES } from "core/constants/viewMode";
import RedDot from 'shared/RedDot';
import { useRouter } from "expo-router";
import { ANALYTICS_EVENTS } from "core/analytics/const";
import dark from 'core/constants/themes/dark';
import DailyChallengeLeaderboard from '../DailyChallengeLeaderboard';
import Analytics from 'core/analytics';
import { DAILY_CHALLENGE_DIVISION } from '../../../../core/constants/dailyChallenge';


const LEADERBOARD_WIDGET_SIZE = 5;

const DailyChallengeLeaderboardWidget = props => {
    const router = useRouter();
    const todayDateStr = new Date().toISOString()
    const { challengeNumber } = useDailyChallengeNumber() ?? EMPTY_OBJECT;

    const styleProps = useMemo(() => ({
        contentContainerStyle: styles.contentContainerStyle,
        rankColumnStyle: styles.rankColumnStyle,
        headerContainerStyle: styles.headerContainer,
    }), []);

    const onPressSeeMore = useCallback(() => {
        Analytics.track(ANALYTICS_EVENTS.DAILY_CHALLENGE.CLICKED_ON_SEE_MORE_DAILY_CHALLENGE_LIVE_STANDING)
        router.push('/daily-challenge-leaderboard')
    }, [router]);

    return (
        <View style={styles.container}>
            <View style={styles.contentContainer}>
                <View style={styles.headerRow}>
                    <Text style={styles.label}>OPEN DC #{challengeNumber}</Text>
                    <AntDesign name="arrowright" size={18} color={dark.colors.secondary} onPress={onPressSeeMore} />
                </View>
                <View style={styles.liveContainer}>
                    <Text style={styles.liveText}>Live</Text>
                    <RedDot size={8} />
                </View>
                <DailyChallengeLeaderboard
                    division={DAILY_CHALLENGE_DIVISION.OPEN}
                    dateStr={todayDateStr}
                    challengeNumber={challengeNumber}
                    size={LEADERBOARD_WIDGET_SIZE}
                    styleProps={styleProps}
                    mode={VIEW_MODES.COMPACT}
                    headerItems={['#', 'MATHLETE', 'TIME']}
                />
            </View>
        </View>
    );
};

DailyChallengeLeaderboardWidget.propTypes = {

};


export default DailyChallengeLeaderboardWidget