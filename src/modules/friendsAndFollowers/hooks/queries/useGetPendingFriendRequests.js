import { gql, useLazyQuery } from '@apollo/client';
import { FRIEND_REQUEST_FRAGMENT } from 'core/graphql/fragments/friends';
import { useCallback } from 'react';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import _get from 'lodash/get';

const DEFAULT_PAGE_SIZE = 50;

const GET_PENDING_FRIEND_REQUESTS = gql`
  query GetPendingFriendRequests($page: Int, $pageSize: Int) {
    getPendingFriendRequests(page: $page, pageSize: $pageSize) {
      results {
        ...FriendRequestFragment
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
  ${FRIEND_REQUEST_FRAGMENT}
`;

const useGetPendingFriendRequests = ({ pageSize = DEFAULT_PAGE_SIZE }) => {
  const [
    fetchPendingFriendRequestsQuery,
    { loading, error, refetch, client, data },
  ] = useLazyQuery(GET_PENDING_FRIEND_REQUESTS, {
    fetchPolicy: 'cache-first',
    notifyOnNetworkStatusChange: true,
  });

  const fetchPendingFriendRequests = useCallback(
    ({ pageNumber }) => {
      if (loading) {
        return;
      }

      return fetchPendingFriendRequestsQuery({
        variables: {
          page: pageNumber,
          pageSize,
        },
      });
    },
    [fetchPendingFriendRequestsQuery, loading, pageSize],
  );

  const updateFriendRequestCache = useCallback(
    ({ addedItems = [], removedItemIds = [], pageNumber = 1 }) => {
      client.cache.updateQuery(
        {
          query: GET_PENDING_FRIEND_REQUESTS,
          variables: { page: pageNumber, pageSize },
          broadcast: true,
          overwrite: true,
        },
        (data) => {
          if (_isEmpty(data?.getPendingFriendRequests)) {
            return data;
          }

          let updatedResults = data?.getPendingFriendRequests?.results;

          if (!_isEmpty(addedItems)) {
            updatedResults = [...addedItems, ...updatedResults];
          }

          if (!_isEmpty(removedItemIds)) {
            updatedResults = _filter(
              updatedResults,
              (item) => !_includes(removedItemIds, item?._id),
            );
          }

          return {
            ...data,
            getPendingFriendRequests: {
              ...data.getPendingFriendRequests,
              results: updatedResults,
              totalResults:
                _get(data, ['getPendingFriendRequests', 'totalResults'], 0) +
                _size(addedItems) -
                _size(removedItemIds),
            },
          };
        },
      );

      return client.cache.readQuery({
        query: GET_PENDING_FRIEND_REQUESTS,
        variables: { page: pageNumber, pageSize },
      });
    },
    [client.cache, pageSize],
  );

  return {
    loading,
    error,
    data,
    fetchPendingFriendRequests,
    updateFriendRequestCache,
  };
};
export default useGetPendingFriendRequests;
