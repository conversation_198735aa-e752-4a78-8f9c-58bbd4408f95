import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';
import { FRIENDS_FRAGMENT } from 'core/graphql/fragments/friends';
import _filter from 'lodash/filter';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import _isNil from 'lodash/isNil';
import { useSession } from '../../../auth/containers/AuthProvider';

const DEFAULT_PAGE_SIZE = 50;

const GET_FRIENDS = gql`
  query GetFriends($page: Int, $pageSize: Int, $sortOption: SortOptions) {
    getFriends(page: $page, pageSize: $pageSize, sortOption: $sortOption) {
      results {
        ...FriendsFragment
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
  ${FRIENDS_FRAGMENT}
`;

const useGetFriends = ({ pageSize = DEFAULT_PAGE_SIZE, sortOption = null }) => {
  const { user, updateCurrentUser } = useSession();

  const [fetchGetFriendsQuery, { loading, error, refetch, client, data }] =
    useLazyQuery(GET_FRIENDS, {
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    });

  const fetchFriends = useCallback(
    async ({ pageNumber }) => {
      if (loading) {
        return;
      }

      return fetchGetFriendsQuery({
        variables: {
          page: pageNumber,
          pageSize,
          sortOption,
        },
      });
    },
    [fetchGetFriendsQuery, loading, pageSize, sortOption],
  );

  const updateFriendsCache = useCallback(
    async ({ addedItems = [], removedItemIds = [], pageNumber = 1 }) => {
      client.cache.updateQuery(
        {
          query: GET_FRIENDS,
          variables: { page: pageNumber, pageSize, sortOption },
          broadcast: true,
          overwrite: true,
        },
        (data) => {
          const { getFriends } = data ?? EMPTY_OBJECT;
          if (_isEmpty(getFriends)) {
            return data;
          }

          let { results: updatedResults, totalResults } =
            getFriends ?? EMPTY_OBJECT;

          if (!_isEmpty(addedItems)) {
            updatedResults = [...addedItems, ...updatedResults];
          }

          if (!_isEmpty(removedItemIds)) {
            updatedResults = _filter(
              updatedResults,
              (item) => !_includes(removedItemIds, item?._id),
            );
          }

          const updatedTotalItems =
            totalResults + _size(addedItems) - _size(removedItemIds);

          const { stats } = user ?? EMPTY_OBJECT;
          const { friendsCount, ...restStatsData } = stats;

          updateCurrentUser({
            stats: {
              friendsCount: _isNil(friendsCount)
                ? 0
                : Math.max(updatedTotalItems, 0),
              ...restStatsData,
            },
          });

          return {
            ...data,
            getFriends: {
              ...getFriends,
              results: updatedResults,
              totalResults: updatedTotalItems,
            },
          };
        },
      );

      return client.cache.readQuery({
        query: GET_FRIENDS,
        variables: { page: pageNumber, pageSize, sortOption: null },
      });
    },
    [client.cache, pageSize, sortOption, updateCurrentUser, user],
  );

  return {
    loading,
    error,
    data,
    fetchFriends,
    updateFriendsCache,
  };
};

export default useGetFriends;
