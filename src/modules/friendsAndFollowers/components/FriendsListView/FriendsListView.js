import PropTypes from 'prop-types';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import PaginatedList from 'shared/PaginatedList/PaginatedList';
import NoFriendsView from 'modules/friendsAndFollowers/components/NoFriendsView';
import _isFunction from 'lodash/isFunction';
import { FRIENDS_STATUS } from '../../constants/friendsListTab';
import FriendRequestCard from '../FriendRequestCard';
import FriendCard from '../FriendCard';
import useGetFriends from '../../hooks/queries/useGetFriends';
import useGetPendingFriendRequests from '../../hooks/queries/useGetPendingFriendRequests';
import Footer from './Footer';

const PAGE_SIZE = 50;

const FriendsListView = (props) => {
  const { status, onPendingCountFetched } = props;
  const [hasFriends, setHasFriends] = useState(true);

  const {
    fetchFriends,
    updateFriendsCache,
    data: friendsData,
  } = useGetFriends({ pageSize: PAGE_SIZE });

  const {
    fetchPendingFriendRequests,
    updateFriendRequestCache,
    data: pendingReqsData,
  } = useGetPendingFriendRequests({ pageSize: PAGE_SIZE });

  const renderFriendsListItem = useCallback(
    ({ item, _, onRemove }) => {
      if (status === FRIENDS_STATUS.PENDING) {
        return <FriendRequestCard infoData={item} onRemove={onRemove} />;
      }
      return (
        <FriendCard
          infoData={item}
          onRemove={onRemove}
          isOnline={item.isOnline}
          currActivity={item.currActivity}
        />
      );
    },
    [status],
  );

  const fetchFriendsData = useCallback(
    async ({ pageNumber }) => {
      const response = await fetchFriends({ pageNumber });
      const { data } = response;
      const { getFriends: friendsObject } = data ?? EMPTY_OBJECT;
      const { results, totalResults } = friendsObject;
      if (!results || results.length === 0) {
        setHasFriends(false);
        return { data: [], totalItems: 0 };
      }
      return { data: results, totalItems: totalResults };
    },
    [fetchFriends],
  );

  const fetchPendingRequestsData = useCallback(
    async ({ pageNumber }) => {
      const response = await fetchPendingFriendRequests({ pageNumber });
      const { data } = response;
      const { getPendingFriendRequests: friendsRequestObject } =
        data ?? EMPTY_OBJECT;
      const { results, totalResults } = friendsRequestObject;

      if (onPendingCountFetched) {
        onPendingCountFetched(totalResults);
      }

      return { data: results, totalItems: totalResults };
    },
    [fetchPendingFriendRequests, onPendingCountFetched],
  );

  const friendsTabRef = useRef(null);
  const pendingTabRef = useRef(null);

  const dataVersionRef = useRef({
    friends: JSON.stringify(friendsData || {}),
    pending: JSON.stringify(pendingReqsData || {}),
  });

  useEffect(() => {
    const currentFriendsData = JSON.stringify(friendsData || {});
    if (currentFriendsData !== dataVersionRef.current.friends) {
      dataVersionRef.current.friends = currentFriendsData;
      if (
        status !== FRIENDS_STATUS.PENDING &&
        friendsTabRef?.current &&
        _isFunction(friendsTabRef?.current?.loadData)
      ) {
        friendsTabRef?.current?.loadData?.();
      }
    }
  }, [friendsData, status]);

  useEffect(() => {
    const currentPendingData = JSON.stringify(pendingReqsData || {});
    if (currentPendingData !== dataVersionRef.current.pending) {
      dataVersionRef.current.pending = currentPendingData;
      if (
        status === FRIENDS_STATUS.PENDING &&
        pendingTabRef?.current &&
        _isFunction(pendingTabRef?.current?.loadData)
      ) {
        pendingTabRef?.current?.loadData?.();
      }
    }
  }, [pendingReqsData, status]);

  if (!hasFriends) {
    return <NoFriendsView />;
  }

  return (
    <View style={{ flex: 1 }}>
      <PaginatedList
        ref={status !== FRIENDS_STATUS.PENDING ? friendsTabRef : pendingTabRef}
        key={status}
        emptyListComponent={null}
        placeholderComponent={null}
        fetchData={
          status !== FRIENDS_STATUS.PENDING
            ? fetchFriendsData
            : fetchPendingRequestsData
        }
        renderItem={renderFriendsListItem}
        renderHeader={() => null}
        pageSize={PAGE_SIZE}
        keyExtractor={(item, index) => ` ${index}`}
        contentContainerStyle={{ maxWidth: 960 }}
        dataKey={
          status === FRIENDS_STATUS.PENDING
            ? 'getPendingFriendRequests'
            : 'getFriends'
        }
        listFooterComponent={<View style={{ height: 80 }} />}
        updateCacheFunction={
          status === FRIENDS_STATUS.PENDING
            ? updateFriendRequestCache
            : updateFriendsCache
        }
        pullToRefreshEnabled
        showNoDataStateForAllPages={false}
      />
      <Footer />
    </View>
  );
};

FriendsListView.propTypes = {
  status: PropTypes.string,
  onPendingCountFetched: PropTypes.func,
};

export default React.memo(FriendsListView);
