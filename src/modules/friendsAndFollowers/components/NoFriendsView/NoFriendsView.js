import React, { useCallback } from 'react';
import { Image, Text, View } from 'react-native';
import noFriendsImage from '@/assets/images/add_friends.png';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import { ScrollView } from 'tamagui';
import { useRouter } from 'expo-router';
import _map from 'lodash/map';
import { INSTRUCTIONS } from 'modules/friendsAndFollowers/constants/instructions';
import { openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import styles from './NoFriendsView.styles';

const PlayViaLinkModalContent = ({ closeBottomSheet }) => {
  const router = useRouter();

  const navigateToCreateLobby = useCallback(() => {
    closeBottomSheet?.();
    router.push(`/games/lobby`);
  }, [router, closeBottomSheet]);

  const renderInstructionCard = ({ key, title }) => (
    <View style={styles.instructionContainer}>
      <View style={styles.iconFrame}>
        <TextWithShadow
          text={key}
          textStyle={styles.keyStyles}
          containerStyle={styles.textContainerStyle}
          shadowColor="black"
          shadowOffsetX={-2}
          shadowOffsetY={-2}
        />
      </View>
      <View style={styles.contentContainer}>
        <Text style={styles.stepText}>{`STEP ${key}`}</Text>
        <View style={styles.textTitleContainer}>
          <Text style={styles.titleText} ellipsizeMode="tail" numberOfLines={2}>
            {title}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <View>
      <View style={styles.bodyContainer}>
        <View>
          <Text style={styles.headerText}>PLAY USING LINK</Text>
        </View>
        <View style={styles.instructionsContainer}>
          {_map(INSTRUCTIONS, (instruction) =>
            renderInstructionCard(instruction),
          )}
        </View>
        <View style={styles.footer}>
          <InteractivePrimaryButton
            onPress={navigateToCreateLobby}
            label="SETUP GAME"
            buttonStyle={styles.playNowButton}
            buttonContainerStyle={styles.playNowButtonContainer}
            labelStyle={styles.playNowButtonText}
            buttonBorderBackgroundStyle={styles.playNowBackground}
          />
        </View>
      </View>
    </View>
  );
};

const NoFriendsView = () => {
  const router = useRouter();

  const onPressFindFriend = useCallback(() => {
    router.push('/search-mathletes');
  }, [router]);

  const onClickPlayViaLink = useCallback(() => {
    openBottomSheet({
      content: ({ closeBottomSheet }) => (
        <PlayViaLinkModalContent closeBottomSheet={closeBottomSheet} />
      ),
    });
  }, []);

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
        <Image source={noFriendsImage} style={styles.image} />
        <View style={{ width: 200 }}>
          <TextWithShadow
            text="ADD YOUR FRIENDS"
            textStyle={styles.addFriendsStyle}
            containerStyle={styles.addFriendsContainerStyle}
            shadowWidth={0}
            numberOfLines={2}
            adjustsFontSizeToFit={false}
            strokeColor={"#000000"}
            strokeWidth={6}
          />
        </View>
        <Text style={styles.subtractText}>or subtract them...upto you</Text>
        <InteractivePrimaryButton
          onPress={onPressFindFriend}
          label="Find Friend"
          buttonStyle={styles.findFriendButton}
          buttonContainerStyle={styles.findFriendButtonContianer}
          labelStyle={styles.findFriendText}
          buttonBorderBackgroundStyle={styles.findFriendBackground}
          buttonContentStyles={{ paddingVertical: 0 }}
        />
        <Text style={styles.playWithLinkText}>Friend not on the app?</Text>
        <Text style={styles.noProblemText}>
          No problem, ask them to click the link
        </Text>
        <InteractivePrimaryButton
          onPress={onClickPlayViaLink}
          label="Play via Link"
          buttonStyle={styles.playWithLinkButton}
          buttonContainerStyle={styles.playWithLinkButtonContianer}
          labelStyle={styles.playWithLinkTextStyle}
          buttonBorderBackgroundStyle={styles.playWithLinkBackground}
          buttonContentStyles={{ paddingVertical: 0 }}
        />
      </View>
    </ScrollView>
  );
};

export default NoFriendsView;
