import React, { useCallback, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import UserImage from 'atoms/UserImage';
import { useRouter } from 'expo-router';
import PropTypes from 'prop-types';
import uuid from 'react-native-uuid';
import Analytics from 'core/analytics/index.js';
import { ANALYTICS_EVENTS } from 'core/analytics/const.js';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { useAcceptFriendRequest } from '../../hooks/mutations/useAcceptFriendRequest.js';
import { useRejectFriendRequest } from '../../hooks/mutations/useRejectFriendRequest.js';
import useGetFriends from '../../hooks/queries/useGetFriends.js';
import useGetPendingFriendRequests from '../../hooks/queries/useGetPendingFriendRequests.js';
import { useSession } from '../../../auth/containers/AuthProvider.js';
import useFriendRequestCardStyles from './FriendRequestCard.style.js';

const friendsObjToBeAdded = ({ sender, currentUserId }) => ({
  __typename: 'FriendsOutput',
  _id: uuid.v4(),
  senderId: sender?._id,
  receiverId: currentUserId,
  acceptedAt: '2024-11-22T15:42:34.482+05:30',
  friendInfo: { __typename: 'UserPublicDetails', ...sender },
});

const PAGE_SIZE = 50;

const FriendRequestCard = (props) => {
  const { infoData, onRemove } = props;
  const { acceptFriendRequest } = useAcceptFriendRequest();
  const { rejectFriendRequest } = useRejectFriendRequest();

  const { updateFriendsCache } = useGetFriends({ pageSize: PAGE_SIZE });
  const { updateFriendRequestCache } = useGetPendingFriendRequests({
    pageSize: PAGE_SIZE,
  });

  const router = useRouter();
  const { user: currentUser } = useSession();
  const { _id: currentUserId } = currentUser ?? EMPTY_OBJECT;

  const {
    username,
    profileImageUrl,
    rating,
    _id: senderId,
  } = infoData?.sender ?? EMPTY_OBJECT;

  const [isAcceptingRequest, setIsAcceptingRequest] = useState(false);
  const [isRejectingRequest, setIsRejectingRequest] = useState(false);

  const styles = useFriendRequestCardStyles();

  const handleOnAcceptPressed = useCallback(async () => {
    if (isAcceptingRequest) {
      return;
    }
    try {
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_ACCEPT_FRIEND_REQUEST,
      );
      setIsAcceptingRequest(true);
      await acceptFriendRequest({ senderId });

      const friendObj = friendsObjToBeAdded({
        sender: infoData?.sender,
        currentUserId,
      });

      await updateFriendsCache({
        addedItems: [friendObj],
        pageNumber: 1,
      });

      await updateFriendRequestCache({
        removedItemIds: [infoData?._id],
        pageNumber: 1,
      });

      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: 'Accepted Friend Request',
      });

      onRemove?.();
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: `You and ${username} are now friends`,
      });
    } catch (e) {
      setIsAcceptingRequest(false);
    } finally {
      setIsAcceptingRequest(false);
    }
  }, [
    senderId,
    infoData,
    currentUserId,
    isAcceptingRequest,
    username,
    setIsAcceptingRequest,
    onRemove,
    acceptFriendRequest,
    updateFriendsCache,
    updateFriendRequestCache,
  ]);

  const handleOnRejectPressed = useCallback(async () => {
    if (isRejectingRequest) {
      return;
    }
    try {
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_REJECT_FRIEND_REQUEST,
      );
      setIsRejectingRequest(true);
      await rejectFriendRequest({ senderId });
      onRemove?.();
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: `Successfully Rejected Friend Request of ${username}`,
      });
    } catch (e) {
      setIsRejectingRequest(false);
    } finally {
      setIsRejectingRequest(false);
    }
  }, [isRejectingRequest, rejectFriendRequest, senderId, onRemove, username]);

  const navigateToUserProfile = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
        .CLICKED_ON_USER_PROFILE_FROM_PENDING_REQUESTS_TAB,
    );
    router.push(`/profile/${username}`);
  }, [username, router]);

  return (
    <TouchableOpacity style={styles.container} onPress={navigateToUserProfile}>
      <View style={styles.userInfoWithImage}>
        <UserImage
          style={styles.userImage}
          user={{ profileImageUrl }}
          rounded={false}
        />
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{username}</Text>
          <Text style={styles.userRating}>{rating}</Text>
        </View>
      </View>
      <View style={[styles.userInfoWithImage, { alignItems: 'center' }]}>
        <TouchableOpacity onPress={handleOnRejectPressed}>
          <Text style={styles.rejectText}>
            {isRejectingRequest ? 'Rejecting....' : 'Reject'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleOnAcceptPressed}>
          <Text style={styles.acceptText}>
            {isAcceptingRequest ? 'Accepting....' : 'Accept'}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

FriendRequestCard.propTypes = {
  infoData: PropTypes.object,
  onRemove: PropTypes.func,
};

export default React.memo(FriendRequestCard);
