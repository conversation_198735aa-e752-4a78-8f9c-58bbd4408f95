import React, { useCallback, useMemo, useState } from "react"
import { View, Text } from "react-native"
import PropTypes from "prop-types"
import { TouchableOpacity } from "react-native"
import UserImage from "atoms/UserImage"
import { useRouter } from "expo-router"
import _isEqual from "lodash/isEqual"
import _toString from "lodash/toString"
import useFollowersAndFolloweeCardStyles from "./FollowersAndFolloweeCard.style.js";
import useUnFollowUser from "../../hooks/mutations/useUnfollowUser.js"
import { closeRightPane } from "molecules/RightPane/RightPane"
import useRemoveFollower from "../../hooks/mutations/useRemoveFollower.js"
import Analytics from "../../../../core/analytics/index.js"
import { ANALYTICS_EVENTS } from "../../../../core/analytics/const.js"
import { showToast, TOAST_TYPE } from 'molecules/Toast'

const FollowersAndFolloweeCard = (props) => {
    const styles = useFollowersAndFolloweeCardStyles()
    const { infoData, onRemove, isFollowerCard } = props
    const router = useRouter()

    const { unFollowUser } = useUnFollowUser()
    const { removeFollower } = useRemoveFollower()

    const [isUnfollowingUser, setIsUnfollowingUser] = useState(false)
    const [isRemovingFollower, setIsRemovingFollower] = useState(false)

    const userDetailsData = useMemo(() => {
        return infoData?.userInfo
    }, [infoData?.userInfo])

    const { username, profileImageUrl, rating, _id: userId } = userDetailsData ?? EMPTY_OBJECT

    const handleRemoveFollower = useCallback(async () => {
        if (isRemovingFollower) {
            return
        }
        try {
            Analytics.track(ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_REMOVE_FOLLOWER)
            setIsRemovingFollower(true)
            await removeFollower({ followerId: userId })
            onRemove?.()
            showToast({
                type: TOAST_TYPE.SUCCESS,
                description: `Successfully Removed  ${username} from your followers list `
            })
        } catch (e) {
            setIsRemovingFollower(false)
        } finally {
            setIsRemovingFollower(false)
        }
    }, [isRemovingFollower, setIsRemovingFollower, onRemove, removeFollower, userId, username])

    const handleUnfollowUser = useCallback(async () => {
        if (isUnfollowingUser) {
            return
        }
        try {
            Analytics.track(ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_REMOVE_FOLLOWING)
            setIsUnfollowingUser(true)
            await unFollowUser({ followeeId: userId })
            onRemove?.()
            showToast({
                type: TOAST_TYPE.SUCCESS,
                description: `Successfully Unfollowed ${username} `
            })
        } catch (e) {
            setIsUnfollowingUser(false)
        } finally {
            setIsUnfollowingUser(false)
        }
    }, [userId, isUnfollowingUser, setIsUnfollowingUser, onRemove, unFollowUser, username])

    const navigateToUserProfile = useCallback(() => {
        if (isFollowerCard) {
            Analytics.track(ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_USER_PROFILE_FROM_FOLLOWERS_PAGE)
        } else {
            Analytics.track(ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_USER_PROFILE_FROM_FOLLOWINGS_PAGE)
        }

        closeRightPane?.()
        router.push(`/profile/${username}`)
    }, [username, router, closeRightPane])

    return (
        <TouchableOpacity style={styles.container} onPress={navigateToUserProfile}>
            <View style={styles.userInfoWithImage}>
                <UserImage style={styles.userImage} user={{ profileImageUrl }} rounded={false} />
                <View style={styles.userInfo}>
                    <Text style={styles.userName}>
                        {username}
                    </Text>
                    <Text style={styles.userRating}>
                        {rating}
                    </Text>
                </View>
            </View>
            <View style={[styles.userInfoWithImage, { alignItems: "center" }]}>
                {!isFollowerCard ? <TouchableOpacity onPress={handleUnfollowUser}>
                    <Text style={styles.challengeText}>
                        {isUnfollowingUser ? "Unfollowing...." : "Unfollow"}
                    </Text>
                </TouchableOpacity> : <TouchableOpacity onPress={handleRemoveFollower}>
                    <Text style={styles.challengeText}>
                        {isRemovingFollower ? "Removing...." : "Remove"}
                    </Text>
                </TouchableOpacity>}
            </View>
        </TouchableOpacity>
    )
}

FollowersAndFolloweeCard.propTypes = {
    infoData: PropTypes.object,
    onRemove: PropTypes.func,
    isFollowerCard: PropTypes.bool
}

export default React.memo(FollowersAndFolloweeCard)