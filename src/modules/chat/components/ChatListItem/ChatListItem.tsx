import React, { useMemo } from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import _get from 'lodash/get';
import groupReader from '@/src/modules/chat/readers/groupReader';
import _isNaN from 'lodash/isNaN';
import styles from './ChatListItem.style';
import useChatContext from '../../hooks/useChatContext';

function timeSince(lastMessageTimeStr) {
  const lastMessageTime = new Date(lastMessageTimeStr);
  if (!lastMessageTime || _isNaN(lastMessageTime)) return '';
  const now = new Date();
  const diffInSeconds = Math.floor((now - lastMessageTime) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (_isNaN(diffInSeconds)) {
    return '';
  }

  if (diffInDays > 0) {
    return `${diffInDays} day(s) ago`;
  }
  if (diffInHours > 0) {
    return `${diffInHours} hour(s) ago`;
  }
  if (diffInMinutes > 0) {
    return `${diffInMinutes} minute(s) ago`;
  }
  return `${diffInSeconds} second(s) ago`;
}

const ChatListItem = ({
  item,
  onPress,
  containerStyle = {},
}: {
  item: any;
  onPress: () => void;
  containerStyle?: any;
}) => {
  const { currentGroupId } = useChatContext();
  const userInfo = groupReader.individual(item) ?? EMPTY_OBJECT;
  const profileImageUrl = _get(userInfo, 'profileImageUrl', '');
  const name = _get(userInfo, 'name', 'Anonymous');
  const lastMessage = _get(item, 'lastMessage', '');
  const isActive = useMemo(
    () => currentGroupId === _get(item, '_id'),
    [currentGroupId, item],
  );
  return (
    <Pressable
      style={[
        styles.chatItemContainer,
        containerStyle,
        isActive && styles.activeChatItemContainer,
      ]}
      onPress={() => onPress?.()}
    >
      <View style={[styles.chatItem]}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: profileImageUrl }} style={styles.avatar} />
        </View>

        <View style={styles.messageContent}>
          <View style={styles.messageHeader}>
            <Text style={styles.userName}>{name || 'Anonymous'}</Text>
            <Text style={styles.timeText}>
              {timeSince(lastMessage?.createdAt)}
            </Text>
          </View>

          <View style={styles.messageFooter}>
            <Text style={styles.messageText} numberOfLines={2}>
              {lastMessage?.content ?? `You and ${name} are now Friends...`}
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default React.memo(ChatListItem);
