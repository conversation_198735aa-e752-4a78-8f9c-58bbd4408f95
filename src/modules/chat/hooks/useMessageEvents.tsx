import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';
import _get from 'lodash/get';
import { MESSAGE_EVENTS } from '../constants/events';
import { CreateMessageInput } from '../types/messages';
import { WEBSOCKET_CHANNELS } from 'core/constants/websocket';
import useWebsocketStore from 'store/useWebSocketStore';
import EventManager from '@/src/core/event';
import { events, listenersNamespace } from '@/src/core/event/constants';

const useMessageEvents = () => {
  const { userId }: any = useSession();
  const { sendMessage } = useWebsocketStore((state) => ({
    sendMessage: state.sendMessage,
  }));
  const channel = WEBSOCKET_CHANNELS.UserEvents(userId);

  const [message, setMessage] = useState(null);

  const sendNewMessage = useCallback(
    (message: CreateMessageInput) => {
      sendMessage({
        type: 'addMessage',
        channel,
        data: {
          ...message,
        },
      });
    },
    [sendMessage],
  );

  useEffect(() => {
    const eventManager = new EventManager();
    const subscription = eventManager.on(
      events.MessagePublishedEvent,
      listenersNamespace.MessagePublishedEvent,
      (data: any) => {
        setMessage(data);
      },
    );
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { message, sendNewMessage };
};

export default useMessageEvents;
