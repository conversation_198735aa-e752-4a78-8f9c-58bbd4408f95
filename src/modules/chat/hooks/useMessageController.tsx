import { useCallback, useEffect } from 'react';
import _get from 'lodash/get';
import useMessagesQuery from './useMessagesQuery';
import useMessageEvents from './useMessageEvents';
import { CreateMessageInput, Message } from '../types/messages';

const useMessagesController = (groupId: string) => {
  const { messages, loading, error, loadMore, hasMore, addNewMessage } =
    useMessagesQuery(groupId);

  const { message, sendNewMessage } = useMessageEvents();

  const _sendNewMessage = useCallback(
    (_message: CreateMessageInput) => {
      sendNewMessage(_message);
      addNewMessage(_message as Message);
    },
    [sendNewMessage, addNewMessage],
  );

  useEffect(() => {
    if (message && _get(message, 'groupId', '') === groupId) {
      addNewMessage(message);
    }
  }, [addNewMessage, message, groupId]);

  return {
    messages,
    loading,
    error,
    loadMore,
    hasMore,
    sendNewMessage: _sendNewMessage,
  };
};

export default useMessagesController;
