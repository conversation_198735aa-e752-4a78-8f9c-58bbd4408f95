import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useLazyQuery } from '@apollo/client';
import _size from 'lodash/size';
import _get from 'lodash/get';
import { useSession } from 'modules/auth/containers/AuthProvider';
import _find from 'lodash/find';
import { SORT_DIRECTION } from '@/src/core/constants/chatConstants';
import { CreateMessageInput, Message } from '../types/messages';
import { Group } from '../types/groups';
import { MESSAGE_EVENTS } from '../constants/events';
import groupReader from '../readers/groupReader';
import { GET_MESSAGE_GROUP, GET_MESSAGES } from '../graphql';
import {
  GroupsParams,
  LAST_MESSAGE_ID,
  MessagesParams,
  MessagingContextType,
} from '../types';
import ChatContext, { ChatContextProvider } from '../context';
import useWebsocketStore from 'store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from 'core/constants/websocket';

const PAGE_SIZE = 50;
const EMPTY_OBJECT = {};

const useChat = () => {
  const { userId }: any = useSession();
  const { isConnected, lastMessage, joinChannel, sendMessage } =
    useWebsocketStore((state) => ({
      isConnected: state.isConnected,
      lastMessage: state.lastMessage,
      joinChannel: state.joinChannel,
      sendMessage: state.sendMessage,
    }));

  const channel = WEBSOCKET_CHANNELS.UserEvents(userId);

  const [messages, setMessages] = useState<Message[]>([]);
  const [messagesParams, setMessagesParams] = useState<MessagesParams>({
    lastMessageId: null,
    hasMore: true,
  });

  const [groups, setGroups] = useState<Group[]>([]);
  const [groupsParams, setGroupsParams] = useState<GroupsParams>({
    nextPage: 1,
    hasMore: true,
  });

  const [currentGroupId, setCurrentGroupId] = useState<string | null>(null);
  const currentGroup: Group | any = useMemo(() => {
    if (!groups || _size(groups) === 0) return EMPTY_OBJECT;
    return _find(groups, { _id: currentGroupId }) ?? EMPTY_OBJECT;
  }, [currentGroupId, groups]);

  const [messageLoadingCustom, setMessageLoadingCustom] = useState(false);

  const [fetchMessages, { loading: messagesLoading, error: messagesError }] =
    useLazyQuery(GET_MESSAGES, {
      fetchPolicy: 'network-only',
      notifyOnNetworkStatusChange: true,
    });

  const [fetchMessageGroups, { loading: groupsLoading, error: groupsError }] =
    useLazyQuery(GET_MESSAGE_GROUP, {
      fetchPolicy: 'network-only',
      notifyOnNetworkStatusChange: true,
    });

  useEffect(() => {
    if (isConnected) {
      joinChannel(channel);
    }
  }, [isConnected, joinChannel, channel]);

  const incomingMessage = React.useMemo(() => {
    let _message = null;
    const data = _get(lastMessage, channel, EMPTY_OBJECT);
    const _event = _get(data, 'type', '');

    if (_event === MESSAGE_EVENTS.MESSAGE_PUBLISHED_EVENT) {
      const _payload = _get(data, 'event', EMPTY_OBJECT);
      _message = _get(_payload, 'message');
    }

    return _message;
  }, [lastMessage, channel]);

  const addNewMessage = useCallback((newMessage: Message) => {
    setMessages((prevMessages) => [newMessage, ...prevMessages]);
  }, []);

  const addNewMessageRef = useRef(addNewMessage);
  addNewMessageRef.current = addNewMessage;

  useEffect(() => {
    if (incomingMessage) {
      const incomingMessageGroupId = _get(incomingMessage, 'groupId');

      if (incomingMessageGroupId === currentGroupId) {
        addNewMessageRef.current(incomingMessage);
      }

      setGroups((prevGroups) =>
        prevGroups.map((group) => {
          if (groupReader.id(group) === incomingMessageGroupId) {
            return {
              ...group,
              lastMessage: incomingMessage,
            };
          }
          return group;
        }),
      );
    }
  }, [incomingMessage, currentGroupId]);

  const getMessagesOnGroupChange = useCallback(async () => {
    if (!currentGroupId) {
      setMessages([]);
      return;
    }

    setMessagesParams({
      lastMessageId: null,
      hasMore: true,
    });

    setMessageLoadingCustom(true);

    const response = await fetchMessages({
      variables: {
        groupId: currentGroupId,
        pageSize: PAGE_SIZE,
        lastMessageId: null,
        sortDirection: SORT_DIRECTION.DESC,
      },
    });

    setMessageLoadingCustom(false);
    const { data } = response;
    const { getMessagesByGroupId: messagesObject } = data ?? EMPTY_OBJECT;
    const messagesFetched = _get(messagesObject, 'messages', []);

    if (_size(messagesFetched) === 0) {
      setMessages([]);
      return;
    }

    const nextLastMessageId: LAST_MESSAGE_ID = _get(
      messagesObject,
      'lastMessageId',
      null,
    );
    const hasMore = _get(messagesObject, 'hasMore', false);

    setMessages(() => messagesFetched);
    setMessagesParams((prevParams) => ({
      ...prevParams,
      lastMessageId: nextLastMessageId,
      hasMore,
    }));
  }, [fetchMessages, currentGroupId]);

  const getMoreMessages = useCallback(
    async (lastMessageId: LAST_MESSAGE_ID) => {
      if (messagesLoading || !currentGroupId) return;

      const response = await fetchMessages({
        variables: {
          groupId: currentGroupId,
          pageSize: PAGE_SIZE,
          lastMessageId,
          sortDirection: SORT_DIRECTION.DESC,
        },
      });

      const { data } = response;
      const { getMessagesByGroupId: messagesObject } = data ?? EMPTY_OBJECT;
      const messagesFetched = _get(messagesObject, 'messages', []);

      if (_size(messagesFetched) === 0) return;

      const nextLastMessageId: LAST_MESSAGE_ID = _get(
        messagesObject,
        'lastMessageId',
        null,
      );
      const hasMore = _get(messagesObject, 'hasMore', false);

      setMessages((prevMessages) => [...prevMessages, ...messagesFetched]);
      setMessagesParams((prevParams) => ({
        ...prevParams,
        lastMessageId: nextLastMessageId,
        hasMore,
      }));
    },
    [fetchMessages, messagesLoading, currentGroupId],
  );

  const getMessageGroups = useCallback(
    async (pageToFetch: number) => {
      if (groupsLoading) return;

      const response = await fetchMessageGroups({
        variables: {
          input: {
            page: pageToFetch,
            pageSize: PAGE_SIZE,
          },
        },
      });

      const { data } = response;
      const { getAllMessageGroups: messageGroupsObject } = data ?? EMPTY_OBJECT;
      const fetchedGroups = _get(messageGroupsObject, 'groups', []);

      if (_size(fetchedGroups) === 0) return;

      const nextPage = _get(messageGroupsObject, 'nextPage', pageToFetch);
      const hasMore = _get(messageGroupsObject, 'hasMore', false);

      setGroups((prevGroups) => [...prevGroups, ...fetchedGroups]);
      setGroupsParams((prevParams) => ({
        ...prevParams,
        nextPage,
        hasMore,
      }));
    },
    [fetchMessageGroups, groupsLoading],
  );

  const updateLastMessage = useCallback((message) => {
    setGroups((prevGroups) =>
      prevGroups.map((group) => {
        if (groupReader.id(group) === message.groupId) {
          return {
            ...group,
            lastMessage: message,
          };
        }
        return group;
      }),
    );
  }, []);

  const sendNewMessage = useCallback(
    (messageInput: CreateMessageInput) => {
      sendMessage?.({
        type: 'addMessage',
        channel,
        data: {
          ...messageInput,
        },
      });
      updateLastMessage(messageInput);
      addNewMessage(messageInput as Message);
    },
    [sendMessage, updateLastMessage, addNewMessage],
  );

  const loadMoreMessages = useCallback(() => {
    if (messagesLoading) return;
    if (!messagesParams.hasMore) return;
    getMoreMessages(messagesParams.lastMessageId);
  }, [getMoreMessages, messagesLoading, messagesParams]);

  const loadMoreGroups = useCallback(() => {
    if (groupsLoading) return;
    if (!groupsParams.hasMore) return;
    getMessageGroups(groupsParams.nextPage);
  }, [getMessageGroups, groupsLoading, groupsParams]);

  useEffect(() => {
    getMessageGroups(groupsParams.nextPage);
  }, []);

  useEffect(() => {
    getMessagesOnGroupChange();
  }, [currentGroupId, getMessagesOnGroupChange]);

  const value: MessagingContextType = {
    messages,
    messagesLoading: messageLoadingCustom || messagesLoading,
    messagesError,
    loadMoreMessages,
    hasMoreMessages: messagesParams.hasMore,
    sendNewMessage,

    groups,
    groupsLoading,
    groupsError,
    loadMoreGroups,
    hasMoreGroups: groupsParams.hasMore,
    currentGroup,

    currentGroupId,
    setCurrentGroupId,
  };
  return value;
};

export const WithChatContext = (Component: any) => {
  const ChatContextWrapper = (props) => {
    const contextValue = useChat();
    return (
      <ChatContextProvider value={contextValue}>
        <Component {...props} />
      </ChatContextProvider>
    );
  };
  return React.memo(ChatContextWrapper);
};

const useChatContext = () => useContext(ChatContext);
export default useChatContext;
