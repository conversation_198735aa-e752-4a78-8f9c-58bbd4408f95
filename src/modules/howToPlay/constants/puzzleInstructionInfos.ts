import { InstructionStep } from 'modules/puzzles/components/PuzzleInstruction';
import dark from 'core/constants/themes/dark';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';

const crossMathInstructions: InstructionStep[] = [
  {
    title: 'BODMAS rule',
    description:
      'Follow BODMAS rule to solve this puzzle. Division and multiplication will have highest priority',
  },
  {
    title: 'How To Fill',
    description:
      'Tap on the grid cell that you want to fill. Then tap on the number in the footer row. The grid cell will be filled.',
  },
  {
    title: 'Undo Redo',
    description:
      'Tap on the UNDO icon for UNDO and tap on the REDO icon for REDO.',
    icon: {
      name: 'undo-variant',
      size: 18,
      color: dark.colors.textDark,
    },
  },
  {
    title: 'Delete Filled Cell',
    description:
      'You can tap on the filled grid cell to remove it from the grid.',
  },
  {
    title: 'Use Logic',
    description:
      'Start with the cells that have the most constraints (e.g., cells with only one possible number).',
  },
  {
    title: 'Repeat',
    description:
      'Continue filling in numbers and checking your work until the entire grid is complete.',
  },
];

const kenKenInstructions: InstructionStep[] = [
  {
    title: 'Understand the Grid and Goal',
    description:
      'Look at the grid (e.g., 4x4, 6x6). Your goal is to fill every square with a number.',
    rules: [
      "For an N×N grid (like 4x4), you'll use the numbers 1 to N (so, 1, 2, 3, and 4 for a 4x4 grid).",
      'Each number must appear exactly once in each row.',
      'Each number must appear exactly once in each column.',
    ],
    image:
      'https://cdn.matiks.com/files/66fc19d744b74099e82125b3_kenken-puzzle.jpg?timestamp=1745249898',
  },
  {
    title: 'Understand the Cages',
    description:
      'You\'ll see heavily outlined areas called "cages." These contain one or more squares.',
    rules: [
      'Each cage has a "clue" in the corner: a target number and a mathematical operation (+, -, ×, ÷).',
    ],
    image:
      'https://cdn.matiks.com/files/66fc19d744b74099e82125b3_cages.jpg?timestamp=1745249856',
  },
  {
    title: 'Use the Cage Clues',
    description:
      "The numbers you place inside a cage must combine using that cage's operation to equal its target number.",
    examples: [
      {
        text: 'A cage marked "+10" with three squares could contain 1, 4, and 5 (since 1+4+5=10).',
      },
      {
        text: 'A cage marked "-5" means two squares whose difference is 5. for example it could contain 1 and 6 in any order (since 6-1=5).',
      },
      {
        text: 'A cage marked "÷2" with two squares could contain a 4 and a 2 (since 4÷2=2) or a 2 and 1 (since 2÷1=2). Order does not matter for subtraction and division!',
      },
      {
        text: 'Important: Numbers can repeat within a cage, if they are in different rows or columns.',
        highlight: true,
      },
    ],
    image:
      'https://cdn.matiks.com/files/66fc19d744b74099e82125b3_understanding-cages.jpg?timestamp=1745249936',
  },
  {
    title: 'Start with Easy Cages',
    description:
      'Look for cages that contain only one square. These are the easiest!',
    rules: [
      'The clue in a single-square cage simply tells you the number that goes in that square. Fill these in first.',
    ],
    image:
      'https://cdn.matiks.com/files/66fc19d744b74099e82125b3_single-cages-examples.jpg?timestamp=1745249917',
  },
  {
    title: 'Use Logic and Deduction',
    description:
      'For cages with multiple squares, figure out the possible combinations of numbers (from 1 to N) that satisfy the clue.',
    rules: [
      "Combine this with the row/column rules. If a 2-square cage needs a '1' and a '3' ('3x'), and one square is in a row that already has a '1', then that square must be the '3', and the other square in the cage must be the '1'.",
    ],
    // image: logicDeductionImage,
  },
  {
    title: 'Check and Complete',
    description:
      'Keep filling squares using the cage clues and the row/column rules.',
    rules: [
      'Once the grid is full, double-check:',
      'Does every row contain numbers 1 to N exactly once?',
      'Does every column contain numbers 1 to N exactly once?',
      'Does every cage calculate correctly?',
    ],
  },
];

export const PUZZLES_HOW_TO_PLAY_INFOS = {
  [PUZZLE_TYPES.CROSS_MATH_PUZZLE]: {
    steps: crossMathInstructions,
    title: 'How to Solve Cross Math Puzzle',
  },
  [PUZZLE_TYPES.KEN_KEN_PUZZLE]: {
    steps: kenKenInstructions,
    title: 'How to Solve Your First KenKen Puzzle',
    subtitle:
      "It's a fun logic puzzle that combines arithmetic with Sudoku-like rules. Here's how to get started:",
  },
};
