import React, { useCallback, useMemo, useState } from 'react';
import {
  Dimensions,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { TabView } from 'react-native-tab-view';
import _includes from 'lodash/includes';
import _findIndex from 'lodash/findIndex';
import _isEqual from 'lodash/isEqual';
import _map from 'lodash/map';
import Header from 'shared/Header';
import dark from 'core/constants/themes/dark';
import {
  PUZZLE_TYPES,
  VALID_PUZZLE_TYPES,
} from 'modules/puzzles/types/puzzleType';
import { PUZZLE_TYPES_VS_LABEL } from 'modules/puzzles/constants/puzzleConstants';

import _size from 'lodash/size';
import { GenericPuzzleInstructionContent } from 'modules/puzzles/components/PuzzleInstruction';
import { PUZZLES_HOW_TO_PLAY_INFOS } from 'modules/howToPlay/constants/puzzleInstructionInfos';
import useMediaQuery from 'core/hooks/useMediaQuery';
import styles from './PuzzleGameHowToPlay.style';

const initialLayout = { width: Dimensions.get('window').width };

const PuzzleTabCard = ({
  puzzleType,
  onPress,
}: {
  puzzleType: string;
  onPress: () => void;
}) => {
  const {
    puzzleType: currentActivePuzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  } = useLocalSearchParams();

  const isActive = _isEqual(currentActivePuzzleType, puzzleType);

  return (
    <TouchableOpacity
      style={[
        styles.tabContainer,
        isActive && { borderColor: dark.colors.puzzle.primary },
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.categoryText,
          isActive && { color: dark.colors.puzzle.primary },
        ]}
      >
        {PUZZLE_TYPES_VS_LABEL[puzzleType] || puzzleType}
      </Text>
    </TouchableOpacity>
  );
};

const PuzzleGameHowToPlay = () => {
  const { puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE } =
    useLocalSearchParams();

  const { isMobile: isCompactMode } = useMediaQuery();

  const validatedPuzzleType = useMemo(
    () =>
      _includes(VALID_PUZZLE_TYPES, puzzleType)
        ? puzzleType
        : PUZZLE_TYPES.CROSS_MATH_PUZZLE,
    [puzzleType],
  );

  const routes = useMemo(
    () =>
      VALID_PUZZLE_TYPES.map((type) => ({
        key: type,
        title: PUZZLE_TYPES_VS_LABEL[type] || type,
      })),
    [],
  );

  const initialIndex = useMemo(
    () =>
      _findIndex(VALID_PUZZLE_TYPES, (type) => type === validatedPuzzleType),
    [validatedPuzzleType],
  );

  const [index, setIndex] = useState(initialIndex >= 0 ? initialIndex : 0);

  const onIndexChange = useCallback((activeIndex: number) => {
    if (activeIndex >= _size(VALID_PUZZLE_TYPES)) {
      return;
    }
    setIndex(activeIndex);
    router.setParams({ puzzleType: VALID_PUZZLE_TYPES?.[activeIndex] });
  }, []);

  const renderScene = useCallback(
    ({ route }: { route: any }) => (
      <View style={styles.contentContainer}>
        <GenericPuzzleInstructionContent
          {...PUZZLES_HOW_TO_PLAY_INFOS?.[route?.key]}
        />
      </View>
    ),
    [],
  );

  const renderTabBar = useCallback(
    () => (
      <View style={styles.tabBarContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.pillsContainer}
        >
          {_map(VALID_PUZZLE_TYPES, (type, index) => (
            <PuzzleTabCard
              key={type}
              puzzleType={type}
              onPress={() => onIndexChange(index)}
            />
          ))}
        </ScrollView>
      </View>
    ),
    [],
  );

  return (
    <View
      style={[
        styles.container,
        !isCompactMode && { paddingLeft: 50, paddingTop: 40 },
      ]}
    >
      <Header title="How to Play" />
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={onIndexChange}
        initialLayout={initialLayout}
        renderTabBar={renderTabBar}
      />
    </View>
  );
};

export default React.memo(PuzzleGameHowToPlay);
