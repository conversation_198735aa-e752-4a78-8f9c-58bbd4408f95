import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  tabBarContainer: {
    paddingVertical: 8,
  },
  pillsContainer: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    paddingHorizontal: 16,
  },
  tabContainer: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    flexDirection: 'row',
    gap: 4,
    minHeight: 30,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 12,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-600',
  },
  contentContainer: {
    flex: 1,
    padding: 16,
  },
});

export default styles;
