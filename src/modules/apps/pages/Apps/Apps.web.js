import React, { useCallback, useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { APPSTORE_LINK, PLAYSRORE_LINK } from 'core/constants/appConstants';
import useDeviceOS from 'core/hooks/useDeviceOS';
import * as Linking from 'expo-linking';
import _keys from 'lodash/keys';
import _isNil from 'lodash/isNil';
import { router } from 'expo-router';

const Apps = () => {
  const { isAndroid, isIOS } = useDeviceOS();

  const [agent, setAgent] = useState();

  const getQueryParamsString = useCallback(() => {
    const { queryParams } = Linking.parse(window.location.href);
    return _keys(queryParams)
      .filter((key) => !_isNil(queryParams[key]))
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`,
      )
      .join('&');
  }, []);

  const redirectToHome = useCallback(() => {
    const baseUrl = 'https://www.matiks.com';
    const queryString = getQueryParamsString();
    const finalUrl = `${baseUrl}?${queryString}`;
    router.replace(finalUrl);
  }, [getQueryParamsString]);

  useEffect(() => {
    const redirectToStore = () => {
      const queryString = getQueryParamsString();
      if (isIOS) {
        if (queryString) {
          window.location.href = `${APPSTORE_LINK}&${queryString}`;
        } else {
          window.location.href = APPSTORE_LINK;
        }
        setAgent('Apple Store');
        setTimeout(() => redirectToHome(), 2000);
      } else if (isAndroid) {
        if (queryString) {
          window.location.href = `${PLAYSRORE_LINK}&${queryString}`;
        } else {
          window.location.href = PLAYSRORE_LINK;
        }
        setAgent('Play store');
        setTimeout(() => redirectToHome(), 2000);
      } else {
        redirectToHome();
        setAgent('Matiks');
      }
    };
    redirectToStore();
  }, [isIOS, isAndroid]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: 'white',
        width: '100%',
        height: '100%',
      }}
    >
      <Text
        style={{
          fontSize: 20,
          textAlign: 'center',
          marginTop: 100,
          color: 'black',
          fontFamily: 'Montserrat-500',
        }}
      >
        Redirecting to {agent}...
      </Text>
    </View>
  );
};

Apps.propTypes = {};

export default React.memo(Apps);
