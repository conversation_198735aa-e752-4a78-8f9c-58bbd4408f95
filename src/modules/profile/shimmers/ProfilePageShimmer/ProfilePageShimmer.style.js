import { StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: 1,
        overflow: "hidden",
        paddingVertical:16,
        paddingHorizontal:16,
        width:"100%",
        height:"100%"
    }
})

const useProfileShimmerStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useProfileShimmerStyles