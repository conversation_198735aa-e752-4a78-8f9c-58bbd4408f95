import userReader from '@/src/core/readers/userReader';
import { WEEKLY_LEAGUE_INFO } from '@/src/modules/matiksWeeklyLeague/constants/weeklyLeagueTypes';
import MaxStreakIcon from '@/assets/images/userStats/max_streak.png';
import TotalXpIcon from '@/assets/images/userStats/total_xp.png';
import TotalGamesIcon from '@/assets/images/userStats/total_games.png';
import blitzIcon from '@/assets/images/icons/blitz_home.png';
import classicIcon from '@/assets/images/icons/classic_home.png';
import memoryIcon from '@/assets/images/icons/memory.png';
import puzzleIcon from '@/assets/images/icons/puzzle_icon.png';
import puzzleDisabledIcon from '@/assets/images/icons/puzzle_disabled.png';
import disabledBlitzIcon from '@/assets/images/icons/blitz_disabled.png';
import disabledClassicIcon from '@/assets/images/icons/classic_disabled.png';
import disabledMemoryIcon from '@/assets/images/icons/memory_disabled.png';
import peakRatingIcon from '@/assets/images/icons/peak_rating.png';
import winLossIcon from '@/assets/images/icons/win_loss.png';
import { GAME_CATEGORIES } from 'modules/home/<USER>/gameTypes';
import _get from "lodash/get";

export const GET_USER_STATS = (user) => [
  {
    title: 'MAX STREAK',
    value: userReader.longestStreak(user),
    icon: MaxStreakIcon,
  },
  {
    title: 'TOTAL XP',
    value: userReader.statikCoins(user),
    icon: TotalXpIcon,
  },
  {
    title: 'LEAGUE',
    value: userReader.league(user),
    icon: WEEKLY_LEAGUE_INFO[userReader.league(user)]?.image,
  },
  {
    title: 'TOTAL GAMES',
    value: userReader.gamesPlayed(user),
    icon: TotalGamesIcon,
  },
];

export const GET_USER_STATISTICS = (stats) => {
  if (!stats) {
    return [
      { title: 'GAMES PLAYED', value: '-', icon: TotalGamesIcon }, 
      { title: 'XP GAINED', value: '-', icon: TotalXpIcon },
      { title: 'PEAK RATING', value: '-', icon: peakRatingIcon },
      { title: 'WIN : LOSS', value: '- : -', icon: winLossIcon },
    ];
  }

  const gamesPlayed = _get(stats, 'gamesPlayed', 0);
  const xpGained = _get(stats, 'xpGained', 0);
  const peakRating = _get(stats, 'peakRating', 0);
  const wins = _get(stats, 'wins', 0);
  const losses = _get(stats, 'losses', 0);

  return [
    {
      title: 'GAMES PLAYED',
      value: gamesPlayed,
      icon: TotalGamesIcon,
    },
    {
      title: 'XP GAINED',
      value: xpGained,
      icon: TotalXpIcon,
    },
    {
      title: 'PEAK RATING',
      value: peakRating,
      icon: peakRatingIcon,
    },
    {
      title: 'WIN : LOSS',
      value: `${wins} : ${losses}`,
      icon: winLossIcon,
    },
  ];
};

export const USER_RATINGS = (user) => [
  {
    ratingName: GAME_CATEGORIES.BLITZ,
    icon: blitzIcon,
    disabledIcon: disabledBlitzIcon,
    rating: userReader.rating(user),
  },
  {
    ratingName: GAME_CATEGORIES.CLASSIC,
    icon: classicIcon,
    disabledIcon: disabledClassicIcon,
    rating: userReader.abilityDuelsRating(user),
  },
  {
    ratingName: GAME_CATEGORIES.MEMORY,
    icon: memoryIcon,
    disabledIcon: disabledMemoryIcon,
    rating: userReader.flashAnzanRating(user),
  },
  {
    ratingName: GAME_CATEGORIES.PUZZLE,
    icon: puzzleIcon,
    disabledIcon: puzzleDisabledIcon,
    rating: userReader.puzzleRating(user),
  },
];

export const RATING_CATEGORIES = [
  { key: GAME_CATEGORIES.BLITZ },
  { key: GAME_CATEGORIES.CLASSIC },
  { key: GAME_CATEGORIES.MEMORY },
  { key: GAME_CATEGORIES.PUZZLE },
];

export const RATING_DETAILS_FILTER = [
  {
    key: 'ALL_TIME',
    filter: 'ALL TIME',
  },
  {
    key: 'TODAY',
    filter: 'TODAY',
  },
  {
    key: 'LAST_7_DAYS',
    filter: '7 DAYS',
  },
  {
    key: 'LAST_30_DAYS',
    filter: '30 DAYS',
  },
  {
    key: 'LAST_90_DAYS',
    filter: '90 DAYS',
  },
  {
    key: 'LAST_YEAR',
    filter: '1 YEAR',
  },
];
