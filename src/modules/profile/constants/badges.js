export const BADGES = {
  ROOKIE: 'ROOKIE',
  NOVICE: 'NOVICE',
  AMATEUR: 'AMATEUR',
  EXPERT: 'EXPERT',
  CANDIDATE: 'CANDIDATE_MASTER',
  MASTER: 'MASTER',
  GRAND_MASTER: 'GRANDMASTER',
  LEGENDARY_GRANDMASTER: 'LEGENDARY_GRANDMASTER',
  GOAT: 'GOAT',
};

const BadgesList = [
  // {
  //     title: "ROOKIE",
  //     image: require('@/assets/images/badges/Novice.png'),
  //     activeDescription: "You are among top 23% mathletes on Matiks",
  //     color: '#ACDCCB',
  //     unActiveDescription: "You will get this badge if your rating is below 999."
  // },
  {
    key: BADGES.NOVICE,
    title: 'NOVICE',
    image: require('@/assets/images/badges/Novice.png'),
    activeDescription: 'You are among top 23% mathletes on Matiks',
    color: '#ACDCCB',
    unActiveDescription: 'Cross the rating of 1000 to get the Novice title.',
    howToEarnDescription: 'Cross the rating of 1000 to get the Novice title.',
    earnedDescription: 'People with rating 1000-1199 get the Novice title.',
  },
  {
    key: BADGES.AMATEUR,
    title: 'AMATEUR',
    image: require('@/assets/images/badges/Amateur.png'),
    activeDescription: 'You are among top 23% mathletes on Matiks',
    color: '#9BDBF3',
    unActiveDescription:
      'Earn a rating between 1200-1599 to hold the Amateur title.',
    howToEarnDescription: 'Cross the rating of 1200 to get the Amateur title.',
    earnedDescription: 'People with rating 1200-1599 get the Amateur title.',
  },
  {
    key: BADGES.EXPERT,
    title: 'EXPERT',
    image: require('@/assets/images/badges/Expert.png'),
    activeDescription: 'You are among top 23% mathletes on Matiks',
    color: '#DEC5DF',
    unActiveDescription:
      'Earn a rating between 1600-1999 to hold the Expert title.',
    howToEarnDescription: 'Cross the rating of 1600 to get the Expert title.',
    earnedDescription: 'People with rating 1600-1999 get the Expert title.',
  },
  {
    key: BADGES.CANDIDATE,
    title: 'CANDIDATE',
    image: require('@/assets/images/badges/Candidate.png'),
    activeDescription: 'You are among top 23% mathletes on Matiks',
    color: '#FBDD55',
    unActiveDescription:
      'Earn a rating between 2000-2499 to hold the Candidate title.',
    howToEarnDescription:
      'Cross the rating of 2000 to get the Candidate title.',
    earnedDescription: 'People with rating 2000-2499 get the Candidate title.',
  },
  {
    key: BADGES.MASTER,
    title: 'MASTER',
    image: require('@/assets/images/badges/Master.png'),
    activeDescription: 'You are among top 23% mathletes on Matiks',
    color: '#F9C9DA',
    unActiveDescription:
      'Earn a rating between 2500-2999 to hold the Master title.',
    howToEarnDescription: 'Cross the rating of 2500 to get the Master title.',
    earnedDescription: 'People with rating 2500-2999 get the Master title.',
  },
  {
    key: BADGES.GRAND_MASTER,
    title: 'GRAND MASTER',
    image: require('@/assets/images/badges/GrandMaster.png'),
    activeDescription: 'You are among top 23% mathletes on Matiks',
    color: '#F49199',
    unActiveDescription:
      'Earn a rating between 3000-3499 to hold the Grand Master title.',
    howToEarnDescription:
      'Cross the rating of 3000 to get the Grand Master title.',
    earnedDescription:
      'People with rating 3000-3499 get the Grand Master title.',
  },
  // {
  //     title: "INTERNATIONAL GRAND MASTER",
  //     image: require('@/assets/images/badges/GrandMaster.png'),
  //     activeDescription: "You are among top 23% mathletes on Matiks",
  //     color: '#F49199',
  //     unActiveDescription: "Earn a rating between 3500-3999 to hold the Expert title."
  // }
];

export const BADGES_DETAILS = {
  [BADGES.ROOKIE]: {
    key: BADGES.ROOKIE,
    title: 'ROOKIE',
    image: require('@/assets/images/badges/Novice.png'),
    color: '#9BDBF3',
    shortForm: 'RK',
    description: 'You will get this badge if your rating is below 999.',
    badgeEarnDescription: 'First Badge unlocked - the journey begins here!',
    background: require('@/assets/images/ratingBackgrounds/amateur.png'),
  },
  [BADGES.NOVICE]: {
    key: BADGES.NOVICE,
    title: 'NOVICE',
    image: require('@/assets/images/badges/Novice.png'),
    color: '#ACDCCB',
    shortForm: 'NV',
    description: 'Earn a rating between 1000-1199 to hold the Novice title.',
    badgeEarnDescription: 'First Badge unlocked - the journey begins here!',
    background: require('@/assets/images/ratingBackgrounds/novice.png'),
  },
  [BADGES.AMATEUR]: {
    key: BADGES.AMATEUR,
    title: 'AMATEUR',
    shortForm: 'AM',
    image: require('@/assets/images/badges/Amateur.png'),
    color: '#9BDBF3',
    description: 'Earn a rating between 1200-1599 to hold the Amateur title.',
    badgeEarnDescription: 'Rising Fast! Challenge yourself to the next tier!',
    background: require('@/assets/images/ratingBackgrounds/amateur.png'),
  },
  [BADGES.EXPERT]: {
    key: BADGES.EXPERT,
    title: 'EXPERT',
    shortForm: 'EX',
    image: require('@/assets/images/badges/Expert.png'),
    color: '#DEC5DF',
    description: 'Earn a rating between 1600-1999 to hold the Expert title.',
    badgeEarnDescription:
      "You've reached Expert! Challenge yourself to the next tier!",
    background: require('@/assets/images/ratingBackgrounds/expert.png'),
  },
  [BADGES.CANDIDATE]: {
    key: BADGES.CANDIDATE,
    title: 'CANDIDATE',
    shortForm: 'CD',
    image: require('@/assets/images/badges/Candidate.png'),
    color: '#FBDD55',
    description: 'Earn a rating between 2000-2499 to hold the Candidate title.',
    badgeEarnDescription: 'Congratulations on becoming master!',
    background: require('@/assets/images/ratingBackgrounds/candidate.png'),
  },
  [BADGES.MASTER]: {
    key: BADGES.MASTER,
    title: 'MASTER',
    shortForm: 'MS',
    image: require('@/assets/images/badges/Master.png'),
    color: '#F9C9DA',
    description: 'Earn a rating between 2500-2999 to hold the Master title.',
    badgeEarnDescription: 'Congratulations on becoming master!',
    background: require('@/assets/images/ratingBackgrounds/master.png'),
  },
  [BADGES.GRAND_MASTER]: {
    key: BADGES.GRAND_MASTER,
    title: 'GRAND MASTER',
    shortForm: 'GM',
    image: require('@/assets/images/badges/GrandMaster.png'),
    color: '#F49199',
    description:
      'Earn a rating between 3000-3499 to hold the Grand Master title.',
    badgeEarnDescription: 'Congratulations on becoming Grand Master!',
    background: require('@/assets/images/ratingBackgrounds/grand_master.png'),
  },
  [BADGES.LEGENDARY_GRANDMASTER]: {
    key: BADGES.LEGENDARY_GRANDMASTER,
    title: 'GOATED',
    shortForm: 'GT',
    image: require('@/assets/images/badges/GrandMaster.png'),
    color: '#ED7648',
    description:
      'Earn a rating between 3500-3999 to hold the Legendary Grand Master title.',
    badgeEarnDescription: 'Congratulations on becoming Legendary Grand Master!',
    background: require('@/assets/images/ratingBackgrounds/goat.png'),
  },
  [BADGES.GOAT]: {
    key: BADGES.GOAT,
    title: 'GOATED',
    shortForm: 'GT',
    image: require('@/assets/images/badges/GrandMaster.png'),
    color: '#ED7648',
    description: 'Earn a rating between 3500-3999 to hold the Goat title.',
    badgeEarnDescription: 'Congratulations on becoming Goat!',
    background: require('@/assets/images/ratingBackgrounds/goat.png'),
  },
};

export default BadgesList;
