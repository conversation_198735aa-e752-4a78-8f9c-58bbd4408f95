import { ICON_TYPES } from "@/src/components/atoms/Icon";
import { FRIENDSHIP_STATUS } from "../../friendsAndFollowers/constants/friendshipStatus";

export const FRIENDS_BUTTON_CONFIG = {
      [FRIENDSHIP_STATUS.ACCEPTED]: {
        label: 'ADDED',
        iconName: 'user-check',
        iconType: ICON_TYPES.FONT_AWESOME_5,
      },
      [FRIENDSHIP_STATUS.PENDING_REQUEST]: {
        label: 'ACCEPT',
        iconName: 'user-check',
        iconType: ICON_TYPES.FONT_AWESOME_5,
      },
      [FRIENDSHIP_STATUS.REQUEST_SENT]: {
        label: 'WITHDRAW',
        iconName: 'user-x',
        iconType: ICON_TYPES.FEATHER,
      },
    };