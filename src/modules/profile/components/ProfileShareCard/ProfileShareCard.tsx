import React, { useRef, useState } from 'react';
import { Image, Platform, Text, View } from 'react-native';
import styles from './ProfileShareCard.style';
import userReader from '@/src/core/readers/userReader';
import { BADGES, BADGES_DETAILS } from '../../constants/badges';
import useCaptureView from '@/src/core/hooks/useCaptureView'; // Import the hook
import dark from '@/src/core/constants/themes/dark';
import _get from 'lodash/get';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { showToast, TOAST_TYPE } from '@/src/components/molecules/Toast';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { USER_RATINGS } from 'modules/profile/constants/constants';
import _slice from 'lodash/slice';

const matiksLogo = require('@/assets/images/LinearGradientIcons/matiksBolt.png');
const appStoreLogo = require('@/assets/images/icons/play-store.png');
const playStoreLogo = require('@/assets/images/icons/app-store.png');

interface RatingItemProps {
  icon: any;
  ratingName: string;
  rating: number | null | undefined;
}

interface ProfileShareCardProps {
  user: any;
}

const RatingItem = ({ icon, ratingName, rating }: RatingItemProps) => {
  return (
    <View style={styles.ratingBox}>
      <Image source={icon} style={styles.ratingIcon} resizeMode="contain" />
      <Text style={styles.ratingValue}>{rating ?? '-'}</Text>
      <Text style={styles.ratingName}>{ratingName}</Text>
    </View>
  );
};

const ProfileShareCard = ({ user }: ProfileShareCardProps) => {
  const viewShotRef = useRef();
  const { captureView } = useCaptureView();
  const ratingsRow1 = _slice(USER_RATINGS(user), 0, 2);
  const ratingsRow2 = _slice(USER_RATINGS(user), 2, 4);
  const [isloading, setIsloading] = useState(false);
  const { isMobile: isCompacMode } = useMediaQuery();

  const isWeb = Platform.OS === 'web';
  const userBadgeKey = userReader.badge(user) || BADGES.NOVICE;

  const badgeInfo =
    BADGES_DETAILS[userBadgeKey] ?? BADGES_DETAILS[BADGES.NOVICE];

  const badgeText = _get(badgeInfo, 'shortForm', 'NV');
  const badgeColor = _get(badgeInfo, 'color', dark.colors.textLight);
  const userImage = userReader.profileImageUrl(user);

  const handleDownload = async () => {
    if (!isWeb || !viewShotRef.current) {
      return;
    }
    setIsloading(true);
    const fileName = `matiks-profile-${userReader.username(user) || 'share'}.png`;
    const message = `Check out my Matiks profile! Username: ${userReader.username(user)}`;

    try {
      await captureView({
        viewRef: viewShotRef,
        message: message,
        fileName: fileName,
      });
    } catch (error) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: 'Failed to share profile. Please try again.',
      });
    } finally {
      setIsloading(false);
    }
  };

  return (
    <View style={styles.outerContainer}>
      <View ref={viewShotRef} style={styles.container}>
        <View style={styles.profileImageOverlayContainer}>
          <View style={styles.profileImageContainer}>
            {userImage ? (
              <Image source={{ uri: userImage }} style={styles.userImage} />
            ) : (
              <View
                style={[
                  styles.userImage,
                  { backgroundColor: dark.colors.background },
                ]}
              />
            )}
          </View>

          <View
            style={[
              styles.profileBadgeContainer,
              { backgroundColor: badgeColor },
            ]}
          >
            <Text style={styles.profileBadgeText}>{badgeText}</Text>
          </View>
        </View>

        <View style={styles.ratingsContainer}>
          <View style={styles.ratingsRow}>
            {ratingsRow1.map((item, index) => (
              <RatingItem
                key={`rating-1-${index}`}
                icon={item.icon}
                ratingName={item.ratingName}
                rating={item?.rating}
              />
            ))}
          </View>
          <View style={styles.ratingsRow}>
            {ratingsRow2.map((item, index) => (
              <RatingItem
                key={`rating-2-${index}`}
                icon={item.icon}
                ratingName={item.ratingName}
                rating={item?.rating}
              />
            ))}
          </View>
        </View>

        <View style={styles.userInfoContainer}>
          <Text style={styles.userName}>
            {userReader.displayName(user).toUpperCase()}
          </Text>
          <Text style={styles.userHandle}>
            {`@${userReader.username(user)?.toUpperCase()}`}
          </Text>
        </View>

        <View style={styles.logoContainer}>
          <View style={styles.matiksLogoContainer}>
            <Image
              source={matiksLogo}
              style={styles.matiksLogo}
              resizeMode="contain"
            />
            <Text style={styles.matiksText}>MATIKS</Text>
          </View>
          <View style={styles.appLogosContainer}>
            <Image
              source={appStoreLogo}
              style={styles.appLogo}
              resizeMode="contain"
            />
            <Image
              source={playStoreLogo}
              style={styles.appLogo}
              resizeMode="contain"
            />
          </View>
        </View>
      </View>
      {isWeb && (
        <InteractiveSecondaryButton
          onPress={handleDownload}
          label={isloading ? 'Downloading...' : 'Download'}
          buttonContainerStyle={{
            width: 300,
            marginTop: !isCompacMode ? 20 : 0,
          }}
          labelStyle={styles.downloadLabel}
        />
      )}
    </View>
  );
};

export default React.memo(ProfileShareCard);
