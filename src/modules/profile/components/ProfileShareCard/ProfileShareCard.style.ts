import dark from '@/src/core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    width: 300,
    height: 360,
    backgroundColor: dark.colors.card,
    borderWidth: 0.5,
    borderColor: dark.colors.primary,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    position: 'relative',
  },
  ratingsContainer: {
    width: '100%',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    marginTop: 20,
  },
  ratingsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 10,
  },
  ratingBox: {
    width: '45%',
    aspectRatio: 1.2,
    backgroundColor: dark.colors.background,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 5,
  },
  ratingIcon: {
    width: 24,
    height: 24,
  },
  ratingValue: {
    fontFamily: 'BebasNeue-500',
    fontSize: 24,
    letterSpacing: 1,
    color: dark.colors.textLight,
  },
  ratingName: {
    fontFamily: 'Montserrat-600',
    fontSize: 10,
    letterSpacing: 1,
    color: dark.colors.textDark,
    textTransform: 'uppercase',
  },
  profileImageOverlayContainer: {
    position: 'absolute',
    top: 100,
    left: '52%',
    transform: [{ translateX: -38 }],
    zIndex: 1,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: dark.colors.yellow,
    borderRadius: 50,
  },
  profileImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 40,
    borderWidth: 6,
    borderColor: dark.colors.card,
    overflow: 'hidden',
    backgroundColor: dark.colors.background,
  },
  profileBadgeContainer: {
    position: 'absolute',
    bottom: -10,
    right: 16,
    backgroundColor: dark.colors.yellow,
    borderRadius: 8,
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: dark.colors.card,
  },
  profileBadgeText: {
    color: dark.colors.background,
    fontSize: 10,
    fontFamily: 'Montserrat-800',
  },
  userInfoContainer: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingHorizontal: 20,
    marginTop: 12,
  },
  userName: {
    fontFamily: 'Montserrat-700',
    fontSize: 16,
    color: dark.colors.textLight,
    textAlign: 'center',
    letterSpacing: 1,
  },
  userHandle: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: withOpacity(dark.colors.textLight, 0.4),
    textAlign: 'center',
    marginTop: 2,
    letterSpacing: 1,
  },
  logoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 20,
  },
  matiksLogoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  matiksLogo: {
    width: 16,
    height: 16,
  },
  matiksText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: dark.colors.textDark,
    letterSpacing: 1,
  },
  appLogosContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  appLogo: {
    width: 20,
    height: 20,
  },
  outerContainer: {
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: 'transparent',
  },
  downloadButton: {
    marginBottom: 10,
    paddingVertical: 8,
    paddingHorizontal: 15,
    backgroundColor: dark.colors.primary,
    borderRadius: 5,
  },
  downloadButtonText: {
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-600',
    fontSize: 14,
  },
  downloadLabel: {
    fontFamily: 'Montserrat-600',
    fontSize: 14,
    color: dark.colors.textLight,
  },

  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
});

export default styles;
