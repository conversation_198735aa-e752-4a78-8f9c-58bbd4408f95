import React, { useCallback, useState } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import DeleteOverlay from 'modules/profile/components/DeleteOverlay/DeleteOverlay';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { Pressable, Text, View } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import dark from 'core/constants/themes/dark';
import styles from './DeleteAccountButton.style';
import userReader from 'core/readers/userReader';

const DeleteAccountButton = () => {
  const [isOverlayVisible, setOverlayVisible] = useState(false);
  const { handleDeleteUser, user } = useSession();

  const onPressDelete = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.PROFILE.CLICKED_ON_PROFILE_PAGE_DELETE_ACCOUNT,
    );
    setOverlayVisible(true);
  }, [setOverlayVisible]);

  const onConfirmDelete = useCallback(() => {
    handleDeleteUser();
    setOverlayVisible(false);
    closeRightPane();
  }, [handleDeleteUser, setOverlayVisible]);

  const renderDeleteOverlay = useCallback(() => {
    if (!isOverlayVisible) {
      return;
    }
    return (
      <DeleteOverlay
        overlayStyle={styles.deleteOverlay}
        onCancel={() => setOverlayVisible(false)}
        onConfirm={() => onConfirmDelete()}
        isVisible={isOverlayVisible}
        toggleOverlay={() => setOverlayVisible(false)}
      />
    );
  }, [isOverlayVisible]);

  if (userReader.isGuest(user)) {
    return null;
  }

  return (
    <>
      <Pressable onPress={onPressDelete} style={styles.container}>
        <View style={styles.contentContainer}>
          <MaterialCommunityIcons
            name="delete-forever"
            size={18}
            color={dark.colors.red}
          />
          <Text style={styles.cancelLabelStyle}>Delete Account</Text>
        </View>
      </Pressable>

      {renderDeleteOverlay()}
    </>
  );
};

export default React.memo(DeleteAccountButton);
