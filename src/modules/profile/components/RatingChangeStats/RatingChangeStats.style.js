import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from '../../../../core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      borderRadius: !isCompactMode ? 12 : 0,
      paddingHorizontal: !isCompactMode ? 12 : 16,
      paddingVertical: !isCompactMode ? 10 : 12,
      backgroundColor: isCompactMode
        ? 'transparent'
        : dark.colors.gradientBackground,
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: withOpacity(dark.colors.textLight, 0.4),
      marginHorizontal: 16,
      marginBottom: 20,
    },
    chartContainer: {
      // marginTop: 10,
      alignItems: 'center',
      width: '100%',
    },
    chartHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      // marginTop: 40,
    },
    chartTitle: {
      color: Dark.colors.textDark,
      fontSize: isCompactMode ? 10 : 14,
      fontFamily: 'Montserrat-600',
      marginBottom: 10,
    },
    chart: {
      borderRadius: 10,
      fontFamily: 'Montserrat-600',
      letterSpacing: 1,
    },
    lineChartLabel: {
      marginBottom: 30,
      color: Dark.colors.textDark,
    },
  });

const useRatingChangeStatsStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useRatingChangeStatsStyles;
