import React, { useCallback, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Text } from '@rneui/themed';
import { View } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { useRouter } from 'expo-router';
import _size from 'lodash/size';
import dark from 'core/constants/themes/dark';
import useRatingChangeStatsStyles from './RatingChangeStats.style';
import useMediaQuery from 'core/hooks/useMediaQuery';

const chartConfig = {
  backgroundGradientFrom: dark.colors.background,
  backgroundGradientTo: dark.colors.background,
  fillShadowGradientTo: 'transparent',
  fillShadowGradientFrom: 'transparent',
  decimalPlaces: 2,
  color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  style: {
    borderRadius: 16,
  },
  propsForDots: {
    r: '5',
    stroke: dark.colors.secondary,
    fill: dark.colors.secondary,
  },
  propsForBackgroundLines: {
    strokeDasharray: '',
  },
};

const webChartConfig = {
  backgroundGradientFrom: dark.colors.gradientBackground,
  backgroundGradientTo: dark.colors.gradientBackground,
  fillShadowGradientTo: dark.colors.secondary,
  fillShadowGradientFrom: dark.colors.secondary,
  decimalPlaces: 2,
  color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  style: {
    borderRadius: 16,
  },
  propsForDots: {
    r: '2',
    stroke: dark.colors.secondary,
    strokeWidth: '1',
  },
  propsForBackgroundLines: {
    strokeDasharray: '',
  },
};

const RatingChangeStats = (props) => {
  const { statsData, user } = props;
  const styles = useRatingChangeStatsStyles();

  const { isMobile: isCompactMode } = useMediaQuery();

  const [containerWidth, setContainerWidth] = useState(0);
  const router = useRouter();

  const chartData = useMemo(
    () => ({
      datasets: [
        {
          data: statsData,
          strokeWidth: 2,
        },
      ],
    }),
    [statsData],
  );

  const navigateToStats = useCallback(() => {
    router.push('/home/<USER>');
  }, [router]);

  const onContainerLayout = useCallback((event) => {
    const width = event?.nativeEvent?.layout?.width || 0;
    setContainerWidth(width);
  }, []);

  const chartWidth = Math.max(containerWidth, 0);

  return (
    <View style={styles.container}>
      <View style={styles.chartHeader}>
        <Text style={styles.chartTitle}>PERFORMANCE</Text>
      </View>
      <View style={styles.chartContainer} onLayout={onContainerLayout}>
        {_size(statsData) < 2 ? (
          <Text style={styles.lineChartLabel}>
            Atleast 2 game should be played to show performance graph
          </Text>
        ) : (
          <View>
            <LineChart
              data={chartData}
              width={chartWidth}
              height={220}
              withDots={true}
              chartConfig={!isCompactMode ? webChartConfig : chartConfig}
              style={styles.chart}
              withHorizontalLines={true}
              withVerticalLines={false}
            />
          </View>
        )}
      </View>
    </View>
  );
};

RatingChangeStats.propTypes = {
  statsData: PropTypes.array,
  user: PropTypes.object,
};

export default React.memo(RatingChangeStats);
