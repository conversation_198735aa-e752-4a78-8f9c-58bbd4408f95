import React, { useCallback } from 'react'
import { TouchableOpacity, StyleSheet, View } from 'react-native'
import { Text, Icon, Overlay } from '@rneui/themed'
import { useRouter } from 'expo-router'
import Dark from '@/src/core/constants/themes/dark'
import { useSession } from '../../auth/containers/AuthProvider'
import userReader from 'core/readers/userReader'

const EditProfilePopover = ({ isVisible, onBackdropPress, onDeletePressed }) => {
    const router = useRouter()
    const { user } = useSession()
    const isGuest = userReader.isGuest(user);
    const navigateToEditProfile = useCallback(() => {
        onBackdropPress()
        router.push(`/profile/${user?.username}/edit-profile`)
    }, [onBackdropPress, router, user?.username])


    return (
        <Overlay
            isVisible={isVisible}
            onBackdropPress={onBackdropPress}
            overlayStyle={styles.popover}>
            {!isGuest && (<TouchableOpacity onPress={navigateToEditProfile}>
                <View style={styles.popoverContent}>
                    <Text style={styles.popoverText}>Edit Profile</Text>
                </View>
            </TouchableOpacity>)}
            <TouchableOpacity onPress={onDeletePressed}>
                <View style={styles.popoverContent}>
                    <Text style={styles.deleteText}>Delete Account</Text>
                </View>
            </TouchableOpacity>
        </Overlay>
    )
}

const styles = StyleSheet.create({
    popover: {
        width: 220,
        position: 'absolute',
        top: 52,
        gap: 10,
        right: -2,
        backgroundColor: Dark.colors.primary,
        borderRadius: 6,
        paddingHorizontal: 16,
        marginRight: 20,
    },
    popoverContent: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    popoverText: {
        color: 'white',
        fontSize: 14,
    },
    deleteText: {
        color: '#FF6A6A',
        fontSize: 14,
    },
})

export default React.memo(EditProfilePopover)
