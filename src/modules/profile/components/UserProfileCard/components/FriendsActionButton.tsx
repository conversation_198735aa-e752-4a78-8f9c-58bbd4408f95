import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import { FRIENDS_BUTTON_CONFIG } from 'modules/profile/constants/friends';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from '../../../../../core/constants/themes/dark';
import styles from '../Compact/CompactUserProfileCard.style';

type FriendActionButtonProps = {
  friendshipStatus: string;
  onPress: () => void;
  buttonStyle?: StyleProp<ViewStyle>;
  buttonContainerStyle?: StyleProp<ViewStyle>;
  FRIENDS_BUTTON_CONFIG: {
    [key: string]: {
      label: string;
      iconName: string;
      iconType: string;
    };
  };
};

const FriendActionButton: React.FC<FriendActionButtonProps> = ({
  friendshipStatus,
  onPress,
}) => {
  const config = FRIENDS_BUTTON_CONFIG[friendshipStatus];

  const { isMobile: isCompactMode } = useMediaQuery();

  const label = config?.label || 'ADD FRIEND';
  const iconName = config?.iconName || 'adduser';
  const iconType = config?.iconType || ICON_TYPES.ANT_DESIGN;

  return (
    <InteractiveSecondaryButton
      label={label}
      labelStyle={styles.friendsStatusText}
      onPress={onPress}
      iconConfig={{
        name: iconName,
        type: iconType,
        size: 15,
        color: dark.colors.secondary,
      }}
      buttonStyle={{ paddingHorizontal: 16 }}
      buttonContainerStyle={[
        { height: 38, flex: 1, maxWidth: 180 },
        !isCompactMode && { minWidth: 180 },
      ]}
    />
  );
};

export default FriendActionButton;
