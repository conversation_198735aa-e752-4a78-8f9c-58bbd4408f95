import UserImage from 'atoms/UserImage';
import PropTypes from 'prop-types';
import React, { useCallback } from 'react';
import { Platform, View } from 'react-native';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import dark from 'core/constants/themes/dark';
import { useRouter } from 'expo-router';
import useGetMessageGroupForFriends from '@/src/modules/friendsAndFollowers/hooks/queries/useGetMessageGroupForFriends';
import { FRIENDSHIP_STATUS } from 'modules/friendsAndFollowers/constants/friendshipStatus';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import {
  closePopover,
  showPopover,
} from '@/src/components/molecules/Popover/Popover';
import { openShareableCardFlow } from '@/src/components/shared/ShareResultModal';
import VerticalGradientCard from '@/src/components/molecules/VerticalGradientCard';
import _get from 'lodash/get';
import FriendActionButton from 'modules/profile/components/UserProfileCard/components/FriendsActionButton';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics/Analytics';
import userReader from 'core/readers/userReader';
import { BADGES_DETAILS } from '../../../constants/badges';
import UserInfoCard from '../../UserInfoCard';
import ProfileShareCard from '../../ProfileShareCard';
import SocialLinks from '../../SocialLinks';
import styles from './CompactUserProfileCard.style';
import usePublicProfileCardController from '../../../hooks/usePublicProfileCardController';

const CompactUserProfileCard = (props) => {
  const { user, badge, isCurrentUser, userAdditionalInfo } = props;
  const { isFollowing, friendshipStatus: friendshipStatusInitialValue } =
    userAdditionalInfo ?? EMPTY_OBJECT;

  const isWeb = Platform.OS === 'web';
  const {
    isFollowing: isUserFollowedByMe,
    friendshipStatus,
    isCurrentUserIsGuest,
    isGuest,
    handleOnAddOrRemoveFriendPressed,
    navigateToFriendsPage,
    handleOnChallengePressed,
  } = usePublicProfileCardController({
    user,
    isFollowing,
    friendshipStatus: friendshipStatusInitialValue,
  });
  const router = useRouter();
  const { getMessageGroupIDForFriends } = useGetMessageGroupForFriends();

  const gradientColor = _get(
    BADGES_DETAILS[badge],
    'color',
    dark.colors.textLight,
  );
  const routeToChat = useCallback(async () => {
    try {
      const { data } = await getMessageGroupIDForFriends({
        friendID: user?._id,
      });
      router.push(`/chat?messageGroupId=${data.getMessageGroupIdForFriends}`);
    } catch (e) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong. Please try again later`,
      });
    }
  }, [getMessageGroupIDForFriends, user._id, router]);

  const renderProfileShareCard = useCallback(
    () => (
      <View>
        <ProfileShareCard user={user} />
      </View>
    ),
    [user],
  );

  const userProfileUrl = `https://www.matiks.com/profile/${userReader.username(user)}`;

  const handleShareInNative = useCallback(
    ({ message = '' } = EMPTY_OBJECT) => {
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_SHARE_PROFILE_CARD,
      );
      if (isWeb) {
        showPopover({
          content: <ProfileShareCard user={user} />,
          overlayLook: true,
        });
        return;
      }
      closePopover();
      openShareableCardFlow({
        renderResultCard: renderProfileShareCard,
        message: userProfileUrl,
        storyBackgroundColors: {
          backgroundBottomColor:
            dark.colors.game.share.storyBackgroundColorBottom,
          backgroundTopColor: dark.colors.game.share.storyBackgroundColorTop,
        },
      });
    },
    [renderProfileShareCard, userProfileUrl],
  );

  return (
    <View style={styles.background}>
      <VerticalGradientCard gradientColor={gradientColor}>
        <View style={styles.imageContainer}>
          <UserImage
            user={user}
            size={48}
            rounded={false}
            style={styles.UserImage}
          />
        </View>
        <UserInfoCard
          user={user}
          navigateToFriendsPage={navigateToFriendsPage}
        />
        <SocialLinks links={user?.links} />
      </VerticalGradientCard>

      {!isGuest && !isCurrentUserIsGuest && (
        <View style={styles.followAndFriendsButtonRow}>
          {!isCurrentUser && (
            <FriendActionButton
              friendshipStatus={friendshipStatus}
              onPress={handleOnAddOrRemoveFriendPressed}
            />
          )}
          {!isCurrentUser && (
            <InteractiveSecondaryButton
              label="CHALLENGE"
              labelStyle={styles.friendsStatusText}
              onPress={handleOnChallengePressed}
              buttonStyle={{ paddingHorizontal: 16 }}
              buttonContainerStyle={{ height: 38, maxWidth: 120 }}
            />
          )}
          {isCurrentUser && (
            <InteractiveSecondaryButton
              label="ADD MORE FRIENDS"
              labelStyle={styles.friendsStatusText}
              iconConfig={{
                name: 'adduser',
                type: ICON_TYPES.ANT_DESIGN,
                size: 20,
                color: dark.colors.secondary,
              }}
              onPress={navigateToFriendsPage}
              buttonStyle={{ paddingHorizontal: 16 }}
              buttonContainerStyle={{ height: 38, flex: 1 }}
            />
          )}
          {friendshipStatus === FRIENDSHIP_STATUS.ACCEPTED ? (
            <InteractiveSecondaryButton
              iconConfig={{
                name: 'mail',
                type: ICON_TYPES.ENTYPO,
                size: 20,
                color: dark.colors.textLight,
              }}
              labelStyle={styles.friendsStatusText}
              onPress={routeToChat}
              buttonStyle={{ paddingHorizontal: 10 }}
              buttonContainerStyle={{ height: 38, maxWidth: 54 }}
            />
          ) : null}
          <InteractiveSecondaryButton
            iconConfig={{
              name: 'share',
              type: ICON_TYPES.FEATHER,
              size: 20,
              color: dark.colors.textLight,
            }}
            labelStyle={styles.friendsStatusText}
            onPress={handleShareInNative}
            buttonStyle={{ paddingHorizontal: 10, maxWidth: 60 }}
            buttonContainerStyle={{ height: 38 }}
          />
        </View>
      )}
    </View>
  );
};

CompactUserProfileCard.propTypes = {
  user: PropTypes.object,
  badge: PropTypes.object,
  shouldShowBadge: PropTypes.bool,
  onPressBadge: PropTypes.func,
  isCurrentUser: PropTypes.bool,
  userAdditionalInfo: PropTypes.object,
};

export default React.memo(CompactUserProfileCard);
