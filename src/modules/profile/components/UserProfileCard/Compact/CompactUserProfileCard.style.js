import { StyleSheet } from 'react-native';
import dark from '../../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  background: {
    width: '100%',
    justifyContent: 'center',
  },

  innerCard: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    height: 20,
  },
  card: {
    flex: 1,
    paddingHorizontal: 2,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    borderRadius: 12,
  },
  imageContainer: {
    position: 'absolute',
    left: 24,
    top: -45,
    zIndex: 20,
    borderRadius: 50,
    borderWidth: 6,
    borderColor: dark.colors.background,
  },
  badgeContainer: {
    alignItems: 'flex-end',
    width: 80,
  },
  badge: {
    width: 60,
    height: 60,
  },
  badgeTitle: {
    fontFamily: 'Montserrat-700',
    fontSize: 10,
  },
  userInfo: {
    alignItems: 'flex-start',
    paddingLeft: 16,
    marginTop: 40,
  },
  userName: {
    fontFamily: 'Montserrat-700',
    fontSize: 16,
    lineHeight: 20,
    color: 'white',
    marginBottom: 3,
  },
  UserImage: {
    height: 72,
    width: 72,
    borderRadius: 50,
    overflow: 'hidden',
  },
  username: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    opacity: 0.6,
    color: dark.colors.textLight,
  },
  bio: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    color: dark.colors.textLight,
    opacity: 0.6,
    marginTop: 12,
  },
  friendsSection: {
    flexDirection: 'row',
    gap: 12,
    paddingLeft: 16,
    marginTop: 16,
  },
  userRating: {
    fontSize: 14,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
  },
  followAndFriendsButtonRow: {
    justifyContent: 'flex-start',
    marginTop: 14,
    gap: 8,
    marginHorizontal: 12,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  friendsAndFollowersText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    lineHeight: 16,
    color: dark.colors.secondary,
    marginBottom: 3,
  },
  friendsStatusText: {
    color: dark.colors.secondary,
    fontFamily: 'Montserrat-700',
    fontSize: 12,
  },
});

export default styles;
