import { StyleSheet } from 'react-native';

import dark from '@/src/core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    width: '100%',
    height: 400,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
  },
  greetingText: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    lineHeight: 28,
    color: withOpacity(dark.colors.textLight, 0.6),
    marginTop: 10,
  },
  discordText: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    color: dark.colors.secondary,
  },
});

export default styles;
