import React, { useCallback } from 'react';
import { Image, Linking, Text, View } from 'react-native';
import styles from './Gretting.style';
import GreetingImage from '@/assets/images/greeting.png';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import dark from '@/src/core/constants/themes/dark';
import { DISCORD_COMMUNITY_URL } from '@/src/core/constants/appConstants';

const Greetings = () => {
  const handleNavigationToDiscord = useCallback(() => {
    Linking.openURL(DISCORD_COMMUNITY_URL);
  });

  return (
    <View style={styles.container}>
      <Image
        source={GreetingImage}
        style={{ width: 108, height: 154 }}
        resizeMode="contain"
      />
      <Text style={styles.greetingText}>THANK YOU FOR USING THE APP</Text>
      <InteractiveSecondaryButton
        label="DISCORD"
        iconConfig={{
          name: 'discord',
          type: ICON_TYPES.FONT_AWESOME_6,
          color: dark.colors.secondary,
        }}
        labelStyle={styles.discordText}
        borderColor={dark.colors.secondary}
        buttonContainerStyle={{ width: 140, marginTop: 30 }}
        onPress={handleNavigationToDiscord}
      />
    </View>
  );
};

export default React.memo(Greetings);
