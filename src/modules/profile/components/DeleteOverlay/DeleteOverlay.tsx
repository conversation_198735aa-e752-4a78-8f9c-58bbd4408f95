import CustomOverlay from '@/src/components/shared/CustomOverlay';
import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';

interface DeleteOverlayProps {
  onCancel: () => void;
  onConfirm: () => void;
  isVisible: boolean;
  toggleOverlay: () => void;
  overlayStyle?: StyleProp<ViewStyle>;
}

const DeleteOverlay: React.FC<DeleteOverlayProps> = (props) => {
  const { onCancel, onConfirm, isVisible, toggleOverlay, overlayStyle } = props;
  return (
    <CustomOverlay
      overlayStyle={overlayStyle}
      cancelText="Cancel"
      confirmText="Delete"
      onCancel={onCancel}
      onConfirm={onConfirm}
      isVisible={isVisible}
      toggleOverlay={toggleOverlay}
      title="Are you sure you want to delete your account?"
    />
  );
};

export default React.memo(DeleteOverlay);
