import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 164,
    backgroundColor: withOpacity(dark.colors.textLight, 0.04),
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingTitleText: {
    fontFamily: 'Montserrat-700',
    fontSize: 16,
    color: dark.colors.textLight,
    lineHeight: 30,
    letterSpacing: 0.5,
  },
  ratingButton: {
    width: 28,
    height: 28,
    borderWidth: 0.5,
    borderColor: dark.colors.tertiary,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoriesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  ratingContainer: {
    height: 92,
    flexBasis: '22%',
    flexDirection: 'column',
    borderWidth: 1,
    borderRadius: 12,
    borderColor: withOpacity(dark.colors.textLight, 0.4),
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingText: {
    fontFamily: 'BebasNeue-500',
    fontSize: 24,
    color: dark.colors.textLight,
    lineHeight: 28,
    letterSpacing: 1,
    marginTop: 6,
  },
  ratingName: {
    fontFamily: 'Montserrat-600',
    fontSize: 10,
    letterSpacing: 2,
    marginTop: 2,
    color: withOpacity(dark.colors.textLight, 0.6),
  },
});

export default styles;
