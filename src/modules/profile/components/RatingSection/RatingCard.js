import React, { useCallback } from 'react';
import { Image, Text, View, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import styles from './RatingCard.style';
import userReader from '@/src/core/readers/userReader';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { USER_RATINGS } from '../../constants/constants';
import _map from 'lodash/map';
import Icon from '@/src/components/atoms/Icon';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import dark from '@/src/core/constants/themes/dark';
import { showRightPane } from '@/src/components/molecules/RightPane/RightPane';
import RatingDetailsPage from '../../pages/RatingDetailsPage';

const RatingCard = ({ user }) => {
  const router = useRouter();
  const { isMobile } = useMediaQuery();

  const onPressRatingDetails = () => {
    if(!isMobile){
      showRightPane({
        content: <RatingDetailsPage username={userReader.username(user)}/>
    })
      return;
    }
    router.push(`/profile/${userReader.username(user)}/rating-details`);
  };


  const renderRatingCategoryCard = useCallback(
    ({ rating, ratingName, iconPath }) => {
      return (
        <View style={styles.ratingContainer}>
          <Image source={iconPath} style={{ width: 20, height: 20 }} />
          <Text style={styles.ratingText}>{rating ?? '1000'}</Text>
          <Text style={[styles.ratingName, isMobile && { fontSize: 7 }]}>
            {ratingName}
          </Text>
        </View>
      );
    },
    [isMobile],
  );

  return (
    <View style={[styles.container, !isMobile && { borderRadius: 8 }]}>
      <View style={styles.headerContainer}>
        <Text style={styles.ratingTitleText}>Ratings</Text>
        {/* <TouchableOpacity
          style={styles.ratingButton}
          onPress={onPressRatingDetails}
        >
          <Icon
            name="arrowright"
            type={ICON_TYPES.ANT_DESIGN}
            size={20}
            color={dark.colors.secondary}
        />
        </TouchableOpacity> */}
      </View>
      <View style={styles.categoriesContainer}>
        {_map(USER_RATINGS(user), (rating) =>
          renderRatingCategoryCard({
            rating: rating.rating,
            ratingName: rating.ratingName,
            iconPath: rating.icon,
          }),
        )}
      </View>
    </View>
  );
};

export default RatingCard;
