import { Overlay } from '@rneui/base';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import Entypo from '@expo/vector-icons/Entypo';
import {
  Animated,
  View,
  Text,
  Image,
  Easing,
  Platform,
  Modal,
} from 'react-native';
import dark from 'core/constants/themes/dark';
import <PERSON>tieView from 'lottie-react-native';
import { BADGES_DETAILS } from '../../constants/badges';
import PropTypes from 'prop-types';
import useBadgeEarnedOverlayStyles from './BadgeEarnedOverlay.style';
import BadgeConfetti from 'assets/images/badges/animation/badge_confetti.json';
import _isEmpty from 'lodash/isEmpty';
import usePlatformStats from 'core/graphql/queries/usePlatformStats';
import { useSession } from '../../../auth/containers/AuthProvider';
import _toUpper from 'lodash/toUpper';
import userReader from 'core/readers/userReader';
import useOverlayEventHandler from '@/src/overlays/hooks/useOverlayEventHandler';
import { events, listenersNamespace } from 'core/event/constants';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import useIsPlaying from '@/src/overlays/hooks/useIsPlaying';

const BadgeEarnedOverlay = () => {
  const { totalSignedInUsers, loading } = usePlatformStats();

  const { user, refreshCurrentUser } = useSession();

  const [showBadgeEarnedOverlay, setShowBadgeEarnedOverlay] = useState(false);

  const globalRank = userReader.globalRank(user);

  const percentile = Math.ceil(
    (globalRank * 100) / Math.max(totalSignedInUsers, 1),
  );
  const isPlayingGame = useIsPlaying();

  const handleBadgeEarnedEvent = useCallback(
    ({ payload }) => {
      setShowBadgeEarnedOverlay(true);
      Analytics.track(ANALYTICS_EVENTS.BADGE.BADGE_EARNED_MODAL_SHOWN, {
        ...payload,
      });
      // refresh user
      refreshCurrentUser();
    },
    [refreshCurrentUser],
  );

  const payload = useOverlayEventHandler(
    events.BadgeAssignedEvent,
    listenersNamespace.BadgeAssignedEvent,
    handleBadgeEarnedEvent,
  );

  const styles = useBadgeEarnedOverlayStyles();
  const translateY = useRef(new Animated.Value(500)).current;

  const { newBadge } = payload;

  const closeModal = useCallback(() => {
    setShowBadgeEarnedOverlay?.(false);
  }, [setShowBadgeEarnedOverlay]);

  const isWeb = Platform.OS === 'web';

  const animation = useRef(null);

  const badgeItem = BADGES_DETAILS[newBadge];

  useEffect(() => {
    if (showBadgeEarnedOverlay) {
      Animated.timing(translateY, {
        toValue: 0, // Center position
        duration: 250, // Duration of the animation in ms
        easing: Easing.out(Easing.ease),
        useNativeDriver: true, // Improve performance with native driver
      }).start();
    } else {
      Animated.timing(translateY, {
        toValue: 500, // Bottom off-screen
        duration: 250,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }).start();
    }
    return () => {
      translateY.stopAnimation();
    };
    
  }, [showBadgeEarnedOverlay]);

  if (_isEmpty(newBadge) || isPlayingGame) {
    return null;
  }

  const description =
    percentile <= 50
      ? `You are among top ${percentile}% Mathletes on Matiks`
      : badgeItem.badgeEarnDescription;

  return (
    <Overlay
      ModalComponent={Modal}
      overlayStyle={styles.overlayContainer}
      isVisible={showBadgeEarnedOverlay}
      onBackdropPress={closeModal}
    >
      <Animated.View
        style={[styles.mainContainer, { transform: [{ translateY }] }]}
      >
        <View
          style={{
            width: '100%',
            height: '100%',
            borderRadius: 10,
            position: 'absolute',
          }}
        >
          <LottieView
            autoPlay
            loop={true}
            ref={animation}
            resizeMode="cover"
            source={BadgeConfetti}
          />
        </View>
        <View style={{ padding: 16, width: '100%', height: '100%' }}>
          <View style={styles.headerRow}>
            <Entypo
              testID="close-modal-button"
              name="cross"
              size={22}
              color={dark.colors.textDark}
              onPress={closeModal}
            />
          </View>
          <View
            style={{ justifyContent: 'center', width: '100%', height: 260 }}
          >
            <View style={styles.imageContainer}>
              <Image
                source={badgeItem?.image}
                style={{ width: 240, height: 240 }}
              />
            </View>
            <View style={styles.textContainer}>
              <Text
                style={[
                  styles.titleText,
                  { color: badgeItem?.color || 'white' },
                ]}
              >
                YOU ARE NOW A {_toUpper(badgeItem?.title)} !
              </Text>
              <Text style={styles.descriptionText}>{description}</Text>
            </View>
          </View>
        </View>
      </Animated.View>
    </Overlay>
  );
};

BadgeEarnedOverlay.propTypes = {};

export default React.memo(BadgeEarnedOverlay);
