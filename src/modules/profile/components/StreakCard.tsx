import { Image, StyleSheet, Text, View } from 'react-native';
import React, { useCallback, useState } from 'react';
import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';
import StreakIcon from '@/assets/images/icons/fire_streak_icon.png';
import useStreakAnalytics from 'modules/profile/hooks/query/useStreakAnalytics';
import userReader from 'core/readers/userReader';
import { useSession } from '../../auth/containers/AuthProvider';
import dark from '../../../core/constants/themes/dark';
import Analytics from '../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../core/analytics/const';
import Pressable from '@/src/components/atoms/Pressable';

const styles = StyleSheet.create({
  streakCardStyle: {
    minWidth: 41,
    height: 38,
    justifyContent: 'center',
    backgroundColor: dark.colors.primary,
    paddingLeft: 10,
    paddingRight: 12,
    borderRadius: 20,
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
  },
  streakCardTextStyle: {
    color: 'white',
    textAlign: 'center',
    fontFamily: 'Montserrat-800',
    fontSize: 12,
  },
  innerStreakContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
});

const StreakCard = () => {
  const router = useRouter();
  const { isMobile: isCompactMode } = useMediaQuery();

  const [isVisible, setIsVisible] = useState(false);

  const { user } = useSession();

  const currentStreak = userReader.currentStreak(user);

  const { hasStreak: hasTodayStreak } = useStreakAnalytics();

  const handleOnPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_ON_STREAKS, {
      currentStreak: userReader.currentStreak(currentStreak),
      longestStreak: userReader.longestStreak(user),
    });
    if (!isCompactMode) {
      setIsVisible(true);
      router.push(`/profile/${user?.username}/streak`);
      return;
    }
    router.push(`/profile/${user?.username}/streak`);
  }, [router, isCompactMode, user]);

  return (
    <Pressable
      style={[
        styles.streakCardStyle,
        !isCompactMode && {
          backgroundColor: isVisible ? dark.colors.primary : 'transparent',
        },
        !hasTodayStreak && { opacity: 0.4 },
      ]}
      onPress={handleOnPress}
    >
      <View style={{ flexDirection: 'row', gap: 6, alignItems: 'center' }}>
        <Image source={StreakIcon} style={{ height: 22, width: 22 }} />
        <Text style={styles.streakCardTextStyle}>
          {`${currentStreak ?? 0}`}
        </Text>
      </View>
    </Pressable>
  );
};

export default React.memo(StreakCard);
