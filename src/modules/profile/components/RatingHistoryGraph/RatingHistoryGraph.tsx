import React, { useCallback, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Text } from '@rneui/themed';
import { LayoutChangeEvent, View } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import _size from 'lodash/size';
import dark from 'core/constants/themes/dark';
import useRatingHistoryStyles from './RatingHistoryGraph.style';
import useMediaQuery from 'core/hooks/useMediaQuery';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { GAME_CATEGORIES } from '@/src/modules/home/<USER>/gameTypes';
import { useRouter } from 'expo-router';
import _isArray from 'lodash/isArray';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';

const chartConfig = {
  backgroundGradientFrom: dark.colors.background,
  backgroundGradientTo: dark.colors.background,
  fillShadowGradientTo: dark.colors.secondary,
  fillShadowGradientFrom: dark.colors.secondary,
  decimalPlaces: 0,
  yAxisInterval: 1,
  color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  style: {
    borderRadius: 16,
    fontFamily: 'Montserrat-700',
  },
  propsForDots: {
    r: '3',
    stroke: dark.colors.secondary,
    fill: dark.colors.secondary,
  },
  propsForBackgroundLines: {
    strokeDasharray: '',
  },
};

const webChartConfig = {
  backgroundGradientFrom: dark.colors.gradientBackground,
  backgroundGradientTo: dark.colors.gradientBackground,
  fillShadowGradientTo: dark.colors.secondary,
  fillShadowGradientFrom: dark.colors.secondary,
  decimalPlaces: 0,
  yAxisInterval: 1,
  color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
  style: {
    borderRadius: 16,
    fontFamily: 'Montserrat-700',
  },
  propsForDots: {
    r: '2',
    stroke: dark.colors.secondary,
    strokeWidth: '0.5',
  },
  propsForBackgroundLines: {
    strokeDasharray: '',
  },
};

const RatingHistoryGraph: React.FC<Props> = (props) => {
  const { graphData, ratingKey, isCurrentUser } = props;
  const styles = useRatingHistoryStyles();
  const router = useRouter();

  const { isMobile: isCompactMode } = useMediaQuery();

  const [containerWidth, setContainerWidth] = useState(0);

  const handleOnPressPlayAGame = useCallback(() => {
    if (ratingKey === GAME_CATEGORIES.PUZZLE) {
      router.push('/puzzle-home');
      return;
    }
    router.push(`/games?gameMode=${ratingKey}`);
  }, [router, ratingKey]);

  const chartData = useMemo(() => {
    const ratings = _isArray(graphData) ? _map(graphData, 'rating') : [];
    const dataPoints = _isEmpty(ratings) ? [0] : ratings;
    const xLabels = _isEmpty(dataPoints) ? [''] : _map(dataPoints, () => "");

    return {
      labels: xLabels,
      datasets: [
        {
          data: dataPoints,
          strokeWidth: 2,
          color: (opacity = 1) => dark.colors.secondary,
        },
      ],
    };
  }, [graphData]);

  const onContainerLayout = useCallback((event: LayoutChangeEvent) => {
    const width = event?.nativeEvent?.layout?.width || 0;
    setContainerWidth(width);
  }, []);

  const chartWidth = Math.max(containerWidth, 0);

  return (
    <View style={styles.container}>
      <View style={styles.chartContainer} onLayout={onContainerLayout}>
        <View style={styles.legendContainer}>
          <View style={styles.dot}></View>
          <Text style={styles.ratingTypeText}>{ratingKey}</Text>
        </View>
        {!graphData || _size(graphData) < 2 ? (
          <View style={styles.noGamesContainer}>
            <Text style={styles.lineChartLabel}>
              Atleast 2 data points are needed to show the rating graph.
            </Text>
            {isCurrentUser && (
              <InteractiveSecondaryButton
                label="PLAY A GAME"
                onPress={handleOnPressPlayAGame}
                labelStyle={styles.playAGameText}
                buttonContainerStyle={{ height: 38, width: 140 }}
              />
            )}
          </View>
        ) : (
          <View>
            <LineChart
              data={chartData}
              width={chartWidth}
              height={220}
              withDots={false}
              chartConfig={!isCompactMode ? webChartConfig : chartConfig}
              style={styles.chart}
              segments={5}
              withHorizontalLines={true}
              withVerticalLines={false}
              withVerticalLabels={false}
              bezier={true}
            />
          </View>
        )}
      </View>
    </View>
  );
};

RatingHistoryGraph.propTypes = {
  graphData: PropTypes.arrayOf(
    PropTypes.shape({
      date: PropTypes.string,
      rating: PropTypes.number.isRequired,
      __typename: PropTypes.string,
    }).isRequired,
  ),
  ratingKey: PropTypes.string,
};

export default React.memo(RatingHistoryGraph);
