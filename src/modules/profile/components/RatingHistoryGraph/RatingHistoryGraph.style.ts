import Dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from '../../../../core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      borderRadius: !isCompactMode ? 12 : 12,
      paddingHorizontal: !isCompactMode ? 12 : 0,
      paddingVertical: !isCompactMode ? 10 : 12,
      backgroundColor: isCompactMode
        ? 'transparent'
        : dark.colors.gradientBackground,
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: withOpacity(dark.colors.textLight, 0.4),
      marginHorizontal: 16,
      marginBottom: 20,
      marginTop: 30,
      paddingRight: 16
    },
    chartContainer: {
      alignItems: 'center',
      width: '100%',
      justifyContent: 'center',

    },
    chartHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      // marginTop: 40,
    },
    chartTitle: {
      color: Dark.colors.textDark,
      fontSize: isCompactMode ? 10 : 14,
      fontFamily: 'Montserrat-600',
      marginBottom: 10,
    },
    chart: {
      borderRadius: 10,
      fontFamily: 'Montserrat-600',
      letterSpacing: 1,
    },
    lineChartLabel: {
      marginBottom: 30,
      color: Dark.colors.textDark,
      marginHorizontal: 16
    },
    legendContainer:{
        flexDirection: 'row',
        gap: 8,
        alignSelf: 'flex-start',
        paddingHorizontal: 16,
        marginBottom: 12,
        justifyContent: 'center',
        alignItems: 'center'
    },
    dot:{
        width: 10,
        height: 10,
        backgroundColor: dark.colors.secondary,
        borderRadius: 20,
    },
    ratingTypeText:{
        fontFamily: 'Montserrat-700',
        letterSpacing: 1,
        fontSize: 14, 
        color: dark.colors.textLight,
    },
    noGamesContainer:{
      justifyContent: 'center',
      alignItems:'center',
      marginBottom: 12
    },
    playAGameText:{
      fontFamily:'Montserrat-700',
      fontSize: 12,
      color: dark.colors.secondary
    }
  });

const useRatingHistoryStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useRatingHistoryStyles;
