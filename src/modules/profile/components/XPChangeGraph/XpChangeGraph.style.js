import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from '../../../../core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    mainContainer: {
      flexDirection: 'column',
      gap: 16,
      marginHorizontal: 16,
    },
    container: {
      paddingHorizontal: !isCompactMode ? 12 : 0,
      paddingVertical: !isCompactMode ? 10 : 12,
      backgroundColor: isCompactMode
        ? 'transparent'
        : dark.colors.gradientBackground,
      justifyContent: 'flex-start',
      borderWidth: 1,
      borderColor: withOpacity(dark.colors.textLight, 0.4),
      borderRadius: 12,
      alignItems: 'flex-start',
      paddingRight: 30,
      marginBottom: 16,
      height: !isCompactMode ? 300 : 220,
    },
    webContainer: {
      height: !isCompactMode ? 360 : 260,
    },
    label: {
      fontFamily: 'Montserrat-700',
      fontSize: 16,
      letterSpacing: 0.5,
      lineHeight: 30,
      color: dark.colors.textLight,
    },
    chartContainer: {
      alignItems: 'flex-start',
      width: '100%',
      height: 280,
    },
    webChartContainer: {
      height: 200,
    },
    graphAreaContainer: {
      flexDirection: 'row',
      width: '100%',
      alignItems: 'flex-start',
    },
    chartWrapper: {
      flex: 1,
      marginRight: 10,
    },
    chartHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: !isCompactMode ? 12 : 16,
    },
    chartTitle: {
      color: Dark.colors.textDark,
      fontSize: isCompactMode ? 10 : 14,
      fontFamily: 'Montserrat-600',
      marginBottom: 10,
    },
    chart: {
      borderRadius: 10,
      fontFamily: 'Montserrat-600',
      letterSpacing: 1,
    },
    lineChartLabel: {
      marginBottom: 30,
      color: Dark.colors.textDark,
    },
    customLegendContainer: {
      paddingHorizontal: 16,
      marginBottom: 16,
    },
    legendItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 6,
    },
    legendColorBox: {
      width: 10,
      height: 10,
      borderRadius: 20,
      marginRight: 6,
    },
    legendText: {
      color: Dark.colors.textLight,
      fontSize: 10,
      fontFamily: 'Montserrat-700',
      letterSpacing: 1,
    },
    loadingIndicator: {
      marginTop: 20,
      marginBottom: 20,
      alignSelf: 'center',
      height: 180,
      justifyContent: 'center',
    },
    messageText: {
      color: Dark.colors.textDark,
      textAlign: 'center',
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      paddingHorizontal: 10,
      alignSelf: 'center',
    },
  });

const useXpChangeGraphStyles = () => {
  const { isMobile } = useMediaQuery();
  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};

export default useXpChangeGraphStyles;
