import React, { useCallback } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import styles from './RatingDetailsCard.style';
import { Icon } from '@rneui/themed';
import dark from '@/src/core/constants/themes/dark';
import _get from 'lodash/get';
import { USER_RATINGS } from 'modules/profile/constants/constants';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import DeformedRectangle from '@/src/components/svg/BackgroudViews/DeformedRectangle';

interface RatingDetailsCardProps {
  index: number;
  ratingData: {
    ratingName: string;
    icon?: any;
    rating?: number;
    currentRating?: string | number;
    ratingChange?: string | number;
    type?: string;
  };
  onPress: (ratingName: string) => void;
  isSelected: boolean;
}

const RatingDetailsCard: React.FC<RatingDetailsCardProps> = 
  ({ index, ratingData, onPress, isSelected }) => {
    const { user } = useSession();
    const currentRating = _get(ratingData, 'currentRating', '-');
    const ratingChange = _get(ratingData, 'ratingChange', '-');
    const type = _get(ratingData, 'type', '-');
    const isUp = Number(ratingChange) > 0;
    const ratingIcons = USER_RATINGS(user);
    const { isMobile: isCompactMode } = useMediaQuery();

    const handleOnPress = useCallback(() => {
      onPress(ratingData.type);
    },[onPress, ratingData.type]);

    return (
      <TouchableOpacity
        style={[
          styles.ratingCard,
          isSelected && styles.selectedRatingCard,
          { aspectRatio: isCompactMode ? 1.6 : 2.0 },
        ]}
        onPress={handleOnPress}
        activeOpacity={0.8}
      >
        <View>
          {isSelected && <View style={styles.backgroundRect1}>
            <DeformedRectangle />
          </View>}
          {isSelected && <View style={styles.backgroundRect2}>
            <DeformedRectangle />
          </View>}
          <Text style={styles.ratingValue}>{currentRating}</Text>
          <Text style={styles.ratingName}>{type}</Text>
          <View style={styles.ratingChangeContainer}>
            <View style={styles.bottomRow}>
              <Text
                style={[
                  styles.ratingChangeText,
                  isUp ? styles.ratingChangeUp : styles.ratingChangeDown,
                ]}
              >
                {ratingChange}
              </Text>
              <Icon
                name={isUp ? 'arrow-up' : 'arrow-down'}
                type="material-community"
                size={16}
                color={isUp ? dark.colors.secondary : dark.colors.red}
              />
            </View>
          </View>
        </View>
        <Image
          source={
            isSelected
              ? ratingIcons[index].icon
              : ratingIcons[index].disabledIcon
          }
          style={[styles.ratingIcon]}
          resizeMode="contain"
        />
      </TouchableOpacity>
    );
  };


export default React.memo(RatingDetailsCard);
