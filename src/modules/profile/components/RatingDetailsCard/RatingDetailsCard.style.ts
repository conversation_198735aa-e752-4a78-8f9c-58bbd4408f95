import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const styles = StyleSheet.create({
  ratingCard: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 0.5,
    borderColor: withOpacity(dark.colors.textLight, 0.4),
    padding: 16,
    aspectRatio: 1.6,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'flex-start',
    overflow: 'hidden'
  },
  selectedRatingCard: {
    borderColor: dark.colors.secondary,
    borderWidth: 0.5,
    backgroundColor: withOpacity(dark.colors.textLight, 0.1),
  },
  ratingValue: {
    fontFamily: 'BebasNeue-500',
    fontSize: 28,
    color: dark.colors.textLight,
    letterSpacing: 1,
    lineHeight: 28,
  },
  ratingName: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    color: withOpacity(dark.colors.textLight, 0.6),
    textTransform: 'uppercase',
    marginTop: 2,
    lineHeight: 12,
    letterSpacing: 1,
  },
  bottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  ratingChangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingChangeText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    marginRight: 4,
    letterSpacing: 1,
    lineHeight: 12,
  },
  ratingChangeUp: {
    color: dark.colors.secondary,
  },
  ratingChangeDown: {
    color: dark.colors.red, // Red for down
  },
  ratingIcon: {
    width: 28,
    height: 28,
    opacity: 0.6,
    alignSelf: 'center',
  },
  backgroundRect1:{
    position: 'absolute',
    top: -20,
    left: -80
  },
  backgroundRect2:{
    position: 'absolute',
    top: -30,
    left: 0
  }
});

export default styles;
