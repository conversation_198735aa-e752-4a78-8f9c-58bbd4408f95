import { StyleSheet } from 'react-native';

import Dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  background: {
    marginTop: 2,
    width: '100%',
    height: 70,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Dark.colors.tertiary,
  },
  profileDescription: {
    flexDirection: 'row',
    gap: 4,
  },
  card: {
    flex: 1,
    paddingHorizontal: 2,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 8,
    borderRadius: 12,
  },
  imageContainer: {
    marginHorizontal: 1,
    zIndex: 5,
  },
  WinOrLoss: {
    width: 32,
    height: 24,
    marginHorizontal: 2,
    zIndex: 5,
    borderRadius: 4,
    backgroundColor: Dark.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  userInfo: {
    alignItems: 'flex-start',
    paddingLeft: 8,
  },
  userName: {
    fontSize: 14,
    color: 'white',
    marginBottom: 4,
  },
  thenRating: {
    fontSize: 10,
    fontFamily: 'Montserrat-600',
    color: Dark.colors.textLight,
    letterSpacing: 1,
  },
  gameTime: {
    fontSize: 10,
    fontFamily: 'Montserrat-400',
    color: Dark.colors.textDark,
  },
  ratingChange: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
  },
  userImage: {
    height: 32,
    width: 32,
    borderRadius: 4,
    overflow: 'hidden',
  },
  endContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  WLText: {
    fontFamily: 'Montserrat-700',
    color: Dark.colors.textLight,
    fontSize: 12,
  },
});

export default styles;
