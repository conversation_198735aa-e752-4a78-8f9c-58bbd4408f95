import { Image, Text, TouchableOpacity, View } from 'react-native';
import numeral from 'numeral';
import userReader from 'core/readers/userReader';
import React, { useCallback } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import {
  WEEKLY_LEAGUE_INFO,
  WEEKLY_LEAGUE_TYPES_MAP,
} from 'modules/matiksWeeklyLeague/constants/weeklyLeagueTypes';
import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useGetWeeklyCoinsEarned from 'modules/profile/hooks/query/useGetWeeklyCoinsEarned';
import styles from './UserStatikCoinsCard.style';
import useHaptics from '@/src/core/hooks/useHaptics';

const NUMERAL_FORMAT = '0[.]00a';

const UserStatikCoinsCard = () => {
  const router = useRouter();
  const { isMobile: isCompactMode } = useMediaQuery();
  const { triggerHaptic } = useHaptics();

  const { user } = useSession();

  const userCurrentLeague = userReader.userCurrentLeague(user);

  const { coinsEarned } = useGetWeeklyCoinsEarned();

  const handleOnPress = useCallback(() => {
    triggerHaptic();
    Analytics.track(ANALYTICS_EVENTS.STATIK_COINS.CLICKED_ON_STATIK_COINS, {});
    router.push(`/weekly-league`);
  }, [router, triggerHaptic]);

  const getXpIcon = () => {
    switch (userCurrentLeague) {
      case WEEKLY_LEAGUE_TYPES_MAP.BRONZE:
        return WEEKLY_LEAGUE_INFO[WEEKLY_LEAGUE_TYPES_MAP.BRONZE].image;
      case WEEKLY_LEAGUE_TYPES_MAP.SILVER:
        return WEEKLY_LEAGUE_INFO[WEEKLY_LEAGUE_TYPES_MAP.SILVER].image;
      case WEEKLY_LEAGUE_TYPES_MAP.GOLD:
        return WEEKLY_LEAGUE_INFO[WEEKLY_LEAGUE_TYPES_MAP.GOLD].image;
      case WEEKLY_LEAGUE_TYPES_MAP.DIAMOND:
        return WEEKLY_LEAGUE_INFO[WEEKLY_LEAGUE_TYPES_MAP.DIAMOND].image;
      case WEEKLY_LEAGUE_TYPES_MAP.RUBY:
        return WEEKLY_LEAGUE_INFO[WEEKLY_LEAGUE_TYPES_MAP.RUBY].image;
      default:
        return WEEKLY_LEAGUE_INFO[WEEKLY_LEAGUE_TYPES_MAP.BRONZE].image;
    }
  };

  return (
    <TouchableOpacity style={styles.streakCardStyle} onPress={handleOnPress}>
      <View style={styles.innerStreakContainer}>
        <Image source={getXpIcon()} style={{ height: 22, width: 22 }} />
        <Text
          style={[
            styles.streakCardTextStyle,
            isCompactMode && { fontSize: 12 },
          ]}
        >
          {`${numeral(coinsEarned ?? 0).format(NUMERAL_FORMAT)} XP`}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const UserStatiCoinsCardContainer = (props) => {
  const { user } = useSession();
  const isGuestUser = userReader.isGuest(user);

  if (isGuestUser) {
    return null;
  }

  return <UserStatikCoinsCard {...props} />;
};

export default React.memo(UserStatiCoinsCardContainer);
