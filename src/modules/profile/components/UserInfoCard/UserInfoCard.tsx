import React from 'react';
import { Text, View } from 'react-native';
import PropTypes from 'prop-types';
import userReader from '../../../../core/readers/userReader';
import styles from './UserInfoCard.style';

interface UserInfoCardProps {
  user: any;
  navigateToFriendsPage?: () => void;
  navigateToFollowersPage?: () => void;
  navigateToFollowingsPage?: () => void;
}

const UserInfoCard = ({ user, navigateToFriendsPage }: UserInfoCardProps) => {
  const bio = userReader.bio(user);

  const renderStatText = (
    count: number,
    label: string,
    onPress?: () => void,
  ) => (
    <Text style={styles.friendsAndFollowersText} onPress={onPress}>
      <Text style={styles.friendsAndFollowersCountText}>{count ?? 0}</Text>
      <Text style={styles.label}>{` ${label}`}</Text>
    </Text>
  );

  return (
    <>
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{userReader.displayName(user) || 'Anonymous'}</Text>
        <Text testID='user-username' style={styles.username}>{`@${userReader.username(user)}`}</Text>
        {bio && <Text style={styles.bio}>{bio}</Text>}
      </View>

      <View style={styles.friendsSection}>
        {renderStatText(
          userReader.friendsCount(user),
          'Friends',
          navigateToFriendsPage,
        )}
      </View>
    </>
  );
};

UserInfoCard.propTypes = {
  user: PropTypes.object.isRequired,
  navigateToFriendsPage: PropTypes.func,
  navigateToFollowersPage: PropTypes.func,
  navigateToFollowingsPage: PropTypes.func,
};

export default React.memo(UserInfoCard);
