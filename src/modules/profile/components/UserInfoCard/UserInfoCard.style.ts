import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
  userInfo: {
    alignItems: 'flex-start',
    paddingLeft: 16,
    marginTop: 50,
    width: '100%',
  },
  userName: {
    fontFamily: 'Montserrat-700',
    fontSize: 16,
    lineHeight: 20,
    color: 'white',
    marginBottom: 3,
  },
  username: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    color: dark.colors.textDark,
    opacity: 0.6,
  },
  bio: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    color: dark.colors.textLight,
    opacity: 0.6,
    marginTop: 12,
    paddingRight: 16,
  },
  friendsSection: {
    flexDirection: 'row',
    gap: 12,
    paddingLeft: 16,
    marginTop: 16,
    width: '100%',
  },
  friendsAndFollowersText: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    lineHeight: 16,
    color: dark.colors.secondary,
    marginBottom: 3,
    gap:10
  },
  friendsAndFollowersCountText: {
    fontFamily: 'Montserrat-800',
    fontSize: 12,
    lineHeight: 16,
    color: dark.colors.secondary,
  },
  label:{
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    color: dark.colors.secondary,
  }
});

export default styles;
 