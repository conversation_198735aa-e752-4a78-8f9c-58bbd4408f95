import React, { useCallback, useState } from 'react';
import { ImageBackground, Platform, View } from 'react-native';
import userReader from 'core/readers/userReader';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import _get from 'lodash/get';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import bgImage from '@/assets/images/ratingBackgrounds/novice.png';
import WebBackButton from 'shared/WebBackButton';
import EditProfilePage from 'modules/profile/pages/EditProfilePage';
import { showRightPane } from 'molecules/RightPane/RightPane';
import { useSession } from 'modules/auth/containers/AuthProvider';
import styles from './BadgeBackground.style';
import Header from '../../../../components/shared/Header/Header';
import EditProfilePopover from '../EditProfilePopover';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';
import InteractiveSecondaryButton from '../../../../components/atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from '../../../../components/atoms/Icon';
import dark from '../../../../core/constants/themes/dark';
import { BADGES, BADGES_DETAILS } from '../../constants/badges';
import DeleteOverlay from '../DeleteOverlay/DeleteOverlay';

interface BadgeBackgroundProps {
  user: any;
  isCurrentUser: boolean;
}

const BadgeBackground: React.FC<BadgeBackgroundProps> = ({
  user,
  isCurrentUser,
}) => {
  const [isPopoverVisible, setPopoverVisible] = useState(false);
  const [isOverlayVisible, setOverlayVisible] = useState(false);
  const { handleDeleteUser } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();
  const isWeb = Platform.OS === 'web';
  const isGuest = userReader.isGuest(user);

  const badge = userReader.badge(user);
  const badgeDetails = _get(BADGES_DETAILS, badge, BADGES_DETAILS.default);
  const backgroundImage = _get(badgeDetails, 'background', bgImage);
  const badgeTitle = _get(badgeDetails, 'title', 'NOVICE');
  const badgeColor = _get(
    BADGES_DETAILS[badge],
    'color',
    BADGES_DETAILS[BADGES.NOVICE].color,
  );

  const onPressDelete = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.PROFILE.CLICKED_ON_PROFILE_PAGE_DELETE_ACCOUNT,
    );
    setPopoverVisible(false);
    setOverlayVisible(true);
  }, [isOverlayVisible, setOverlayVisible]);

  const onPressEditProfile = useCallback(() => {
    showRightPane({
      content: <EditProfilePage />,
    });
  }, []);

  const renderDeleteOverlay = useCallback(() => {
    if (!isOverlayVisible) {
      return;
    }
    return (
      <DeleteOverlay
        onCancel={() => setOverlayVisible(false)}
        onConfirm={() => {
          handleDeleteUser();
          setOverlayVisible(false);
        }}
        isVisible={isOverlayVisible}
        toggleOverlay={() => setOverlayVisible(false)}
      />
    );
  }, [isOverlayVisible]);

  const togglePopover = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.PROFILE.CLICKED_ON_PROFILE_PAGE_KEBAB_MENU,
    );
    setPopoverVisible((prevPopoverVisible) => !prevPopoverVisible);
  }, []);

  const renderTrailingComponent = useCallback(() => {
    if (!isCurrentUser || isGuest) {
      return;
    }
    return (
      <InteractiveSecondaryButton
        onPress={togglePopover}
        iconConfig={{
          name: 'more-vertical',
          type: ICON_TYPES.FEATHER,
          color: dark.colors.textDark,
          size: 20,
        }}
        buttonContainerStyle={{ width: 36, height: 36 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
    );
  }, [togglePopover, isCurrentUser, isGuest]);

  const renderEditProfileButton = useCallback(() => {
    if (!isCurrentUser || isGuest) {
      return;
    }
    return (
      <InteractiveSecondaryButton
        testID="edit-profile"
        onPress={onPressEditProfile}
        iconConfig={{
          name: 'edit',
          type: ICON_TYPES.MATERIAL_ICONS,
          color: dark.colors.textDark,
          size: 16,
        }}
        buttonContainerStyle={{ width: 36, height: 36 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
    );
  }, [onPressEditProfile, isCurrentUser, isGuest]);

  return (
    <View style={[styles.container, !isCompactMode && { marginTop: 16 }]}>
      <ImageBackground
        source={backgroundImage}
        style={styles.backgroundImage}
        resizeMode={isWeb ? 'stretch' : 'cover'}
        imageStyle={[!isCompactMode && styles.backgroundImageStyle]}
      >
        <View style={styles.header}>
          <Header
            isTransparentBg
            renderTrailingComponent={renderTrailingComponent}
          />
          <View style={{ margin: 12 }}>
            <WebBackButton
              renderTrailingComponent={renderEditProfileButton}
              isTransparentBg
            />
          </View>
        </View>
        <TextWithShadow
          text={badgeTitle}
          containerStyle={styles.ratingTitleContainer}
          textStyle={{
            fontFamily: 'Montserrat-900',
            fontSize: 24,
            color: badgeColor,
            letterSpacing: 2,
          }}
          shadowWidth={0}
          shadowOffsetX={0}
          shadowOffsetY={0}
          strokeColor="#000000"
          strokeWidth={isWeb ? 5 : 7}
        />
        {renderDeleteOverlay()}
        {isCurrentUser && (
          <EditProfilePopover
            isVisible={isPopoverVisible}
            onBackdropPress={togglePopover}
            onDeletePressed={onPressDelete}
          />
        )}
      </ImageBackground>
    </View>
  );
};

export default React.memo(BadgeBackground);
