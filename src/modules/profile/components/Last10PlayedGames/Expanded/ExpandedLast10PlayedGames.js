import React from 'react';
import PropTypes from 'prop-types';
import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import { Icon, Text } from '@rneui/themed';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import dark from 'core/constants/themes/dark';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import RatingCategoryCard from 'modules/profile/components/RatingCategoryCard';
import { RATING_CATEGORIES } from '../../../constants/constants';
import RecentGameCard from '../../RecentGameCard';
import styles from '../Last10PlayedGames.style';

const ExpandedLast10PlayedGames = (props) => {
  const {
    gamesData,
    loading,
    selectedRatingType,
    onSelectRatingType,
    onSeeMorePressed,
    isCurrentUser,
    user,
  } = props;

  return (
    <View style={styles.expandedContainer}>
      <View style={styles.recentGamesHeader}>
        <Text style={[styles.recentGamesTitle]}>Last 5 Games</Text>
        {!_isEmpty(gamesData) && (
          <TouchableOpacity
            style={styles.ratingButton}
            onPress={onSeeMorePressed}
          >
            <Icon
              name="arrowright"
              type={ICON_TYPES.ANT_DESIGN}
              size={20}
              color={dark.colors.secondary}
            />
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.ratingCategories}>
        {_map(RATING_CATEGORIES, (category) => (
          <RatingCategoryCard
            name={category.key}
            categoryKey={category.key}
            selectedRatingType={selectedRatingType}
            onSelectRatingType={onSelectRatingType}
          />
        ))}
      </View>

      {loading && (
        <ActivityIndicator
          size="large"
          color={dark.colors.primary}
          style={{ marginVertical: 30 }}
        />
      )}

      {!loading && _isEmpty(gamesData) && (
        <View style={styles.noGamesContainer}>
          <Text style={styles.noGamesText}>
            Not Played any {selectedRatingType} Games Yet
          </Text>
        </View>
      )}

      {!loading && !_isEmpty(gamesData) && (
        <View style={styles.recentGamesContainer}>
          {_map(gamesData, (game) => (
            <RecentGameCard
              key={game._id}
              recentGame={game}
              isCurrentUser={isCurrentUser}
            />
          ))}
        </View>
      )}
    </View>
  );
};

ExpandedLast10PlayedGames.propTypes = {
  gamesData: PropTypes.array,
  loading: PropTypes.bool,
  selectedRatingType: PropTypes.string,
  onSelectRatingType: PropTypes.func,
  onSeeMorePressed: PropTypes.func,
  isCurrentUser: PropTypes.bool,
  user: PropTypes.object,
};

export default React.memo(ExpandedLast10PlayedGames);
