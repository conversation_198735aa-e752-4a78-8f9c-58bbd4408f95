import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import dark from '@/src/core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  userCard: {
    alignItems: 'center',
    marginTop: 20,
  },
  chartContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 40,
  },
  chartTitle: {
    color: Dark.colors.textDark,
    fontSize: 14,
    fontFamily: 'Montserrat-700',
    marginBottom: 10,
  },
  chart: {
    borderRadius: 16,
  },
  recentGamesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
  },
  recentGamesTitle: {
    color: Dark.colors.textLight,
    letterSpacing: 0.5,
    lineHeight: 30,
    fontFamily: 'Montserrat-700',
    fontSize: 16,
  },
  noGamesContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    height: 130,
    gap: 30,
  },
  noGamesText: {
    color: Dark.colors.textDark,
    letterSpacing: 0.5,
    lineHeight: 30,
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    textAlign: 'center',
  },
  playGameText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: Dark.colors.secondary,
  },
  moreText: {
    color: Dark.colors.secondary,
    fontSize: 16,
  },
  recentGamesContainer: {
    alignItems: 'center',
  },
  lineChartLabel: {
    color: 'white',
  },

  expandedContainer: {
    paddingHorizontal: 12,
    borderRadius: 12,
    backgroundColor: Dark.colors.gradientBackground,
  },
  ratingButton: {
    width: 28,
    height: 28,
    borderWidth: 0.5,
    borderColor: dark.colors.tertiary,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingCategories: {
    width: '100%',
    height: 60,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  ratingCategoryCard: {
    borderRadius: 20,
    borderWidth: 1,
    height: 32,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderColor: withOpacity(dark.colors.textLight, 0.1),
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingCategoryText: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    color: withOpacity(dark.colors.textLight, 0.4),
    letterSpacing: 0.5,
  },
  selectedRatingCategoryCard: {
    borderWidth: 1,
    borderColor: dark.colors.secondary,
    backgroundColor: withOpacity(dark.colors.textLight, 0.1),
  },
  selectedRatingCategoryText: {
    color: dark.colors.secondary,
  },
});

export default styles;
