import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';
import CompactLast10PlayedGames from './Compact';
import ExpandedLast10PlayedGames from './Expanded';
import useUserLastPlayedGames from '../../hooks/query/useUserLastPlayedGames';
import { RATING_CATEGORIES } from '../../constants/constants';
import userReader from 'core/readers/userReader';

const ProfileRecentGames = (props) => {
  const { isCurrentUser, user } = props;
  const router = useRouter();
  const userId = userReader.id(user);

  const [selectedRatingType, setSelectedRatingType] = useState(
    RATING_CATEGORIES[0].key,
  );
  const {
    loading,
    error,
    games: processedGames,
    fetchGames,
  } = useUserLastPlayedGames({
    userId,
    ratingType: selectedRatingType,
    pageSize: 5,
  });

  const onSeeMorePressed = useCallback(() => {
    router.push(`/profile/${user?.username}/recent-games`);
  }, [router, user?.username]);

  const handleSelectRatingType = useCallback((type) => {
    setSelectedRatingType(type);
  }, []);

  const { isMobile: isCompactMode } = useMediaQuery();

  const ComponentToBeRendered = isCompactMode
    ? CompactLast10PlayedGames
    : ExpandedLast10PlayedGames;

  return (
    <ComponentToBeRendered
      onSeeMorePressed={onSeeMorePressed}
      gamesData={processedGames}
      loading={loading}
      selectedRatingType={selectedRatingType}
      onSelectRatingType={handleSelectRatingType}
      isCurrentUser={isCurrentUser}
      user={user}
    />
  );
};

ProfileRecentGames.propTypes = {
  isCurrentUser: PropTypes.bool,
  user: PropTypes.object,
};

export default React.memo(ProfileRecentGames);
