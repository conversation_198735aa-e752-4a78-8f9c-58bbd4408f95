import PropTypes from 'prop-types';
import React, { useCallback } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { Icon, Text } from '@rneui/themed';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import dark from 'core/constants/themes/dark';
import RatingCategoryCard from 'modules/profile/components/RatingCategoryCard';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { useRouter } from 'expo-router';
import { GAME_CATEGORIES } from 'modules/home/<USER>/gameTypes';
import { RATING_CATEGORIES } from '../../../constants/constants';
import RecentGameCard from '../../RecentGameCard';
import styles from '../Last10PlayedGames.style';

const CompactLast10PlayedGames = (props) => {
  const {
    gamesData,
    loading,
    selectedRatingType,
    onSelectRatingType,
    onSeeMorePressed,
    isCurrentUser,
    user,
  } = props;

  const router = useRouter();

  const handleOnPressPlayAGame = useCallback(() => {
    if (selectedRatingType === GAME_CATEGORIES.PUZZLE) {
      router.push('/puzzle-home');
      return;
    }
    router.push(`/games?gameMode=${selectedRatingType}`);
  }, [router, selectedRatingType]);

  return (
    <View style={{ marginTop: -40, marginHorizontal: 16 }}>
      <View style={styles.recentGamesHeader}>
        <Text style={styles.recentGamesTitle}>Last 5 Games</Text>
        {!_isEmpty(gamesData) && (
          <Icon
            name="chevron-right"
            type="font-awesome-5"
            color={dark.colors.secondary}
            size={15}
            onPress={onSeeMorePressed}
          />
        )}
      </View>

      <View style={styles.ratingCategories}>
        {_map(RATING_CATEGORIES, (category) => (
          <RatingCategoryCard
            name={category.key}
            categoryKey={category.key}
            selectedRatingType={selectedRatingType}
            onSelectRatingType={onSelectRatingType}
          />
        ))}
      </View>

      {loading && (
        <ActivityIndicator
          size="large"
          color={dark.colors.primary}
          style={{ marginVertical: 30 }}
        />
      )}

      {!loading && _isEmpty(gamesData) && (
        <View style={styles.noGamesContainer}>
          <Text style={styles.noGamesText}>
            Not Played any {selectedRatingType} Games Yet
          </Text>
          {isCurrentUser && (
            <InteractiveSecondaryButton
              label="PLAY A GAME"
              onPress={handleOnPressPlayAGame}
              labelStyle={styles.playGameText}
              buttonContainerStyle={{ height: 38, width: 140 }}
            />
          )}
        </View>
      )}

      {!loading && !_isEmpty(gamesData) && (
        <View style={styles.recentGamesContainer}>
          {_map(gamesData, (game) => (
            <RecentGameCard
              key={game._id}
              recentGame={game}
              isCurrentUser={isCurrentUser}
            />
          ))}
        </View>
      )}
    </View>
  );
};

CompactLast10PlayedGames.propTypes = {
  gamesData: PropTypes.array,
  loading: PropTypes.bool,
  selectedRatingType: PropTypes.string,
  onSelectRatingType: PropTypes.func,
  onSeeMorePressed: PropTypes.func,
  isCurrentUser: PropTypes.bool,
  user: PropTypes.object,
};

export default React.memo(CompactLast10PlayedGames);
