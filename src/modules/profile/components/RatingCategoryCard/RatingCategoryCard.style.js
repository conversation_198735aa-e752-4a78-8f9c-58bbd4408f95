import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  ratingCategoryCard: {
    borderRadius: 20,
    borderWidth: 1,
    height: 32,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderColor: withOpacity(dark.colors.textLight, 0.1),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  ratingCategoryText: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    color: withOpacity(dark.colors.textLight, 0.4),
    letterSpacing: 0.5,
  },
  selectedRatingCategoryCard: {
    borderWidth: 1,
    borderColor: dark.colors.secondary,
    backgroundColor: withOpacity(dark.colors.textLight, 0.1),
  },
  selectedRatingCategoryText: {
    color: dark.colors.secondary,
  },
});

export default styles;
