import React from 'react';
import PropTypes from 'prop-types';
import { Text, TouchableOpacity, View } from 'react-native';
import styles from './RatingCategoryCard.style';

const RatingCategoryCard = ({
  name,
  categoryKey,
  selectedRatingType,
  onSelectRatingType,
}) => {
  const isSelected = selectedRatingType === categoryKey;

  return (
    <TouchableOpacity onPress={() => onSelectRatingType?.(categoryKey)}>
      <View
        style={[
          styles.ratingCategoryCard,
          isSelected && styles.selectedRatingCategoryCard,
        ]}
      >
        <Text
          style={[
            styles.ratingCategoryText,
            isSelected && styles.selectedRatingCategoryText,
          ]}
        >
          {name}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

RatingCategoryCard.propTypes = {
  name: PropTypes.string.isRequired,
  categoryKey: PropTypes.string.isRequired,
  selectedRatingType: PropTypes.string.isRequired,
  onSelectRatingType: PropTypes.func.isRequired,
};

export default React.memo(RatingCategoryCard);
