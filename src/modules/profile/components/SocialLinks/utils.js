import { ICON_TYPES } from 'atoms/Icon';
import _includes from 'lodash/includes';
import _toLower from 'lodash/toLower';
import _split from 'lodash/split';
import _first from 'lodash/first';
import _get from 'lodash/get';
import _startsWith from 'lodash/startsWith';
import _reduce from 'lodash/reduce';
import _tail from 'lodash/tail';

export const getSocialIcons = ({ links }) => {
  const socialPlatforms = {
    instagram: {
      iconConfig: {
        name: 'instagram',
        type: ICON_TYPES.ANT_DESIGN,
      },
      extractUsername: (url) => {
        // Extract username from Instagram URL patterns
        // instagram.com/username or instagram.com/username/
        if (_includes(_toLower(url), 'instagram.com/')) {
          const parts = _split(url, 'instagram.com/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        }
        return null;
      },
    },
    facebook: {
      iconConfig: {
        name: 'facebook',
        type: ICON_TYPES.ANT_DESIGN,
      },
      extractUsername: (url) => {
        // Extract username from Facebook URL patterns
        // facebook.com/username or facebook.com/profile.php?id=...
        if (_includes(_toLower(url), 'facebook.com/')) {
          const parts = _split(url, 'facebook.com/');
          if (parts.length > 1) {
            const potentialUsername = _first(
              _split(_get(parts, 1, ''), /[\/\?]/),
            );
            return !_startsWith(potentialUsername, 'profile.php')
              ? potentialUsername
              : null;
          }
        }
        return null;
      },
    },
    't.me': {
      iconConfig: {
        name: 'telegram',
        type: ICON_TYPES.ANT_DESIGN,
      },
      extractUsername: (url) => {
        // Extract username from Telegram URL patterns
        // t.me/username
        if (_includes(_toLower(url), 't.me/')) {
          const parts = _split(url, 't.me/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        }
        return null;
      },
    },
    telegram: {
      iconConfig: {
        name: 'telegram',
        type: ICON_TYPES.ANT_DESIGN,
      },
      extractUsername: (url) => {
        // Extract username from Telegram URL patterns
        // telegram.me/username or telegram.org/username
        const lowerUrl = _toLower(url);
        if (_includes(lowerUrl, 'telegram.me/')) {
          const parts = _split(url, 'telegram.me/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        } else if (_includes(lowerUrl, 'telegram.org/')) {
          const parts = _split(url, 'telegram.org/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        }
        return null;
      },
    },
    linkedin: {
      iconConfig: {
        name: 'linkedin',
        type: ICON_TYPES.FONT_AWESOME_6,
      },
      extractUsername: (url) => {
        // Extract username from LinkedIn URL patterns
        // linkedin.com/in/username or linkedin.com/company/company-name
        const lowerUrl = _toLower(url);
        if (_includes(lowerUrl, 'linkedin.com/in/')) {
          const parts = _split(url, 'linkedin.com/in/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        } else if (_includes(lowerUrl, 'linkedin.com/company/')) {
          const parts = _split(url, 'linkedin.com/company/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        }
        return null;
      },
    },
    twitter: {
      iconConfig: {
        name: 'twitter',
        type: ICON_TYPES.FONT_AWESOME_6,
      },
      extractUsername: (url) => {
        // Extract username from Twitter URL patterns
        // twitter.com/username
        if (_includes(_toLower(url), 'twitter.com/')) {
          const parts = _split(url, 'twitter.com/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        }
        return null;
      },
    },
    'x.com': {
      iconConfig: {
        name: 'x-twitter',
        type: ICON_TYPES.FONT_AWESOME_6,
      },
      extractUsername: (url) => {
        // Extract username from X.com URL patterns
        // x.com/username
        if (_includes(_toLower(url), 'x.com/')) {
          const parts = _split(url, 'x.com/');
          if (parts.length > 1) {
            const username = _first(_split(_get(parts, 1, ''), /[\/\?]/));
            return username || null;
          }
        }
        return null;
      },
    },
  };

  // Helper function to extract domain from URL
  const extractDomain = (url) => {
    // Remove protocol and get domain
    let domain = url;
    if (_includes(domain, '://')) {
      domain = _get(_split(domain, '://'), 1, '');
    }
    // Get the domain part (before any path, query, etc.)
    domain = _first(_split(domain, /[\/\?#]/));
    return _tail(_split(domain, 'www.'));
  };

  return _reduce(
    links,
    (acc, link) => {
      let isSocialLink = false;
      for (const [platform, config] of Object.entries(socialPlatforms)) {
        if (_includes(link, platform)) {
          // Extract username from the URL
          const username = config.extractUsername
            ? config.extractUsername(link)
            : null;

          isSocialLink = true;
          acc.push({
            platform,
            iconConfig: config.iconConfig,
            url: link,
            username,
          });
          break;
        }
      }
      if (!isSocialLink) {
        // For non-social links, extract domain as username and use web icon
        const domain = extractDomain(link);
        acc.push({
          platform: 'web',
          iconConfig: {
            name: 'globe',
            type: ICON_TYPES.FONT_AWESOME_6,
          },
          url: link,
          username: domain,
        });
      }
      return acc;
    },
    [],
  );
};
