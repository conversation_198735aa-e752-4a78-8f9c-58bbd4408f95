import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';
import { withOpacity } from '../../../../core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
  },
  mainContainer: {
    backgroundColor: dark.colors.gradientBackground,
    borderRadius: 12,
    paddingVertical: 12,
  },
  linkItem: {
    width: 100,
    // maxWidth: 100,
    height: 24,
    borderWidth: 1,
    borderColor: withOpacity(dark.colors.textLight, 0.4),
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingHorizontal: 8,
  },
  linkText: {
    fontFamily: 'Montserrat-600',
    fontSize: 9,
    maxWidth: 64,
    color: withOpacity(dark.colors.textLight, 0.4),
    letterSpacing: 0.5,
  },
});

export default styles;
