import React, { useCallback, useMemo } from 'react';
import {
  Linking,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _map from 'lodash/map';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Icon from 'atoms/Icon';
import styles from './SocialLinks.style';
import { getSocialIcons } from './utils';

const SocialLinks = ({ links }) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const { user } = useSession();

  const onPressSocialLink = useCallback(({ item } = EMPTY_OBJECT) => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_SOCIAL_LINK, {
      platform: item?.platform,
    });
    Linking.openURL(item?.url);
  }, []);

  const renderIcon = (item) => (
    <View style={styles.icon}>
      <Icon size={12} color="white" {...item.iconConfig} />
    </View>
  );

  const socialData = useMemo(() => getSocialIcons({ links }), [links]);

  if (_isEmpty(links) || _isNil(links)) {
    return null;
  }

  return (
    <View
      style={[
        { gap: 15, paddingTop: 20 },
        !isCompactMode && styles.mainContainer,
      ]}
    >
      <ScrollView
        horizontal
        style={{ width: '100%' }}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[styles.container, isCompactMode && { gap: 8 }]}
      >
        {_map(socialData, (item) => (
          <TouchableOpacity
            key={item.platform}
            style={styles.linkItem}
            onPress={() => onPressSocialLink({ item })}
          >
            {renderIcon(item)}
            <Text
              style={styles.linkText}
              ellipsizeMode="tail"
              numberOfLines={1}
            >{`${item.username}`}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export default React.memo(SocialLinks);
