import PropTypes from "prop-types"
import { View, Text, ScrollView } from "react-native"
import _map from "lodash/map"
import useUserAchievementStyles from "./UserAchievement.style"
import AchievementCard from "../AchievementCard"
import React from "react"

import _isEmpty from "lodash/isEmpty"
import _isNil from "lodash/isNil"

const UserAchievement = (props) => {
    const styles = useUserAchievementStyles()

    const { user } = props
    const { awardsAndAchievements: achievements } = user

    if (_isEmpty(achievements) || _isNil(achievements)) {
        return null
    }

    return (
        <View style={styles.container}>
            <Text style={styles.titleText}>
                Rewards and Recognitiion
            </Text>
            <ScrollView style={styles.achievements} 
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}>
                {
                    _map(achievements, (achievement, index) => <AchievementCard
                        description={achievement.description}
                        imageUrl={achievement.imageUrl}
                        key={`${index}`}
                        title={achievement.title} />)
                }
            </ScrollView>
        </View>
    )
}

UserAchievement.propTypes = {
    user: PropTypes.object
}


export default React.memo(UserAchievement)