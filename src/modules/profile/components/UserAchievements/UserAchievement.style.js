import { StyleSheet } from "react-native"
import dark from "../../../../core/constants/themes/dark"
import useMediaQuery from "core/hooks/useMediaQuery"
import { useMemo } from "react"

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        marginTop: !isCompactMode ? 0 : 16,
        marginBottom: 20,
        gap: 20,
        paddingHorizontal: isCompactMode ? 0 : 15,
        paddingVertical: isCompactMode ? 0 : 20,
        backgroundColor: isCompactMode ? "transparent" : dark.colors.gradientBackground,
        borderRadius: 12,
        marginHorizontal: 16,
    },

    titleText: {
        fontSize: isCompactMode ? 16 : 14,
        fontFamily: "Montserrat-700",
        lineHeight: 17,
        color: dark.colors.textLight,
        letterSpacing: 0.5
    },
    achievements: {
        gap: 20,
        maxHeight:400,
    }
})

const useUserAchievementStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useUserAchievementStyles