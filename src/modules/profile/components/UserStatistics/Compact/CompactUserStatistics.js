import React from 'react';
import { Image, View } from 'react-native';
import { Text } from '@rneui/themed';
import styles from '../UserStatistics.style';
import PropTypes from 'prop-types';
import _isNil from 'lodash/isNil';
import _take from 'lodash/take';
import _slice from 'lodash/slice';
import _map from 'lodash/map';

const CompactUserStatistics = (props) => {
  const { stats } = props;

  const StatsItem = ({ title, value, icon }) => {
    const displayValue = _isNil(value) ? '-' : value;
    return (
      <View style={styles.statBox}>
        <View style={styles.contentContainer}>
          <Text style={styles.statText1}>{displayValue}</Text>
          <Text style={styles.statText2}>{title}</Text>
        </View>
        <View style={styles.iconContainer}>
          <Image
            source={icon}
            style={{ width: 32, height: 32 }}
            resizeMode="cover"
          />
        </View>
      </View>
    );
  };
  const statsRow1 = _take(stats, 2);
  const statsRow2 = _slice(stats, 2, 4);

  return (
    <View style={styles.container}>
      <Text style={styles.headerText}>Stats Overview</Text>
      <View style={styles.statsContainer}>
        {_map(statsRow1, (stat, index) => (
          <StatsItem
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
          />
        ))}
      </View>

      <View style={[styles.statsContainer, { marginTop: 10 }]}>
        {_map(statsRow2, (stat, index) => (
          <StatsItem
            key={index + statsRow1.length}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
          />
        ))}
      </View>
    </View>
  );
};

CompactUserStatistics.propTypes = {
  user: PropTypes.object,
};

export default React.memo(CompactUserStatistics);
