import { StyleSheet } from "react-native";
import dark from "../../../../core/constants/themes/dark";
import { withOpacity } from "@/src/core/utils/colorUtils";

const styles = StyleSheet.create({
    container:{
        flex: 1,
        paddingHorizontal: 16,
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 12,
    },
    contentContainer:{
        
    },
    statBox: {
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderColor:withOpacity(dark.colors.textLight, 0.6),
        borderWidth:0.5,
        borderRadius: 10,
        flex: 1,
        height: 66,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    webStatsbox:{
        
    },
    statText1: {
        color: 'white',
        fontSize: 24,
        fontFamily: 'BebasNeue-500',
        paddingLeft: 2,
        lineHeight: 28,
        letterSpacing: 1,
        marginBottom: 4,
    },
    statText2: {
        color: withOpacity(dark.colors.textLight, 0.6),
        fontSize: 10,
        letterSpacing: 1,
        fontFamily: 'Montserrat-600',
    },
    headerText:{
        fontFamily: "Montserrat-700",
        fontSize: 16,
        lineHeight: 17,
        color: dark.colors.textLight,
        marginBottom:16,
    },
    iconContainer:{
        width:36,
        height:'100%',
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'flex-end',
    },
    
})

export default styles