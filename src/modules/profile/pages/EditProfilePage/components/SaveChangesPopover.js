import React from 'react';
import CustomOverlay from '../../../../../components/shared/CustomOverlay/CustomOverlay';
import dark from '../../../../../core/constants/themes/dark';

const SaveChangesPopover = ({
  isVisible,
  toggleSavePopover,
  handleSave,
  isUpdatingDetails,
}) => (
  <CustomOverlay
    overlayStyle={{ maxWidth: 300 }}
    isVisible={isVisible}
    toggleOverlay={toggleSavePopover}
    title="Are you sure you want to save the changes?"
    cancelText="Cancel"
    confirmText={isUpdatingDetails ? 'Saving....' : 'Save'}
    onCancel={toggleSavePopover}
    onConfirm={handleSave}
    cancelButtonColor="transparent"
    confirmButtonColor={dark.colors.secondary}
    cancelButtonTextStyle={{
      fontSize: 14,
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
    }}
    confirmButtonTextStyle={{
      fontSize: 14,
      fontFamily: 'Montserrat-500',
      color: dark.colors.secondary,
    }}
  />
);

export default React.memo(SaveChangesPopover);
