import React from 'react'
import { TouchableOpacity, Text } from 'react-native'

import styles from './SelectImagePopover.style'

import { Overlay } from '@rneui/themed'
import PropTypes from 'prop-types'
import useMediaQuery from 'core/hooks/useMediaQuery'

const SelectImagePopover = (props) => {
    const { isVisible, onBackdropPress, handleChooseImage, handleRemoveImage } = props
    const { isMobile: isCompactMode } = useMediaQuery()
    return (
        <Overlay
            isVisible={isVisible}
            onBackdropPress={onBackdropPress}
            overlayStyle={[styles.popoverImagePicker, !isCompactMode && { right: 150, top: 170 }]}
        >
            <TouchableOpacity onPress={handleChooseImage} style={styles.choose}>
                <Text style={styles.popoverText}>Choose Photo</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleRemoveImage} style={styles.remove}>
                <Text style={styles.popoverText}>Remove Photo</Text>
            </TouchableOpacity>
        </Overlay>
    )
}

SelectImagePopover.propTypes = {
    isVisible: PropTypes.bool,
    onBackdropPress: PropTypes.func,
    handleChooseImage: PropTypes.func,
    handleRemoveImage: PropTypes.func,
}
export default React.memo(SelectImagePopover)
