import { Dimensions, StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';
import dark from '../../../../core/constants/themes/dark';

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Dark.colors.gradient,
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    marginBottom: 20,
    color: 'white',
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: 100,
    borderRadius: 8,
    // marginBottom: 24,
  },
  userImage: {
    height: 80,
    width: 80,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    overflow: 'hidden',
    resizeMode: 'cover',
  },
  loadingContainer: {
    height: 80,
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    justifyContent: 'center',
  },
  inputLabel: {
    color: Dark.colors.textDark,
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 4,
  },
  errorLabel: {
    color: dark.colors.error,
    fontFamily: 'Montserrat-400',
    fontSize: 11,
    lineHeight: 12,
    marginTop: 4,
  },
  inputBoxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Dark.colors.primary,
    borderRadius: 8,
    borderColor: Dark.colors.tertiary,
    borderWidth: 1,
    flex: 1,
    gap: 6,
    height: 40,
    paddingHorizontal: 10,
  },
  input: {
    flex: 1,
    color: 'white',
    outlineStyle: 'none',
    fontSize: 14,
    height: 40,
    fontFamily: 'Montserrat-500',
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Dark.colors.primary,
    borderRadius: 5,
    paddingRight: 10,
    height: 40,
  },
  deleteButton: {
    backgroundColor: dark.colors.white,
  },
  deleteButtonText: {
    color: dark.colors.notification,
  },
  deleteOverlay: {
    maxWidth: 380,
  },
  saveButton: {
    width: '100%',
    justifyContent: 'center',
    borderRadius: 50,
    height: 40,
  },
  requiredTag: {
    color: Dark.colors.errorDark,
    fontSize: 12,
    fontFamily: 'Montserrat-400',
    lineHeight: 16,
  },
  inputFields: {
    gap: 20,
  },
  editImageOverlay: {
    height: 25,
    position: 'absolute',
    bottom: -10,
    left: 30,
    width: 25,
    backgroundColor: dark.colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 15,
  },
  editTextStyle: {
    color: 'white',
    fontSize: 17,
    fontFamily: 'Montserrat-500',
  },
  sectionTitle: {
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    fontSize: 12,
    marginBottom: 10,
  },
  headerExpanded: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  headerCompact: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    marginBottom: 24,
  },
  addButton: {
    width: 94,
    // height: 32,
    borderColor: dark.colors.secondary,
    backgroundColor: 'transparent',
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    fontSize: 14,
    textAlign: 'center',
  },
  removeButtonText: {
    fontFamily: 'Montserrat-500',
    color: dark.colors.error,
    fontSize: 12,
    lineHeight: 20,
    textAlign: 'left',
  },
  addButtonRow: {
    marginVertical: 10,
    width: '100%',
    alignItems: 'flex-start',
    justifyContent: 'flex-end',
  },
  achievementContainer: {
    gap: 10,
  },
  achievementImage: {
    height: 60,
    width: 60,
    borderColor: dark.colors.tertiary,
    borderRadius: 8,
    borderWidth: 1,
  },
  achievementImageContainer: {
    marginBottom: 10,
  },
});

export default styles;
