import React from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import ErrorView from 'atoms/ErrorView';
import CompactProfilePage from './Compact';
import ExpandedProfilePage from './Expanded';
import ProfilePageShimmer from '../../shimmers/ProfilePageShimmer/ProfilePageShimmer';
import useGetUserByUserNameQuery from '../../../../core/hooks/useGetUserByUsername';

const ProfilePageContainer = (props) => {
  const { username } = props;
  const { isMobile, isTablet } = useMediaQuery();

  const isCompactMode = isMobile || isTablet;

  const { user, loading, error, isCurrentUser, userAdditionalInfo } =
    useGetUserByUserNameQuery({ userName: username });

  if (loading) {
    return <ProfilePageShimmer />;
  }

  if (_isEmpty(user) || _isNil(user) || error) {
    return (
      <ErrorView errorMessage={`User not Found with username ${username}`} />
    );
  }

  const ProfilePageComponent = isCompactMode
    ? CompactProfilePage
    : ExpandedProfilePage;

  return (
    <ProfilePageComponent
      user={user}
      isCurrentUser={isCurrentUser}
      userAdditionalInfo={userAdditionalInfo}
    />
  );
};

ProfilePageContainer.propTypes = {
  username: PropTypes.string,
};

export default React.memo(ProfilePageContainer);
