import React, { useCallback, useState } from 'react';
import { ScrollView, View } from 'react-native';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import PropTypes from 'prop-types';
import { useSession } from 'modules/auth/containers/AuthProvider';
import DeleteAccountButton from 'modules/profile/components/DeleteAccountButton/DeleteAccountButton';
import useMediaQuery from 'core/hooks/useMediaQuery';
import styles from '../Profile.style';
import ProfileRecentGames from '../../../components/Last10PlayedGames';
import LogoutOverlay from '../../../../home/<USER>/MobileDrawerScreen/LogoutOverlay';
import UserProfileCard from '../../../components/UserProfileCard';
import UserStatistics from '../../../components/UserStatistics';
import BadgeBackground from '../../../components/BadgeBackground/BadgeBackground';
import RatingCard from '../../../components/RatingSection';
import Greetings from '../../../components/Greetings';
import XpChangeGraph from '../../../components/XPChangeGraph';
import { GET_USER_STATS } from '../../../constants/constants';

const CompactProfilePage = (props) => {
  const { user, isCurrentUser, userAdditionalInfo } = props;

  const [isOverlayVisible, setIsOverlayVisible] = useState(false);
  const { signOut } = useSession();
  const { isTablet } = useMediaQuery();

  const toggleOverlay = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.PROFILE.CLICKED_ON_PROFILE_PAGE_DELETE_ACCOUNT,
    );
    setIsOverlayVisible((prev) => !prev);
  }, [setIsOverlayVisible]);

  const confirmLogout = useCallback(() => {
    toggleOverlay();
    signOut?.();
  }, [toggleOverlay, signOut]);

  return (
    <View style={styles.compactProfileMainContainer}>
      <ScrollView
        style={{ flex: 1, height: '100%', width: '100%' }}
        contentContainerStyle={styles.container}
        showsVerticalScrollIndicator={false}
      >
        <BadgeBackground isCurrentUser={isCurrentUser} user={user} />
        <View style={styles.userCard}>
          <UserProfileCard
            user={user}
            isCurrentUser={isCurrentUser}
            userAdditionalInfo={userAdditionalInfo}
          />
        </View>
        <RatingCard user={user} />
        {/* {isCurrentUser && <UserCommitmentTracker />} */}
        {/* <InsightsSection isCurrentUser={isCurrentUser} user={user} /> */}
        <UserStatistics stats={GET_USER_STATS(user)} />
        {/* <RatingChangeStats statsData={statsData} /> */}
        <XpChangeGraph isCurrentUser={isCurrentUser} user={user} />
        {/* <UserAchievement user={user}/> */}
        <ProfileRecentGames isCurrentUser={isCurrentUser} user={user} />
        <Greetings />
        {isTablet && isCurrentUser && <DeleteAccountButton />}
      </ScrollView>
      <LogoutOverlay
        isVisible={isOverlayVisible}
        toggleOverlay={toggleOverlay}
        confirmLogout={confirmLogout}
        title={
          "Are you sure you want to delete your account? Once deleted, you can't retrieve your history."
        }
      />
    </View>
  );
};

CompactProfilePage.propTypes = {
  user: PropTypes.object,
  recentGames: PropTypes.array,
  statsData: PropTypes.array,
  isCurrentUser: PropTypes.bool,
  userAdditionalInfo: PropTypes.object,
};

export default React.memo(CompactProfilePage);
