import React from 'react';
import { ScrollView, View } from 'react-native';
import PropTypes from 'prop-types';
import _size from 'lodash/size';
import DeleteAccountButton from 'modules/profile/components/DeleteAccountButton';
import styles from '../Profile.style';
import ProfileRecentGames from '../../../components/Last10PlayedGames';
import UserProfileCard from '../../../components/UserProfileCard';
import UserStatistics from '../../../components/UserStatistics';
import BadgeBackground from '../../../components/BadgeBackground/BadgeBackground';
import RatingCard from '../../../components/RatingSection';
import XpChangeGraph from '../../../components/XPChangeGraph';
import { GET_USER_STATS } from '../../../constants/constants';

const ExpandedProfilePage = (props) => {
  const { user, recentGames, statsData, isCurrentUser, userAdditionalInfo } =
    props;

  return (
    <View style={styles.profileMainContainer}>
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={styles.expandedScrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <BadgeBackground isCurrentUser={isCurrentUser} user={user} />
        <View style={styles.userCard}>
          <UserProfileCard
            user={user}
            isCurrentUser={isCurrentUser}
            userAdditionalInfo={userAdditionalInfo}
          />
        </View>
        <RatingCard user={user} />
        {/* <InsightsSection isCurrentUser={isCurrentUser} user={user} /> */}
        <ProfileRecentGames
          recentGames={recentGames}
          isCurrentUser={isCurrentUser}
          user={user}
        />
        {
          isCurrentUser && <DeleteAccountButton />
        }
      </ScrollView>
      <ScrollView
        style={{ flex: 0.6, marginTop: 16 }}
        contentContainerStyle={{ gap: 25 }}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <UserStatistics stats={GET_USER_STATS(user)} />
        <XpChangeGraph user={user} isCurrentUser={isCurrentUser} />

        <View
          style={{
            width: '100%',
            height: _size(statsData) < 2 ? 350 : 240,
            overflow: 'hidden',
            borderRadius: 12,
          }}
        >
          {/* <RatingChangeStats statsData={statsData} user={user} /> */}
        </View>
        {/* <UserAchievement user={user} /> */}
      </ScrollView>
    </View>
  );
};

ExpandedProfilePage.propTypes = {
  user: PropTypes.object,
  recentGames: PropTypes.array,
  statsData: PropTypes.array,
  isCurrentUser: PropTypes.bool,
  userAdditionalInfo: PropTypes.object,
};

export default React.memo(ExpandedProfilePage);
