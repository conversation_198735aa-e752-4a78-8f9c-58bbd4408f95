import Header from '@/src/components/shared/Header';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './RatingDetailsPage.style';
import _map from 'lodash/map';
import {
  GET_USER_STATISTICS,
  RATING_CATEGORIES,
  RATING_DETAILS_FILTER,
} from '../../constants/constants';
import RatingDetailsCard from '../../components/RatingDetailsCard/RatingDetailsCard';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import UserStatistics from '../../components/UserStatistics';
import useGetUserRatingStatisticsByTimeline from '../../hooks/query/useGetUserRatingStatisticsByTimeline';
import RatingHistoryGraph from '../../components/RatingHistoryGraph';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _isEmpty from 'lodash/isEmpty';
import dark from '@/src/core/constants/themes/dark';
import RatingDetailsPageShimmer from './RatingDetailsPageShimmer';

const FilterOptionsCard = ({ filterKey, filterName, onPress, isSelected }) => {
  return (
    <TouchableOpacity
      style={[styles.filterCard, isSelected && styles.selectedFilterCard]}
      onPress={() => onPress(filterKey)}
      activeOpacity={0.7}
    >
      <Text
        style={[styles.filterText, isSelected && styles.selectedFilterText]}
      >
        {filterName}
      </Text>
    </TouchableOpacity>
  );
};

const RatingDetailsPage = (props) => {
  const { username } = props;
  const { user } = useSession();
  const isCurrentUser = user.username === username;
  const { isMobile: isCompactMode } = useMediaQuery();
  const [selectedFilterKey, setSelectedFilterKey] = useState(
    RATING_DETAILS_FILTER[0].key,
  );
  const [selectedRatingKey, setSelectedRatingKey] = useState(
    RATING_CATEGORIES[0].key,
  );

  const [displayedRatingTypeDetails, setDisplayedRatingTypeDetails] = useState(
    [],
  );
  const {
    loadStatistics,
    loading: loadingTimelineStats,
    error: errorTimelineStats,
    ratingOverallStats,
    ratingTypeDetails,
    getGraphPointsForType,
  } = useGetUserRatingStatisticsByTimeline();

  useEffect(() => {
    if (selectedFilterKey) {
      loadStatistics(selectedFilterKey, username);
    }
  }, [selectedFilterKey, username, loadStatistics]);

  useEffect(() => {
    if (!loadingTimelineStats) {
      setDisplayedRatingTypeDetails(ratingTypeDetails);
    }
    if (errorTimelineStats) {
      console.error('Error fetching timeline stats:', errorTimelineStats);
    }
  }, [
    ratingOverallStats,
    ratingTypeDetails,
    loadingTimelineStats,
    errorTimelineStats,
  ]);

  const handleFilterSelect = useCallback((key) => {
    setSelectedFilterKey(key);
  }, []);

  const handleRatingSelect = useCallback((key) => {
    setSelectedRatingKey((prevKey) => (prevKey === key ? null : key));
  }, []);

  const renderRatingItem = ({ item, index }) => (
    <View style={styles.ratingCardContainer}>
      <RatingDetailsCard
        index={index}
        ratingData={item}
        onPress={handleRatingSelect}
        isSelected={selectedRatingKey === item.type}
      />
    </View>
  );

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
      <Header title="Ratings" />
      <View style={styles.filterSection}>
            <ScrollView
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{paddingHorizontal: 16, gap: 8}}
            >
              {_map(RATING_DETAILS_FILTER, (filter) => (
                <FilterOptionsCard
                  key={filter.key}
                  filterKey={filter.key}
                  filterName={filter.filter}
                  onPress={handleFilterSelect}
                  isSelected={selectedFilterKey === filter.key}
                />
              ))}
            </ScrollView>
          </View>
      {loadingTimelineStats ? (
        <RatingDetailsPageShimmer />
      ) : (
        <>
          <View style={{ height: 300 }}>
            <FlatList
              data={displayedRatingTypeDetails}
              renderItem={renderRatingItem}
              keyExtractor={(item) => item.type}
              numColumns={2}
              contentContainerStyle={styles.ratingsGrid}
              showsVerticalScrollIndicator={false}
              extraData={selectedRatingKey}
            />
          </View>
          <UserStatistics
            stats={GET_USER_STATISTICS(ratingOverallStats(selectedRatingKey))}
          />
          <RatingHistoryGraph
            graphData={getGraphPointsForType(selectedRatingKey)}
            ratingKey={selectedRatingKey}
            isCurrentUser={isCurrentUser}
          />
        </>
      )}

    </ScrollView>
  );
};

export default RatingDetailsPage;
