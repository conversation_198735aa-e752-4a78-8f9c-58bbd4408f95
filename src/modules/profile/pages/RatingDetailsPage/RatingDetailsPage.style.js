import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { withOpacity } from '@/src/core/utils/colorUtils';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  filterSection: {
    height: 50,
    marginTop: 24,
    borderBottomWidth: 1,
    borderBottomColor: withOpacity(dark.colors.textLight, 0.1),
  },
  filterCard: {
    borderRadius: 16,
    borderWidth: 1,
    height: 32,
    paddingHorizontal: 12,
    borderColor: withOpacity(dark.colors.textLight, 0.2),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 800, 
    alignSelf: 'center',
  },
  selectedFilterCard: {
    borderColor: dark.colors.secondary,
    backgroundColor: withOpacity(dark.colors.textLight, 0.1),
    borderWidth: 1,
  },
  filterText: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    color: withOpacity(dark.colors.textLight, 0.6),
    letterSpacing: 0.5,
  },
  selectedFilterText: {
    color: dark.colors.secondary,
  },
  ratingsGrid: {
    paddingHorizontal: 12,
    paddingTop: 20,
  },
  ratingCardContainer: {
    flex: 1 / 2,
    padding: 6,
  },
});

export default styles;
