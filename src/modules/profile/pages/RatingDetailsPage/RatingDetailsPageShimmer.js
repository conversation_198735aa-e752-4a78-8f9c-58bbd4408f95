import React from 'react';
import { View, StyleSheet } from 'react-native';
import ShimmerView from '@/src/components/molecules/ShimmerView/ShimmerView';
import _map from 'lodash/map'

const RatingDetailsPageShimmer = () => {

  const renderStatsShimmerCard = () => {
    return (
      <ShimmerView style={styles.userStatisticsCard}/>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.ratingsGrid}>
        <ShimmerView style={styles.ratingCard} />
        <ShimmerView style={styles.ratingCard} />
        <ShimmerView style={styles.ratingCard} />
        <ShimmerView style={styles.ratingCard} />
      </View>
      <ShimmerView style={styles.statsOverviewText}/>
      <View style={styles.userStatisticsContainer}>
        {_map(Array.from({ length: 4 }), (_, index) => (
          <React.Fragment key={index}>
            {renderStatsShimmerCard()}
          </React.Fragment>
        ))}
      </View>
      <ShimmerView style={styles.ratingHistoryGraph} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    marginBottom: 24,
    marginTop: 24
  },
  ratingsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  ratingCard: {
    width: '48%',
    height: 120,
    marginBottom: 8,
    borderRadius: 10
  },
  ratingHistoryGraph: {
    width: "100%",
    height: 200,
    borderRadius: 10
  },
  userStatisticsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  userStatisticsCard: {
    width: '48%',
    height: 60,
    marginBottom: 8,
    borderRadius: 6
  },
  statsOverviewText:{
    width: 160,
    height: 20,
    marginTop: 12,
    marginBottom:10,
    borderRadius: 4
  }
});

export default React.memo(RatingDetailsPageShimmer);