import React, { useCallback, useState } from 'react';
import { Text, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';
import _toString from 'lodash/toString';
import Loading from '@/src/components/atoms/Loading';
import styles from './RecentGames.style';
import PaginatedList from '../../../../components/shared/PaginatedList';
import RecentGameCard from '../../components/RecentGameCard';
import useMediaQuery from '../../../../core/hooks/useMediaQuery';
import useUserGamesByRatingType from '../../hooks/query/useUserGamesByRatingType';
import useGetUserByUserNameQuery from '../../../../core/hooks/useGetUserByUsername';
import PropTypes from 'prop-types';
import ErrorView from 'atoms/ErrorView';
import { RATING_CATEGORIES } from '../../constants/constants';
import RatingCategoryCard from '../../components/RatingCategoryCard/RatingCategoryCard';
import userReader from '@/src/core/readers/userReader';
import Header from 'shared/Header';
import WebBackButton from '@/src/components/shared/WebBackButton';

const PAGE_SIZE = 50;

const RecentGamesPage = (props) => {
  const { user, isCurrentUser } = props;
  const userId = userReader.id(user);

  const [selectedRatingType, setSelectedRatingType] = useState(
    RATING_CATEGORIES[0].key,
  );

  const { isMobile: isCompactMode } = useMediaQuery();

  const { fetchPageData } = useUserGamesByRatingType({
    userId,
    ratingType: selectedRatingType,
    pageSize: PAGE_SIZE,
  });

  const handleSelectRatingType = useCallback(
    (type) => {
      if (type !== selectedRatingType) {
        setSelectedRatingType(type);
      }
    },
    [selectedRatingType],
  );

  const renderGameCard = useCallback(
    ({ item }) => {
      return (
        <View
          style={{
            alignItems: 'center',
            paddingHorizontal: isCompactMode ? 16 : 0,
          }}
        >
          <RecentGameCard recentGame={item} isCurrentUser={isCurrentUser} />
        </View>
      );
    },
    [isCurrentUser, isCompactMode],
  );

  const renderListHeader = useCallback(
    () => (
      <View
        style={[
          styles.ratingCategoriesContainer,
          !isCompactMode && styles.expandedRatingCategoriesContainer,
        ]}
      >
        <View style={styles.ratingCategories}>
          {_map(RATING_CATEGORIES, (category) => (
            <RatingCategoryCard
              key={category.key}
              name={category.key}
              categoryKey={category.key}
              selectedRatingType={selectedRatingType}
              onSelectRatingType={handleSelectRatingType}
            />
          ))}
        </View>
      </View>
    ),
    [isCompactMode, selectedRatingType, handleSelectRatingType],
  );

  const renderPlaceholderComponent = useCallback(
    () => <Loading label={`Loading ${selectedRatingType} games...`} />,
    [selectedRatingType],
  );

  return (
    <View style={[styles.container, !isCompactMode && { maxWidth: '80%' }]}>
      <Header title="Recent Games" />
      <View style={{ marginVertical: 10 }}>
        <WebBackButton title={'Recent Played Games'} />
      </View>
      <PaginatedList
        key={selectedRatingType}
        fetchData={fetchPageData}
        renderItem={renderGameCard}
        pageSize={PAGE_SIZE}
        keyExtractor={(item, index) => _toString(item?.id) + _toString(index)}
        renderHeader={renderListHeader}
        placeholderComponent={renderPlaceholderComponent}
        emptyListComponent={
          <Text style={styles.emptyListText}>
            No {selectedRatingType} games found.
          </Text>
        }
        contentContainerStyle={styles.list}
        listFooterComponent={<View style={{ height: 80 }}></View>}
        style={{ flex: 1 }}
      />
    </View>
  );
};

RecentGamesPage.propTypes = {
  user: PropTypes.object,
  isCurrentUser: PropTypes.bool,
};

const RecentGamesPageContainer = (props) => {
  const { username } = props;
  const { user, loading, error, isCurrentUser } = useGetUserByUserNameQuery({
    userName: username,
  });

  if (loading) {
    return <Loading label={'Loading user Profile'} />;
  }

  if (_isEmpty(user) || _isNil(user) || error) {
    return (
      <ErrorView errorMessage={`User not Found with username ${username}`} />
    );
  }

  return <RecentGamesPage user={user} isCurrentUser={isCurrentUser} />;
};

RecentGamesPageContainer.propTypes = {
  username: PropTypes.string,
};

export default React.memo(RecentGamesPageContainer);
