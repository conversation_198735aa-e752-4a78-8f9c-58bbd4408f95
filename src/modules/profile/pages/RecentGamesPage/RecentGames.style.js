import { StyleSheet } from 'react-native'
import Dark from '@/src/core/constants/themes/dark'

const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: '100%',
        backgroundColor: Dark.colors.background,
    },
    filterContainer: {
        height: 60,
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        paddingHorizontal: 10,
        marginBottom: 30,
    },
    filterButton: {
        paddingHorizontal: 20,
        marginHorizontal: 5,
        height: 40,
        borderRadius: 20,
        backgroundColor: Dark.colors.tertiary,
    },
    cardsContainer: {
        alignItems: 'center',
    },
    heading:{
        fontFamily: 'Montserrat-500',
        color:'white',
        marginTop:20,
        marginBottom:5,
        fontSize:20
    },
    ratingCategoriesContainer: {
        paddingHorizontal: 16, 
        marginBottom: 10, 
    },
    expandedRatingCategoriesContainer: {
        paddingHorizontal: 0,
    },
    ratingCategories: {
        flexDirection: 'row',
        marginTop: 10,
        flexWrap: 'wrap',
        marginBottom: 10, 
    },
    allLoadedText: {
        textAlign: 'center',
        paddingVertical: 20,
        color: Dark.colors.textDark,
        fontFamily: 'Montserrat-500',
    },
    emptyListText: {
        textAlign: 'center',
        paddingVertical: 40,
        color: Dark.colors.textDark,
        fontFamily: 'Montserrat-500',
        fontSize: 14,
    },
    list: {
        paddingBottom: 80,
    },
    recentPlayedText:{
        fontSize: 16,
        fontFamily: 'Montserrat-700',
        color:Dark.colors.textLight,

    }
})

export default styles
