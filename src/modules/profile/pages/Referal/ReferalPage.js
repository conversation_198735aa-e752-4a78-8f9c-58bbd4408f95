import React, { useCallback } from 'react';
import { Image, ScrollView, Text, View } from 'react-native';
import Header from '@/src/components/shared/Header';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import ShieldImage from '@/assets/images/shield.png';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import dark from '@/src/core/constants/themes/dark';
import shieldIcon from '@/assets/images/icons/shield_icon.png';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import userReader from '@/src/core/readers/userReader';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useRouter } from 'expo-router';
import WebBackButton from '@/src/components/shared/WebBackButton';
import Analytics from '@/src/core/analytics';
import { EVENTS } from '@/src/core/analytics/events/profile';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import useInviteFriendOnMatiks from 'core/hooks/useInviteFriendOnMatiks';
import styles from './ReferalPage.style';

const ReferalPage = () => {
  const { user } = useSession();
  const router = useRouter();
  const referralCode = userReader.referralCode(user) ?? 'CODE';

  const { isMobile: isCompactDevice } = useMediaQuery();

  const { handleNormalShare } = useInviteFriendOnMatiks();
  const handleShareLink = useCallback(() => {
    handleNormalShare({
      eventToBeTracked: EVENTS.PROFILE.CLICKED_ON_SHARE_REFERRAL_LINK,
    });
  }, [handleNormalShare]);

  const handleStreakShieldCountPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_TRANSACTION_BUTTON);
    router.push('/streak-shield');
  }, [router]);

  const renderShareCodeContainer = () => (
    <View style={styles.shareCodeContainer}>
      <Text style={styles.referalCode}>{referralCode ?? 'CODE'}</Text>
      <InteractiveSecondaryButton
        onPress={handleShareLink}
        label="SHARE CODE"
        iconConfig={{
          name: 'paper-airplane',
          type: ICON_TYPES.OCTICONS,
          size: 20,
        }}
        buttonContainerStyle={styles.shareButtonContainerStyle}
        labelStyle={styles.shareLabelText}
        borderColor={dark.colors.streak}
        buttonStyle={styles.shareButtonStyle}
        buttonBackgroundStyle={styles.shareButtonBackground}
      />
    </View>
  );

  const renderInfoComponent = (title) => (
    <View style={styles.infoContainer}>
      <View style={styles.infoContent}>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.shieldInfoContainer}>
          <Image
            source={shieldIcon}
            style={{ width: 14, height: 14 }}
            resizeMode="contain"
          />
          <Text style={styles.valueText}>x1</Text>
        </View>
      </View>
    </View>
  );

  const renderTransactionsButton = () => (
    <InteractiveSecondaryButton
      label="Transactions"
      labelStyle={styles.transactionsLabel}
      buttonContainerStyle={{ width: 120, height: 40 }}
      onPress={handleStreakShieldCountPress}
    />
  );

  return (
    <View style={[{ flex: 1 }, !isCompactDevice && styles.webContainer]}>
      <Header renderTrailingComponent={renderTransactionsButton} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        {!isCompactDevice && (
          <View style={styles.webHeaderStyle}>
            <WebBackButton renderTrailingComponent={renderTransactionsButton} />
          </View>
        )}
        <Image source={ShieldImage} style={styles.shieldImage} />
        <TextWithShadow
          text="GET A FREE STREAK SHIELD FOR EVERY INVITE"
          textStyle={styles.streakText}
          containerStyle={styles.streakTextContainer}
          numberOfLines={0}
          shadowOffsetX={0}
          shadowOffsetY={6}
          shadowWidth={0}
          width={320}
          strokeWidth={5}
          strokeColor="#000000"
        />
        {renderShareCodeContainer()}
        <View
          style={{
            width: '100%',
            flexDirection: 'column',
            marginTop: 60,
            gap: 12,
            alignItems: 'center',
          }}
        >
          {renderInfoComponent('You Get:')}
          {renderInfoComponent('They Get:')}
        </View>
      </ScrollView>
    </View>
  );
};

export default React.memo(ReferalPage);
