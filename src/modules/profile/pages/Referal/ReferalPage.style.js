import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  buttonContainerStyle: {
    width: 100,
    height: 35,
  },

  labelStyles: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: 'white',
    opacity: 0.7,
  },
  webContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },

  container: {
    flex: 1,
  },
  contentContainer: {
    alignItems: 'center',
    flexDirection: 'column',
    gap: 10,
    paddingBottom: 16,
  },
  shieldImage: {
    width: 200,
    height: 200,
    marginTop: 30,
  },
  streakText: {
    textAlign: 'center',
    fontSize: 18,
    fontFamily: 'Montserrat-800',
    color: dark.colors.streak,
    alignSelf: 'center',
  },
  streakCompactText: {
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Montserrat-800',
    color: dark.colors.streak,
    alignSelf: 'center',
  },
  streakTextContainer: {
    width: '90%',
    height: 70,
    alignSelf: 'center',
  },
  shareCodeContainer: {
    width: '90%',
    alignSelf: 'center',
    height: 58,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    borderRadius: 200,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 50,
  },
  referalCode: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    color: dark.colors.streak,
    letterSpacing: 4,
    marginLeft: 28,
  },
  shareButtonContainerStyle: {
    width: 140,
    height: 36,

    marginRight: 15,
    marginBottom: 5,
  },
  shareLabelText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.streak,
  },
  shareButtonBackground: {
    borderRadius: 130,
    height: 30,
    bottom: -5,
  },
  shareButtonStyle: {
    borderRadius: 200,
    borderWidth: 1,
  },
  infoContainer: {
    width: '80%',
    height: 72,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
  },

  title: {
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
  },
  shieldInfoContainer: {
    width: 56,
    height: 32,
    borderWidth: 0.5,
    borderRadius: 20,
    justifyContent: 'center',
    gap: 6,
    alignItems: 'center',
    borderColor: dark.colors.textDark,
    backgroundColor: dark.colors.tertiary,
    flexDirection: 'row',
  },
  webHeaderStyle: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    width: '100%',
    maxWidth: 600,
    padding: 16,
  },
  valueText: {
    fontFamily: 'Montserrat-700',
    fontSize: 12,
    color: dark.colors.textLight,
  },
  transactionsButton: {
    paddingHorizontal: 10,
  },
  transactionsLabel: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    color: dark.colors.textLight,
  },
});

export default styles;
