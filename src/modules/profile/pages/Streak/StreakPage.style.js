import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';
import dark from '../../../../core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    webContainer: {
      width: '100%',
      maxWidth: 400,
      justifyContent: 'center',
      alignSelf: 'center',
      marginRight: 190,
    },
    innerContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      height: '70%',
    },

    footer: {
      width: '100%',
      height: 80,
      paddingHorizontal: 20,
      alignItems: 'center',
      justifyContent: 'center',
      borderTopColor: dark.colors.tertiary,
      borderBottomColor: 'transparent',
      paddingBottom: 10,
    },
    buttonContainerStyle: {
      width: '100%',
      height: 60,
      paddingTop: 10,
    },
    webButtonContainerStyle: {
      width: '80%',
    },
    labelStyles: {
      fontFamily: 'Montserrat-800',
      fontSize: 12,
      color: dark.colors.streakOrangeColor,
    },

    streakAvailableContainer: {
      width: 110,
      height: 32,
      backgroundColor: dark.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      gap: 6,
      borderRadius: 20,
      borderWidth: 0.5,
      borderColor: dark.colors.tertiary,
      alignSelf: 'center',
    },
    streakAvailableText: {
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      color: dark.colors.textLight,
    },
    playGameCard: {
      width: '90%',
      height: 110,
      marginHorizontal: 16,
      borderWidth: 1,
      borderRadius: 12,
      borderColor: withOpacity(dark.colors.textLight, 0.4),
      flexDirection: 'row',
      gap: 12,
    },
    timerContainer: {
      height: 110,
      width: 110,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column',
      gap: 4,
    },
    timerText: {
      fontFamily: 'Montserrat-700',
      fontSize: 12,
      color: dark.colors.streakOrangeColor,
    },
    playContainer: {
      height: 110,
      width: '60%',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: 12,
      fontFamily: 'Montserrat-600',
      color: dark.colors.textLight,
    },
    contentContainer: {
      width: '100%',
      height: 220,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 50,
    },
    streakCountStyle: {
      fontSize: 96,
      color: dark.colors.textLight,
      fontFamily: 'Montserrat-800',
      letterSpacing: 2,
    },
    streakCountDisabledStyle: {
      fontSize: 96,
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-800',
      letterSpacing: 2,
    },
    streakCountContainer: {
      alignSelf: 'center',
    },
    dayStreakText: {
      fontSize: 10,
      fontFamily: 'Montserrat-400',
      color: dark.colors.textLight,
      opacity: 0.4,
      letterSpacing: 2,
      marginTop: 8,
      marginBottom: 28,
    },
    maxStreakContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
      marginBottom: 14,
    },
    maxStreak: {
      fontFamily: 'Montserrat-700',
      fontSize: 10,
      letterSpacing: 2,
      color: dark.colors.textLight,
    },
    streakSheet: {
      width: '100%',
      height: 450,
      paddingTop: 40,
      justifyContent: 'flex-start',
      alignItems: 'center',
      flexDirection: 'column',
    },
    shieldImage: {
      width: 120,
      height: 120,
      marginTop: 20,
      alignSelf: 'center',
    },
    streakShieldTitle: {
      fontSize: 28,
      fontFamily: 'Montserrat-800',
      color: dark.colors.streakOrangeColor,
      marginTop: 20,
    },
    streakTitleContainer: {
      justifyContent: 'center',
      alignSelf: 'center',
    },
    description: {
      fontFamily: 'Montserrat-500',
      fontSize: 12,
      color: dark.colors.textLight,
      textAlign: 'center',
      marginTop: 10,
    },

    learnText: {
      fontFamily: 'Montserrat-600',
      fontSize: 12,
      letterSpacing: 1,
      color: dark.colors.textLight,
      alignSelf: 'center',
    },
    calendarContainer: {
      width: '85%',
      height: 400,
      marginTop: 20,
      justifyContent: 'flex-start',
      alignItems: 'center',
      alignSelf: 'center',
      marginBottom: 50,
    },
    imageContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      width: '100%',
      height: 100,
      justifyContent: 'center',
      alignItems: 'center',
    },
    divider: {
      width: '100%',
      height: 1,
      backgroundColor: dark.colors.tertiary,
    },
    rewardsSection: {
      width: '100%',
      height: 280,
      justifyContent: 'center',
      alignItems: 'center',
    },
    rewardMainContainer: {
      flexDirection: 'column',
      gap: 40,
    },
    rewardContainer: {
      width: '90%',
      height: 115,
      borderRadius: 12,
      borderColor: withOpacity(dark.colors.textLight, 0.4),
      borderWidth: 1,
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignSelf: 'center',
    },
    rewardContent: {
      flexDirection: 'column',
      width: '70%',
      paddingRight: 2,
      height: 115,
      justifyContent: 'center',
      alignItems: 'flex-start',
    },
    days: {
      fontSize: 20,
      fontFamily: 'Montserrat-600',
      color: dark.colors.textLight,
    },
    rewardDesc: {
      fontFamily: 'Montserrat-600',
      fontSize: 12,
      color: dark.colors.textLight,
      marginTop: 4,
    },
    locked: {
      fontFamily: 'Montserrat-800',
      fontSize: 12,
      color: dark.colors.textDark,
      marginTop: 8,
      letterSpacing: 1,
    },
  });

const useStreakPageStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useStreakPageStyles;
