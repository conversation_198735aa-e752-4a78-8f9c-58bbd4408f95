import React, { useRef } from 'react';
import { Image, Platform, Text, View } from 'react-native';
import styles from './StreakSharebleCard.style';
import TextWithShadow from 'shared/TextWithShadow';
import matiksLogo from '@/assets/images/LinearGradientIcons/matiksBolt.png';
import appStoreIcon from '@/assets/images/icons/app-store.png';
import playStoreIcon from '@/assets/images/icons/play-store.png';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import useCaptureView from 'core/hooks/useCaptureView';
import userReader from 'core/readers/userReader';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { showToast, TOAST_TYPE } from 'molecules/Toast';

interface StreakSharableCardProps {
  streak: number;
}

const StreakSharebleCard = ({ streak }: StreakSharableCardProps) => {
  const viewShotRef = useRef();
  const { captureView } = useCaptureView();
  const isWeb = Platform.OS === 'web';
  const { user } = useSession();

  const handleDownload = () => {
    if (!isWeb || !viewShotRef.current) {
      return;
    }
    const fileName = `streak-card-${userReader.username(user)}.png`;
    const message = `Here is My Steak at Matiks! Username: ${userReader.username(user)}`;

    captureView({
      viewRef: viewShotRef,
      message: message,
      fileName: fileName,
    }).catch(() => {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: 'Failed to share profile. Please try again.',
      });
    });
  };

  return (
    <View ref={viewShotRef} style={styles.mainContainer}>
      <View style={styles.container}>
        <TextWithShadow
          text={streak ?? 0}
          textStyle={styles.streakText}
          containerStyle={styles.streakTextContainer}
          strokeColor="#000000"
          strokeWidth={10}
          shadowOffsetY={-15}
          shadowOffsetX={5}
          shadowWidth={isWeb ? 0 : 8}
        />
        <Text style={styles.dayStreak}>DAY STREAK</Text>
      </View>
      <View style={styles.footer}>
        <View style={styles.matiksContainer}>
          <Image source={matiksLogo} style={styles.matiksLogo} />
          <Text style={styles.matiksText}>MATIKS</Text>
        </View>
        <View style={styles.iconsContainer}>
          <Image source={appStoreIcon} style={styles.matiksLogo} />
          <Image source={playStoreIcon} style={styles.matiksLogo} />
        </View>
      </View>
      {isWeb && (
        <InteractiveSecondaryButton
          label="Download"
          buttonContainerStyle={{ marginTop: 20 }}
          labelStyle={{
            fontFamily: 'Montserrat-600',
            fontSize: 14,
            color: dark.colors.streakOrangeColor,
          }}
          buttonBackgroundStyle={{ paddingHorizontal: 16 }}
          onPress={handleDownload}
        />
      )}
    </View>
  );
};

export default StreakSharebleCard;
