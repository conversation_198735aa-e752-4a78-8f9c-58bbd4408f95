import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'column',
    width: 275,
    height: 300,
    backgroundColor: dark.colors.textLight,
  },
  container: {
    height: 250,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    gap: 4,
  },
  streakText: {
    fontFamily: 'Montserrat-800',
    fontSize: 124,
    color: dark.colors.streakOrangeColor,
  },
  streakTextContainer: {
    alignSelf: 'center',
  },
  dayStreak: {
    fontFamily: 'Montserrat-700',
    fontSize: 10,
    color: 'black',
    letterSpacing: 2,
  },
  footer: {
    width: '100%',
    height: 52,
    backgroundColor: withOpacity(dark.colors.tertiary, 0.1),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  matiksContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  matiksLogo: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
  },
  matiksText: {
    fontFamily: 'Montserrat-700',
    letterSpacing: 1,
    fontSize: 12,
    color: dark.colors.tertiary,
  },
  iconsContainer: {
    flexDirection: 'row',
    gap: 10,
  },
});

export default styles;
