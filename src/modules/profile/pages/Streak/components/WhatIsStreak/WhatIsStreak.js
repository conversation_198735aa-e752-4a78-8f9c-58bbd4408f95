import React, { useCallback } from 'react';
import { Image, ImageBackground, Text, View } from 'react-native';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import ShieldImage from '@/assets/images/shield.png';
import StreakBGImage from '@/assets/images/streak_arrow.png';
import shieldIcon from '@/assets/images/icons/shield_icon.png';
import { Pressable } from 'react-native-gesture-handler';
import { useRouter } from 'expo-router';
import styles from './WhatIsStreak.style';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import dark from '@/src/core/constants/themes/dark';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

const WhatIsStreak = ({
  streakFreezers,
  onPressEarnShield,
  closeBottomSheet,
}) => {
  const router = useRouter();

  const onPressStreakShieldCount = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.VIEWED_STREAK_FREEZER_TRANSACTIONS)
    closeBottomSheet();
    router.push('/streak-shield');
  }, [router]);

  const renderCenterComponent = () => (
    <Pressable onPress={onPressStreakShieldCount}>
      <View style={styles.streakAvailableContainer}>
        <Image
          source={shieldIcon}
          style={{ width: 14, height: 14 }}
          resizeMode="contain"
        />
        <Text
          style={styles.streakAvailableText}
        >{`${streakFreezers ?? 0} Available`}</Text>
      </View>
    </Pressable>
  );

  const onPressHowToEarnShield = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_HOW_TO_EARN_STREAK_FREEZER)
    closeBottomSheet();
    onPressEarnShield();
  }, [closeBottomSheet, onPressEarnShield]);

  return (
    <View style={styles.streakSheet}>
      {renderCenterComponent()}
      <Image source={ShieldImage} style={styles.shieldImage} />
      <TextWithShadow
        text="STREAK SHIELD"
        textStyle={styles.streakShieldTitle}
        containerStyle={styles.streakTitleContainer}
        shadowColor="black"
        shadowOffsetX={-4}
        shadowOffsetY={-2}
        strokeColor="#000000"
        strokeWidth={3}
      />
      <Text style={styles.description}>
        {`Defends your streak if you \n miss a day practice`}
      </Text>
      <Text style={styles.disclaimerText}>
        YOU CAN ONLY USE 2 STREAK SHIELDS AT A TIME
      </Text>
      <View style={styles.footer}>
        <InteractiveSecondaryButton
          label={'LEARN HOW TO EARN STREAK SHIELD'}
          onPress={onPressHowToEarnShield}
          buttonContainerStyle={[styles.buttonContainerStyle]}
          labelStyle={styles.labelStyles}
          borderColor={dark.colors.streakOrangeColor}
          buttonStyle={{paddingHorizontal: 12}}
        />
      </View>
      {/* <Pressable
        onPress={() => onPressHowToEarnShield()}
        style={styles.pressableContainer}
      >
        <ImageBackground
          source={StreakBGImage}
          style={styles.imageContainer}
          resizeMode="cover"
        >
          <Text style={styles.learnText}>EARN STREAK SHIELD</Text>
        </ImageBackground>
      </Pressable> */}
    </View>
  );
};

export default React.memo(WhatIsStreak);
