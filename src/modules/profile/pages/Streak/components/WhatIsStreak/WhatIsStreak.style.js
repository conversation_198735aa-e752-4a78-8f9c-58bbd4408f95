import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  streakSheet: {
    width: '100%',
    height: 500,
    paddingTop: 40,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'column',
  },
  buttonContainerStyle: {
    width: '100%',
    height: 60,
    paddingTop: 10,
    bottom: 0,
  },
  footer: {
    width: '100%',
    height: 120,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'flex-end',
    borderTopColor: dark.colors.tertiary,
    borderBottomColor: 'transparent',
  },
  labelStyles: {
    fontFamily: 'Montserrat-800',
    fontSize: 12,
    color: dark.colors.streakOrangeColor,
  },
  streakAvailableContainer: {
    width: 110,
    height: 32,
    backgroundColor: dark.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 6,
    borderRadius: 20,
    borderWidth: 0.5,
    borderColor: dark.colors.textDark,
    alignSelf: 'center',
  },
  streakAvailableText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
  },

  shieldImage: {
    width: 120,
    height: 120,
    marginTop: 20,
    alignSelf: 'center',
  },
  streakShieldTitle: {
    fontSize: 28,
    fontFamily: 'Montserrat-800',
    color: dark.colors.streakOrangeColor,
    marginTop: 20,
  },
  streakTitleContainer: {
    justifyContent: 'center',
    alignSelf: 'center',
    marginTop: 12,
  },
  description: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    color: dark.colors.textLight,
    textAlign: 'center',
    marginTop: 10,
  },
  disclaimerText: {
    fontFamily: 'Montserrat-600',
    fontSize: 10,
    color: dark.colors.textLight,
    opacity: 0.4,
    marginTop: 30,
  },
  imageContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  learnText: {
    fontFamily: 'Montserrat-600',
    fontSize: 12,
    letterSpacing: 1,
    color: dark.colors.textLight,
    alignSelf: 'center',
  },
  pressableContainer: {
    flex: 1,
    width: '100%',
    height: 50,
  },
});

export default styles;
