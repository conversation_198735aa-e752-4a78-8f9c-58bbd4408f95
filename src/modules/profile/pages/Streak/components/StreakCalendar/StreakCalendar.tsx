import React, { useCallback, useState } from 'react';
import { View } from 'react-native';
import _isNil from 'lodash/isNil';
import getCurrentTimeWithOffset from '@/src/core/utils/getCurrentTimeWithOffset';
import useGetUserStreakHistory from '@/src/modules/profile/hooks/query/useGetUserStreakHistory';
import { format, parse } from 'date-fns';
import _find from 'lodash/find';
import CalendarView, { CalendarMonthSelector } from 'shared/CalendarView';
import styles from './StreakCalendar.style';
import StreakCalendarDate from '../StreakCalendarDate';

type MonthYearTypes = {
  month: number;
  year: number;
};

export const getTodaysMonthYear = (selectedDate: string): MonthYearTypes => {
  let currentDate = new Date(getCurrentTimeWithOffset());
  if (!_isNil(selectedDate)) {
    currentDate = parse(selectedDate, 'yyyy-MM-dd', new Date());
  }
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  return { month: currentMonth, year: currentYear };
};

const StreakCalendar = () => {
  const todaysDate = format(new Date(getCurrentTimeWithOffset()), 'yyyy-MM-dd');

  const [selectedDate, setSelectedDate] = useState(todaysDate);
  const [selectedMonthYear, setSelectedMonthYear] = useState<MonthYearTypes>(
    getTodaysMonthYear(selectedDate),
  );

  const formattedMonthYear = `${selectedMonthYear.year}-${selectedMonthYear.month < 9 ? '0' : ''}${selectedMonthYear.month + 1}`;

  const { streakHistory, loading } =
    useGetUserStreakHistory(formattedMonthYear);

  const onDateSelected = useCallback(
    ({ date }) => {
      setSelectedDate(date);
    },
    [setSelectedDate],
  );

  const renderCalendarDate = useCallback(
    ({ item }) => {
      const formattedDateString = item?.dateString;
      const streakEntry = _find(
        streakHistory,
        (dateItem) => dateItem?.date.slice(0, 10) === formattedDateString,
      );
      const isCompleted = streakEntry;
      const isShieldUsed = isCompleted && streakEntry.isShieldUsed;

      return (
        <StreakCalendarDate
          item={item}
          onClick={onDateSelected}
          isCompleted={isCompleted}
          isShieldUsed={isShieldUsed}
          loading={loading}
        />
      );
    },
    [streakHistory, onDateSelected, loading],
  );

  return (
    <View style={styles.calendarContainer}>
      <CalendarMonthSelector
        selectedMonthYear={selectedMonthYear}
        setSelectedMonthYear={setSelectedMonthYear}
      />
      <CalendarView
        year={selectedMonthYear.year}
        month={selectedMonthYear.month}
        renderCalendarDate={renderCalendarDate}
        onDatePress={({ dateString }) => setSelectedDate(dateString)}
      />
    </View>
  );
};

export default React.memo(StreakCalendar);
