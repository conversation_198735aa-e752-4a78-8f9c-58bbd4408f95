import React from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { format } from 'date-fns';
import ShimmerView from 'molecules/ShimmerView';
import _compact from 'lodash/compact';
import shieldIcon from '@/assets/images/icons/shield_icon.png';
import styles, { DATE_DIMENSION } from './StreakCalendarDate.styte';

const StreakCalendarDate = ({
  item,
  onClick,
  isCompleted = false,
  isShieldUsed = false,
  loading = false,
}) => {
  const currentDate = new Date(getCurrentTime());
  const today = format(currentDate, 'yyyy-MM-dd');

  const isToday = item?.dateString === today;

  const isFutureDate = item?.dateString > today;

  const renderLoadingDate = () => <ShimmerView style={styles.dateBorder} />;

  const renderDateContent = () => (
    <View
      style={_compact([
        styles.dateBorder,
        isToday && styles.selectedBackground,
        isCompleted && styles.crownContainer,
        isShieldUsed && styles.shieldContainer,
        { borderRadius: 9 },
      ])}
    >
      {isCompleted ? (
        <View
          style={{
            height: DATE_DIMENSION - 2,
            width: DATE_DIMENSION - 2,
            borderRadius: 9,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <View style={styles.completedIconContainer}>
            {isShieldUsed ? (
              <Image source={shieldIcon} style={styles.shieldIconStyle} />
            ) : (
              <Text style={[styles.dateText, isToday && styles.todayText]}>
                {item?.day}
              </Text>
            )}
          </View>
        </View>
      ) : (
        <Text
          style={[
            styles.dateText,
            isToday && styles.todayText,
            isFutureDate && styles.futureText,
          ]}
        >
          {item?.day}
        </Text>
      )}
    </View>
  );

  if (item?.isEmpty) {
    return <View key={item?.dateString} style={styles.emptyDateBox} />;
  }

  return (
    <TouchableOpacity
      key={item.dateString}
      style={styles.dateCell}
      disabled={loading}
    >
      {loading ? renderLoadingDate() : renderDateContent()}
    </TouchableOpacity>
  );
};

export default React.memo(StreakCalendarDate);
