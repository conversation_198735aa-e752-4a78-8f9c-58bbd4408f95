import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

const CALENDAR_WIDTH = 300;
const DAY_WIDTH = CALENDAR_WIDTH / 7;

export const DATE_DIMENSION = DAY_WIDTH - 7;

const styles = StyleSheet.create({
  todayText: {
    color: dark.colors.textLight,
    fontFamily: 'Montserrat-600',
  },
  futureText: {
    color: withOpacity('#FFFFFF', 0.2),
  },
  dateCell: {
    width: DAY_WIDTH,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateBorder: {
    width: DAY_WIDTH - 8,
    height: DAY_WIDTH - 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 9,
  },
  todayBackground: {
    backgroundColor: dark.colors.streak,
  },
  selectedBackground: {
    borderWidth: 1,
    borderColor: dark.colors.streak,
    borderRadius: 9,
  },
  dateText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
    alignSelf: 'center',
  },
  emptyDateBox: {
    width: DAY_WIDTH,
  },
  crownContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 9,
    backgroundColor: withOpacity(dark.colors.streak, 1)
  },
  shieldContainer:{
    backgroundColor: 'transparent',
  },
  crownText: {
    fontSize: 15,
  },
  completedIconContainer: {
   zIndex: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  shieldIconStyle: {
    width: 20, 
    height: 20, 
    resizeMode: 'contain',
    zIndex: 20
  },
});

export default styles;
