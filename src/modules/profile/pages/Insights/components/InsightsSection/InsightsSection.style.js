import { useMemo } from 'react'
import { StyleSheet } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'
import dark from '../../../../../../core/constants/themes/dark'

const createStyles = (isCompactMode) =>
    StyleSheet.create({
        container: {
            backgroundColor: isCompactMode
                ? 'transparent'
                : dark.colors.gradientBackground,
            borderRadius: 12,
            paddingHorizontal: isCompactMode ? 0 : 20,
            paddingVertical: isCompactMode ? 0 : 15,
            gap: isCompactMode ? 0 : 5,
        },
        displayCardsContainer: {
            width: isCompactMode ? '48%' : 160,
            maxWidth: isCompactMode ? 'auto' : 180,
            borderColor: dark.colors.tertiary,
            borderWidth: 1,
            paddingVertical: 8,
            paddingHorizontal: 12,
            borderRadius: 12,
        },
        headerText: {
            fontFamily: 'Montserrat-600',
            fontSize: isCompactMode ? 10 : 14,
            lineHeight: 17,
            color: dark.colors.textDark,
        },
        headerContainer: {
            justifyContent: 'space-between',
            flexDirection: 'row',
        },
        tabItemCard: {
            backgroundColor: 'transparent',
            borderRadius: 16,
            paddingHorizontal: 22,
            paddingVertical: 6,
            justifyContent: 'center',
            alignItems: 'center',
        },
        tabItemText: {
            fontFamily: 'Montserrat-500',
            fontSize: 11,
            lineHeight: 20,
            textAlign: 'center',
            color: dark.colors.textDark,
        },
        noDataText: {
            fontFamily: 'Montserrat-500',
            fontSize: 11,
            lineHeight: 20,
            textAlign: 'center',
            color: dark.colors.textDark,
        },
        tabContainer: {
            marginVertical: 10,
        },
    })

const useExpandedInsightsCardStyles = () => {
    const { isMobile } = useMediaQuery()
    const styles = useMemo(() => createStyles(isMobile), [isMobile])
    return styles
}

export default useExpandedInsightsCardStyles
