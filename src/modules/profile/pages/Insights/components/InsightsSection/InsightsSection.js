import React, { useCallback, useMemo, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import _map from 'lodash/map';
import _slice from 'lodash/slice';
import AntDesign from '@expo/vector-icons/AntDesign';
import { useRouter } from 'expo-router';
import PropTypes from 'prop-types';
import _round from 'lodash/round';
import _isEmpty from 'lodash/isEmpty';
import ShimmerView from 'molecules/ShimmerView';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGetUsersAllPlayedPresets from '../../../../hooks/query/useGetUserAllPlayedPresets';
import { getDescriptiveNameFromIdentifier } from '../../../../../practice/utils/getIdentifierStringFromConfig';
import dark from '../../../../../../core/constants/themes/dark';
import INSIGHTS_TAB_CATEGORIES from '../../constants/categories';
import useExpandedInsightsCardStyles from './InsightsSection.style';
import CardWithLabelAndValue from '../../../../../practice/components/CardWithLabelAndValue';

const InsightsSection = (props) => {
  const { user, isCurrentUser, insightsData } = props;
  const router = useRouter();
  const { isMobile: isCompactMode } = useMediaQuery();
  const styles = useExpandedInsightsCardStyles();

  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectedCategory = useMemo(
    () => INSIGHTS_TAB_CATEGORIES[selectedIndex].key,
    [selectedIndex],
  );

  const navigateToInsightsPage = useCallback(() => {
    router.push(`/profile/${user?.username}/insights`);
  }, [router, user?.username]);

  const filteredData = useMemo(() => {
    const data = insightsData[selectedCategory] || [];
    return _slice(data, 0, isCompactMode ? 4 : 8);
  }, [selectedCategory, insightsData, isCompactMode]);

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.headerText}>INSIGHTS</Text>
        <AntDesign
          name="arrowright"
          size={20}
          color={dark.colors.secondary}
          onPress={navigateToInsightsPage}
        />
      </View>
      <ScrollView
        contentContainerStyle={styles.tabContainer}
        horizontal
        showsHorizontalScrollIndicator={false}
      >
        {_map(INSIGHTS_TAB_CATEGORIES, (tabItem, index) => (
          <TouchableOpacity
            key={tabItem.key}
            style={[
              styles.tabItemCard,
              selectedIndex === index && {
                
                backgroundColor: dark.colors.primary,
              },
            ]}
            onPress={() => setSelectedIndex(index)}
          >
            <Text
              style={[
                styles.tabItemText,
                selectedIndex === index && {
                  color: dark.colors.secondary,
                  fontFamily: 'Montserrat-600',
                },
              ]}
            >
              {tabItem.title}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <View
        style={{
          flexDirection: 'row',
          gap: 10,
          flexWrap: 'wrap',
          flexGrow: 1,
        }}
      >
        {_isEmpty(filteredData) && (
          <Text style={styles.noDataText}>No Data Available</Text>
        )}
        {_map(filteredData, (item, index) => (
          <CardWithLabelAndValue
            key={`${index}`}
            label={getDescriptiveNameFromIdentifier({
              identifier: item?.identifier,
            })}
            value={`${_round(item?.avgTime / 1000, 2)} Sec`}
            containerStyle={styles.displayCardsContainer}
          />
        ))}
      </View>
    </View>
  );
};

InsightsSection.propTypes = {
  isCurrentUser: PropTypes.bool,
  user: PropTypes.object,
  insightsData: PropTypes.object.isRequired,
};

const InsightsSectionContainer = (props) => {
  const { user, isCurrentUser } = props;

  const { playedPresets, loading, error } = useGetUsersAllPlayedPresets({
    username: user?.username,
  });

  if (loading) {
    return (
      <ShimmerView
        shimmerColors={dark.colors.placeholderShimmerColors}
        style={{
          height: 150,
          borderColor: dark.colors.tertiary,
          borderWidth: 1,
          borderRadius: 12,
          width: '100%',
          marginTop: 15,
        }}
      />
    );
  }

  if (_isEmpty(playedPresets) || error) {
    return null;
  }

  return (
    <InsightsSection
      isCurrentUser={isCurrentUser}
      user={user}
      insightsData={playedPresets}
    />
  );
};

InsightsSectionContainer.propTypes = {
  isCurrentUser: PropTypes.bool,
  user: PropTypes.object,
};

export default React.memo(InsightsSectionContainer);
