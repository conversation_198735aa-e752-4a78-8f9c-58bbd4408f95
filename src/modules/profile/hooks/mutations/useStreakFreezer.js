import { gql, useMutation } from '@apollo/client';
import _get from 'lodash/get';
import { useCallback } from 'react';

const USE_STREAK_FREEZER = gql`
  mutation UseStreakFreezer {
    useStreakFreezer
  }
`;

const useStreakFreezer = () => {
  const [streakFreezerQuery, { data, loading, error }] =
    useMutation(USE_STREAK_FREEZER);

  const applyStreakFreezer = useCallback(async () => {
    const response = await streakFreezerQuery();
    return _get(response, ['data', 'useStreakFreezer'], EMPTY_OBJECT);
  }, [streakFreezerQuery]);

  return {
    applyStreakFreezer,
    loading,
    error,
    success: _get(data, 'useStreakFreezer', EMPTY_OBJECT),
  };
};

export default useStreakFreezer;
