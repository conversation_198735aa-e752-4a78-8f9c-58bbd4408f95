import { useCallback } from 'react';
import { gql, useQuery } from '@apollo/client';
import _get from 'lodash/get';
import { useSession } from 'modules/auth/containers/AuthProvider';

const GET_WEEKLY_COINS_EARNED_QUERY = gql`
  query GetUsersWeeklyStatikCoinsV2($userId: ID!) {
    getUsersWeeklyStatikCoinsV2(userId: $userId) {
      totalCoins
      dailyCoins
    }
  }
`;

const useGetWeeklyCoinsEarned = (userId?: string) => {
  const { userId: currSessionUserId } = useSession();
  const {
    loading,
    data: currWeekCoinsData,
    error,
    client,
  } = useQuery(GET_WEEKLY_COINS_EARNED_QUERY, {
    fetchPolicy: 'cache-first',
    notifyOnNetworkStatusChange: true,
    variables: { userId: userId ?? currSessionUserId },
  });

  const updateWeeklyCoinsEarnedCache = useCallback(
    ({ increaseInCoin }: { increaseInCoin?: number }) => {
      client.cache.updateQuery(
        {
          query: GET_WEEKLY_COINS_EARNED_QUERY,
          broadcast: true,
          overwrite: true,
          variables: { userId: userId ?? currSessionUserId },
        },
        (data: any) => {
          const responseData = _get(
            data,
            'getUsersWeeklyStatikCoinsV2',
            EMPTY_OBJECT,
          );

          const totalCoins = _get(responseData, 'totalCoins', 0);

          return {
            ...data,
            getUsersWeeklyStatikCoinsV2: {
              ...responseData,
              totalCoins: totalCoins + (increaseInCoin || 0),
            },
          };
        },
      );
    },
    [client.cache, userId, currSessionUserId],
  );

  return {
    coinsEarned: _get(
      currWeekCoinsData,
      ['getUsersWeeklyStatikCoinsV2', 'totalCoins'],
      0,
    ),
    dailyCoins: _get(
      currWeekCoinsData,
      ['getUsersWeeklyStatikCoinsV2', 'dailyCoins'],
      [],
    ),
    updateWeeklyCoinsEarnedCache,
    loading,
    error,
  };
};

export default useGetWeeklyCoinsEarned;
