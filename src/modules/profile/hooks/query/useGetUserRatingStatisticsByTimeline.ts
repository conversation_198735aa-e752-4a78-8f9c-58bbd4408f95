import { gql, useLazyQuery } from '@apollo/client';
import { useCallback, useMemo } from 'react';
import _get from 'lodash/get';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';

const GET_USER_STATISTICS_BY_TIMELINE = gql`
  query GetUserStatisticsByTimeline(
    $timePeriod: TimePeriod!
    $username: String!
  ) {
    getUserStatisticsByTimeline(
      input: { timePeriod: $timePeriod, userName: $username }
    ) {
      userID
      timePeriod
      overallStats {
        gamesPlayed
        xpGained
        peakRating
        wins
        losses
        type
      }
      ratingDetails {
        type
        currentRating
        ratingChange
      }
      ratingGraphData {
        type
        points {
          date
          rating
        }
      }
    }
  }
`;

const useGetUserRatingStatisticsByTimeline = () => {
  const [fetchStatistics, { loading, error, data }] = useLazyQuery(
    GET_USER_STATISTICS_BY_TIMELINE,
    {
      fetchPolicy: 'network-only',
    },
  );

  const loadStatistics = useCallback(
    async (timePeriod?: string, username?: string) => {
      if (_isEmpty(timePeriod)) {
        return null;
      }
      try {
        const variables = { timePeriod, username };
        const result = await fetchStatistics({
          variables,
        });
        return _get(result, 'data.getUserStatisticsByTimeline', null);
      } catch (err) {
        throw err;
      }
    },
    [fetchStatistics],
  );

  const ratingTypeDetails = useMemo(() => {
    return _get(data, 'getUserStatisticsByTimeline.ratingDetails', []);
  }, [data]);

  const ratingOverallStats = useCallback(
    (ratingType?: string) => {
      const overallStatsData = _get(
        data,
        'getUserStatisticsByTimeline.overallStats',
        [],
      );
      if (_isEmpty(overallStatsData) || _isEmpty(ratingType)) {
        return [];
      }
      return _find(overallStatsData, { type: ratingType });
    },
    [data],
  );

  const getGraphPointsForType = useCallback(
    (ratingType?: string) => {
      const ratingGraphData = _get(
        data,
        'getUserStatisticsByTimeline.ratingGraphData',
        [],
      );
      if (_isEmpty(ratingGraphData) || _isEmpty(ratingType)) {
        return [];
      }
      const graphDataForType = _find(ratingGraphData, { type: ratingType });
      return _get(graphDataForType, 'points', []);
    },
    [data],
  );

  return {
    loadStatistics,
    loading,
    error,
    ratingOverallStats,
    ratingTypeDetails,
    getGraphPointsForType,
  };
};

export default useGetUserRatingStatisticsByTimeline;
