import { useCallback, useEffect, useState } from 'react';
import _get from 'lodash/get';
import useGetUserGamesByRatingType from './useGetUserGamesByRatingType';
import processGamesByRatingType from 'modules/profile/hooks/useProcessGamesByRatingType';

const useUserLastPlayedGames = ({ userId, ratingType, pageSize = 5 }) => {
  const [processedGames, setProcessedGames] = useState([]);
  const {
    loading,
    error,
    rawData,
    fetchGames: executeFetch,
    refetch,
  } = useGetUserGamesByRatingType({
    userId,
    ratingType,
    pageSize,
  });

  const fetchTopGames = useCallback(async () => {
    const result = await executeFetch({ pageNumber: 1, pageSize: pageSize });
    const fetchedRawData = _get(result, 'data.getUserGamesByRatingType');

    if (fetchedRawData) {
      const games = processGamesByRatingType(
        fetchedRawData,
        userId,
        ratingType,
      );
      setProcessedGames(games);
    } else {
      setProcessedGames([]);
    }
  }, [executeFetch, pageSize, ratingType, userId]);

  useEffect(() => {
    if (userId && ratingType) {
      fetchTopGames();
    } else {
      setProcessedGames([]);
    }
  }, [userId, ratingType, fetchTopGames]);

  return {
    loading,
    error,
    games: processedGames,
    fetchGames: fetchTopGames,
  };
};

export default useUserLastPlayedGames;
