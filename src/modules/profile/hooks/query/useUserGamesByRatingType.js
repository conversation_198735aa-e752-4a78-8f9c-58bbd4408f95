import { useCallback } from 'react';
import _get from 'lodash/get';
import useGetUserGamesByRatingType from './useGetUserGamesByRatingType';
import processGamesByRatingType from 'modules/profile/hooks/useProcessGamesByRatingType';

const useUserGamesByRatingType = ({ userId, ratingType, pageSize = 50 }) => {
  const { fetchGames: executeFetch, refetch } = useGetUserGamesByRatingType({
    userId,
    ratingType,
    pageSize,
  });

  const fetchPageData = useCallback(
    async ({ pageNumber }) => {
      try {
        const result = await executeFetch({ pageNumber: pageNumber });
        const fetchedRawData = _get(result, 'data.getUserGamesByRatingType');

        if (fetchedRawData) {
          const processedPageData = processGamesByRatingType(
            fetchedRawData,
            userId,
            ratingType,
          );
          return {
            data: processedPageData,
            totalItems: _get(fetchedRawData, 'totalCount', 0),
          };
        } else {
          return { data: [], totalItems: 0 };
        }
      } catch (error) {
        return { data: [], totalItems: 0 };
      }
    },
    [executeFetch, userId, ratingType],
  );

  return {
    fetchPageData,
  };
};

export default useUserGamesByRatingType;
