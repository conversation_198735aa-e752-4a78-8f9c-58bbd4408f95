import { useCallback, useState } from 'react';
import _get from 'lodash/get';
import useGetUserStreakShieldTransactions from './query/useGetStreakShieldTransactionsQuery';

interface UseStreakShieldTransactionsProps {
  initialPageSize?: number;
}

const useStreakShieldTransactions = ({
  initialPageSize = 10,
}: UseStreakShieldTransactionsProps = {}) => {
  const [currentPage, setCurrentPage] = useState(0);

  const {
    transactions,
    pageInfo,
    loading: isLoading,
    error,
    fetchTransactions: fetchTransactionsQuery,
    refetch,
  } = useGetUserStreakShieldTransactions({
    page: currentPage,
    pageSize: initialPageSize,
  });

  const fetchTransactions = useCallback(
    async ({ pageNumber }) => {
      try {
        const nextPage = pageNumber;
        const response = await fetchTransactionsQuery(nextPage);
        const data = _get(response, [
          'data',
          'getUserStreakShieldTransactions',
        ]);
        setCurrentPage(nextPage);
        return {
          data: data?.results,
          totalItems: data?.totalResults,
          itemsList: data?.results,
        };
      } catch (err) {
        console.error('Error loading more transactions:', err);
      }
    },
    [fetchTransactionsQuery],
  );

  const handleRefresh = useCallback(async () => {
    try {
      setCurrentPage(1);
      await refetch();
    } catch (err) {
      console.error('Error refreshing transactions:', err);
    }
  }, [refetch]);

  const resetPagination = useCallback(() => {
    setCurrentPage(1);
  }, []);

  return {
    transactions,
    pageInfo,
    isLoading,
    error,
    refresh: handleRefresh,
    resetPagination,
    currentPage,
    fetchTransactions,
  };
};

export default useStreakShieldTransactions;
