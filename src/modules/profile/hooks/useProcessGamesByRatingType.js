import _map from 'lodash/map';
import _find from 'lodash/find';
import _compact from 'lodash/compact';
import _keyBy from 'lodash/keyBy';
import _keys from 'lodash/keys';
import _sortBy from 'lodash/sortBy';
import _reverse from 'lodash/reverse';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import { GAME_RESULT_TYPES } from 'modules/home/<USER>/gameTypes';

const processGamesByRatingType = (rawData, userId, ratingType) => {
  if (
    _isNil(rawData) ||
    (_isNil(rawData.games) && _isNil(rawData.puzzleGames)) ||
    _isNil(rawData.users)
  ) {
    return [];
  }
  const usersMap = _keyBy(rawData.users, '_id');
  const gameList =
    ratingType === 'PUZZLE' ? rawData.puzzleGames : rawData.games;
  if (!gameList) return [];
  const sortedGames = _reverse(_sortBy(gameList, 'startTime'));
  return _compact(
    _map(sortedGames, (game) => {
      const playersMap = _keyBy(game?.players, 'userId');
      const leaderboardMap = _keyBy(game?.leaderBoard, 'userId');
      const opponentUserId = _find(_keys(playersMap), (key) => key !== userId);
      const currentUserBase = usersMap[userId] || EMPTY_OBJECT;
      const opponentUserBase = usersMap[opponentUserId] || EMPTY_OBJECT;
      if (
        _isEmpty(currentUserBase) ||
        (opponentUserId && _isEmpty(opponentUserBase))
      ) {
        return null;
      }
      const currentPlayer = {
        ...currentUserBase,
        ...(playersMap[userId] || EMPTY_OBJECT),
        ...(leaderboardMap[userId] || EMPTY_OBJECT),
      };
      const opponentPlayer = opponentUserId
        ? {
            ...opponentUserBase,
            ...(playersMap[opponentUserId] || EMPTY_OBJECT),
            ...(leaderboardMap[opponentUserId] || EMPTY_OBJECT),
          }
        : null;
      let gameResult = GAME_RESULT_TYPES.DRAW;
      const currentPlayerRank = _get(leaderboardMap, [userId, 'rank']);
      const opponentPlayerRank = opponentUserId
        ? _get(leaderboardMap, [opponentUserId, 'rank'])
        : null;
      if (currentPlayerRank === 1) gameResult = GAME_RESULT_TYPES.WIN;
      else if (opponentPlayerRank === 1) gameResult = GAME_RESULT_TYPES.LOSE;

      if (
        _isEmpty(currentPlayer) ||
        (opponentUserId && _isEmpty(opponentPlayer))
      ) {
        return null;
      }
      return { ...game, currentPlayer, opponentPlayer, gameResult };
    }),
  );
};

export default processGamesByRatingType;
