import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';
import { useMemo } from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    contentContainer: {
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      letterSpacing: 1,
      marginBottom: 30,
      color: dark.colors.textLight,
    },
    mainContainerStyle: {
      marginTop: 20,
    },
    innerContainerStyle: {
      flexDirection: 'column',
    },
    textInputStyle: {
      width: 170,
      height: 64,
      backgroundColor: dark.colors.primary,
      borderRadius: 8,
      borderWidth: 2,
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0,
      borderRightColor: 'transparent',
      borderLeftColor: 'transparent',
      borderTopColor: 'transparent',
      borderBottomColor: dark.colors.red,
      fontFamily: 'Montserrat-500',
      color: dark.colors.textDark,
      textAlign: 'center',
      letterSpacing: 8,
      zIndex: 1,
    },
    footer: {
      position: 'absolute',
      bottom: isCompactMode ? 0 : 50,
      left: 0,
      right: 0,
      width: '100%',
      gap: 25,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'column',
      paddingHorizontal: 16,
    },
    continueButton: {
      borderWidth: 0.8,
      borderColor: dark.colors.secondary,
      borderRadius: 10,
      height: 50,
    },
    continueButtonContainer: {
      flex: 1,
      width: '100%',
      minWidth: 200,
      justifyContent: 'center',
      alignItems: 'center',
      maxWidth: 400,
      height: 50,
      borderRadius: 12,
    },
    continueButtonText: {
      fontSize: 12,
      fontFamily: 'Montserrat-800',
      lineHeight: 17,
      color: dark.colors.textLight,
      letterSpacing: 2,
    },
    continueBackground: {
      flex: 1,
      backgroundColor: dark.colors.victoryColor,
    },
    noReferralCode: {
      fontFamily: 'Montserrat-600',
      fontSize: 12,
      color: dark.colors.textLight,
      opacity: 0.4,
      marginBottom: 20,
      letterSpacing: 1,
    },
    error: {
      alignSelf: 'center',
      color: dark.colors.red,
      marginTop: 24,
      fontSize: 10,
      fontFamily: 'Montserrat-700',
      letterSpacing: 1,
    },
    image: {
      position: 'absolute',
      marginTop: 100,
    },
  });

export const useReferralCodePageStyles = () => {
  const { isMobile } = useMediaQuery();
  const styles = useMemo(() => createStyles(isMobile), [isMobile]);
  return styles;
};
