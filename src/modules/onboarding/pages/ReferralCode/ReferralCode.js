import React, { useCallback, useEffect, useState } from 'react';
import { Image, Pressable, Text, TextInput, View } from 'react-native';
import BackgroundImage from '@/assets/images/backgrounds/background_blocks.png';
import InteractivePrimaryButton from '@/src/components/atoms/InteractivePrimaryButton';
import { useRouter } from 'expo-router';
import { useReadReferralCode } from 'core/hooks/useReadReferralCode';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from '@/src/core/analytics';
import { EVENTS } from '@/src/core/analytics/events/profile';
import { useSession } from 'modules/auth/containers/AuthProvider';
import _size from 'lodash/size';
import useSubmitReferralCode from '../../hooks/useSubmitReferralCode';
import { useReferralCodePageStyles } from './ReferralCode.style';

const ReferralCode = () => {
  const { refreshCurrentUser } = useSession();
  const [referralCode, setReferralCode] = useState(null);
  const styles = useReferralCodePageStyles();
  const { submitReferralCode, error, loading } = useSubmitReferralCode();
  const router = useRouter();
  const { referralCode: code } = useReadReferralCode();

  useEffect(() => {
    if (code) {
      setReferralCode(code);
    }
  }, [code]);

  const onNoReferralCode = useCallback(() => {
    router.replace('/home');
  }, [router]);

  const handleContinue = useCallback(async () => {
    if(loading){
      return
    }
    if (!referralCode || referralCode.trim() === '') {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: 'Referral code cannot be empty.',
      });
      return;
    }
    const success = await submitReferralCode(referralCode);
    refreshCurrentUser();
    if (success && !loading) {
      Analytics.track(EVENTS.PROFILE.REFERRAL_CODE_REDEMPTION_SUCCESS, {
        referralCode,
      });
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: 'Referral code redeemed successfully',
      });
      router.replace('/home');
    }
  }, [referralCode, submitReferralCode, loading, router, refreshCurrentUser]);

  const isInActiveButton = _size(referralCode) <= 0;

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <Image source={BackgroundImage} style={styles.image} />
        <Text style={styles.title}>DO YOU HAVE A REFERRAL CODE ?</Text>
        <TextInput
          style={styles.textInputStyle}
          value={referralCode}
          onChangeText={setReferralCode}
          error={null}
        />
        {error && <Text style={styles.error}>INVALID REFERRAL CODE</Text>}
      </View>
      <View style={[styles.footer, isInActiveButton && { paddingBottom: 20 }]}>
        <InteractivePrimaryButton
          onPress={isInActiveButton ? onNoReferralCode : handleContinue}
          label={isInActiveButton ? 'SKIP' : 'CONTINUE'}
          buttonStyle={[styles.continueButton]}
          buttonContainerStyle={styles.continueButtonContainer}
          labelStyle={styles.continueButtonText}
          buttonBorderBackgroundStyle={styles.continueBackground}
        />
        {!isInActiveButton && (
          <Pressable onPress={onNoReferralCode}>
            <Text style={styles.noReferralCode}>SKIP THIS STEP</Text>
          </Pressable>
        )}
      </View>
    </View>
  );
};

export default ReferralCode;
