import React, { useCallback, useEffect, useState } from 'react';
import { Platform, Text, TouchableOpacity, View } from 'react-native';

import StoryViewer from '@/src/components/shared/StoryViewer';
import GoogleLoginButton from 'core/oauth/components/GoogleLoginButton';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import _size from 'lodash/size';
import { FEATURE_PREVIEWS_LIST } from 'modules/onboarding/constants/featurePreviewsList';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAMES } from 'core/constants/pageNames';
import { router } from 'expo-router';
import AppleLogin from 'core/oauth/components/AppleLogin';
import styles from './FeaturesStoryView.style';

const SPLASH_RIVE_ANIMATION_DURATION = 2700;

const FeaturesStoryView = () => {
  const [showStories, setShowStories] = useState(false);
  const [hasReachedEnd, setHasReachedEnd] = useState(false);

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.ONBOARDING.VIEWED_FEATURES_PREVIEW_PAGE, {
      pageName: PAGE_NAMES.ONBOARDING_FEATURES_PREVIEWS,
    });
    const timer = setTimeout(() => {
      setShowStories(true);
    }, SPLASH_RIVE_ANIMATION_DURATION);

    return () => clearTimeout(timer);
  }, []);

  const onIndexChange = useCallback(
    (index: number) => {
      if (index === _size(FEATURE_PREVIEWS_LIST) - 1 && !hasReachedEnd) {
        Analytics.track(
          ANALYTICS_EVENTS.ONBOARDING.REACHED_END_OF_FEATURE_STORIES,
          {
            pageName: PAGE_NAMES.ONBOARDING_FEATURES_PREVIEWS,
          },
        );
        setHasReachedEnd(true);
      }
    },
    [hasReachedEnd],
  );

  const { createGuestUser } = useSession();

  const loginAsGuest = useCallback(async () => {
    showToast({ type: TOAST_TYPE.LOADING, description: 'Logging in...' });
    Analytics.track(ANALYTICS_EVENTS.ONBOARDING.CLICKED_ON_GUEST_LOGIN, {
      pageName: PAGE_NAMES.ONBOARDING_FEATURES_PREVIEWS,
    });
    createGuestUser()
      .then(() => {
        hideToast();
        router.replace('/home');
      })
      .catch(() => {});
  }, [createGuestUser]);

  const trackSkipPress = useCallback((currentIndex: number) => {
    Analytics.track(ANALYTICS_EVENTS.ONBOARDING.SKIPPED_FEATURE_STORIES, {
      pageName: PAGE_NAMES.ONBOARDING_FEATURES_PREVIEWS,
      skippedAtIndex: currentIndex,
    });
  }, []);

  const renderCustomFooter = (
    currentIndex: number,
    internalHandleSkip: () => void,
  ) => {
    const onSkipPress = () => {
      trackSkipPress(currentIndex);
      internalHandleSkip();
    };

    if (hasReachedEnd || _size(FEATURE_PREVIEWS_LIST) === currentIndex + 1) {
      return (
        <View style={[styles.footerContainer]}>
          <TouchableOpacity onPress={loginAsGuest}>
            <Text style={styles.enterAsGuestButtonText}>ENTER AS GUEST</Text>
          </TouchableOpacity>
          <GoogleLoginButton />
          {Platform.OS === 'ios' && (
            <View style={{ marginTop: 16 }}>
              <AppleLogin />
            </View>
          )}
        </View>
      );
    }
    return (
      <TouchableOpacity style={styles.footerContainer} onPress={onSkipPress}>
        <Text style={styles.enterAsGuestButtonText}> SKIP </Text>
      </TouchableOpacity>
    );
  };

  if (!showStories) {
    return (
      <View style={styles.container}>
        <View style={styles.riveContainer}>
          <Rive
            url={RIVE_ANIMATIONS.FEATURES_PREVIEWS_ANIMATIONS.SPLASH_ANIMATION}
            autoPlay
            style={styles.riveAnimation}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StoryViewer
        stories={FEATURE_PREVIEWS_LIST}
        onIndexChange={onIndexChange}
        renderFooter={renderCustomFooter}
      />
    </View>
  );
};

export default React.memo(FeaturesStoryView);
