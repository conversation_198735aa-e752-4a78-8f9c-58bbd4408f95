import _size from 'lodash/size'
import _isEmpty from 'lodash/isEmpty'
import useContestLeaderboardQuery from './queries/useContestLeaderboardQuery'

const useContestLeaderBoard = ({ contest, pageSize }) => {
    const { loading, error, participants, totalParticipants, fetchMore } =
        useContestLeaderboardQuery({ contest, pageSize })

    return {
        loading,
        error,
        fetchMore,
        loadingMore: loading && !_isEmpty(participants),
        hasMore: _size(participants) < totalParticipants,
        participants,
        totalParticipants,
    }
}

export default useContestLeaderBoard
