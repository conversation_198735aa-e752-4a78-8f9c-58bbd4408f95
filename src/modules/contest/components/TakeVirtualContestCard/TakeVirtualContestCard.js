import React, { useCallback, useState } from "react"
import { View } from "react-native"
import { showToast, TOAST_TYPE } from 'molecules/Toast'
import CardWithCTA from "../../../../components/shared/CardWithCTA/CardWithCTA"
import contestBadge from 'assets/images/contest_badge.png'
import PropTypes from "prop-types"
import useMediaQuery from "core/hooks/useMediaQuery"
import useJoinVirtualContest from "../../hooks/mutations/useJoinVirtualContest"
import { useRouter } from "expo-router"
import Analytics from "core/analytics"
import { ANALYTICS_EVENTS } from "core/analytics/const"

const TakeVirtualContestCard = (props) => {
    const { contestId } = props
    const router = useRouter()
    const { isMobile: isCompactMode } = useMediaQuery()

    const [isRegisteringForVirtualContest, setIsRegisteringForVirtualContest] = useState(false)

    const { joinVirtualContest } = useJoinVirtualContest()

    const onPressTakeVirtualContest = useCallback(async () => {
        if (isRegisteringForVirtualContest) {
            return
        }

        try {
            setIsRegisteringForVirtualContest(true)
            const isJoinedSuccessfully = await joinVirtualContest({ contestId })
            if (isJoinedSuccessfully) {
                Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_TAKE_VIRTUAL_CONTEST)
                showToast({
                    type: TOAST_TYPE.SUCCESS,
                    description: 'Successfully Joined Virtual Contest',
                })
                router.push(`/contest/virtual/${contestId}`)
                return
            }
            showToast({
                type: TOAST_TYPE.ERROR,
                description: 'Something went wrong',
            })
        } catch (e) {
            setIsRegisteringForVirtualContest(false)
        } finally {
            setIsRegisteringForVirtualContest(false)
        }
    }, [isRegisteringForVirtualContest, contestId])

    return (
        <View style={{ marginHorizontal: isCompactMode ? 16 : 0, marginVertical: isCompactMode ? 20 : 0 }}>
            <CardWithCTA
                testId="take-virtual-contest-card"
                buttonText={isRegisteringForVirtualContest ? "Registering..." : 'Take Virtual Contest'}
                imageSource={contestBadge}
                onButtonPress={onPressTakeVirtualContest}
                titleText={"Take the virtual contest now and see where your rank would stand!"} />
        </View>
    )
}

TakeVirtualContestCard.propTypes = {
    contestId: PropTypes.string
}

export default React.memo(TakeVirtualContestCard)