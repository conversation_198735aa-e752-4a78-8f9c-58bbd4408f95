import React, { useCallback } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import { useRouter } from 'expo-router';
import LeaderboardItem from './LeaderboardItem';
import useContestLeaderBoard from '../../hooks/useContestLeaderboard';
import Loading from '../../../../components/atoms/Loading';
import styles from './CompactLeaderboard.style';
import ErrorView from '../../../../components/atoms/ErrorView/ErrorView';
import CompactUserScoreCard from './CompactUserScoreCard';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';
import { getContestPropertiesToTrack } from '../../utils/contestEvents';
import {
  PAGE_NAME_KEY,
  PAGE_NAMES,
} from '../../../../core/constants/pageNames';
import userReader from '../../../../core/readers/userReader';
import TakeVirtualContestCard from '../TakeVirtualContestCard';
import contestReader from '../../readers/contestReader';

const CompactLeaderboard = ({ contest }) => {
  const router = useRouter();
  const pageSize = 3;
  const { loading, error, hasMore, participants } = useContestLeaderBoard({
    contest,
    pageSize,
  });

  const contestId = contestReader.id(contest);

  const onPressSeeAll = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.CONTEST.CLICKED_ON_CONTEST_DETAIL_PAGE_ALL_RESULT,
      {
        ...getContestPropertiesToTrack({ contest }),
        [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
      },
    );
    router.push(`/contest/leaderboard/${contest?._id}`);
  }, [router, contest]);

  if (loading) {
    return <Loading />;
  }

  if (error) {
    return <ErrorView errorMessage="Oops, Leaderboard is Empty" />;
  }

  if (_isEmpty(participants)) {
    return (
      <View style={{ alignItems: 'center', marginTop: 0, flex: 1 }}>
        <TakeVirtualContestCard contestId={contestId} />
        <View
          style={{
            flex: 1,
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text style={{ color: 'white' }}> Leaderboard is Empty</Text>
        </View>
      </View>
    );
  }
  return (
    <View style={{ flex: 1 }}>
      <FlatList
        data={participants}
        renderItem={({ item, index }) => (
          <View style={{ alignItems: 'center' }}>
            <LeaderboardItem
              name={userReader.username(item?.user)}
              score={item.score}
              position={index + 1}
            />
          </View>
        )}
        ListHeaderComponent={<CompactUserScoreCard contest={contest} />}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => `${item?._id}-${index}`}
        ListFooterComponent={
          hasMore && (
            <TouchableOpacity onPress={onPressSeeAll}>
              <Text style={styles.footerText}>See All</Text>
            </TouchableOpacity>
          )
        }
      />
    </View>
  );
};

export default CompactLeaderboard;
