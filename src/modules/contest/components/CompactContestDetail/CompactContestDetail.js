import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import React, { useCallback, useMemo, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import Header from 'shared/Header';
import { FontAwesome } from '@expo/vector-icons';
import DarkTheme from 'core/constants/themes/dark';
import WithClubAccess from '@/src/modules/clubs/components/WithClubAccess';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import styles from './CompactContestDetail.style';
import FormBottomSheet from './FormBottomSheet';
import CompactContestDetailsTabBarView from './CompactConetstDetailsTab';
import CompactContestDetailsShimmer from '../../shimmers/CompactContestShimmer';
import CountdownTimer from '../CountdownTimer';
import dark from '../../../../core/constants/themes/dark';
import useContestDetailsController from '../../hooks/useContestDetailsController';
import LinearGradient from '../../../../components/atoms/LinearGradient';
import { getContestPropertiesToTrack } from '../../utils/contestEvents';
import {
  PAGE_NAME_KEY,
  PAGE_NAMES,
} from '../../../../core/constants/pageNames';

const CompactContestDetails = React.memo(
  ({ contestDetails, loading, error, refetch }) => {
    const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
    const onContestRegistrationSuccess = useCallback(() => {
      setIsBottomSheetVisible(false);
      refetch?.();
    }, [setIsBottomSheetVisible, refetch]);

    const {
      onPressRegisterGuestUser,
      handleJoinOrSubmitButtonPress,
      handleFormSubmit,
      onPressUnRegister,
      isAboutToStart,
      isLive,
      isGuest,
      hasEnded,
      hasCompleted,
      hasUserRegistered,
      formFields,
      isSubmitting,
    } = useContestDetailsController({
      contest: contestDetails,
      onContestRegistrationFailure: () => {},
      onContestRegistrationSuccess,
      onRegisterRequest: () => {
        setIsBottomSheetVisible(true);
      },
      refetch,
    });

    const { user } = useSession();
    const canParticipateInTournament =
      userReader.canParticipateInTournaments(user);

    const shouldShowRegisterButton = !hasEnded && !hasUserRegistered;
    const contestIsAboutToStartOrLiveAndUserIsRegistered =
      (hasUserRegistered && isLive && !hasEnded) || (!isLive && isAboutToStart);
    const contestDidNotStartedYetAndNotEnded = !hasEnded && !isLive;
    const contestIsLiveAndNotEnded = !hasEnded && isLive;
    const canUserWithdrawRegistration =
      hasUserRegistered && !hasEnded && !isLive;
    const showLockedRegisterButtonForGuest =
      !canParticipateInTournament && !hasEnded;

    const renderJoinNowOrRegisterButton = useMemo(() => {
      if (hasCompleted || hasEnded) {
        return null;
      }

      if (showLockedRegisterButtonForGuest) {
        return (
          <TouchableOpacity
            style={[styles.fab, styles.lockedRegisterButton]}
            onPress={onPressRegisterGuestUser}
          >
            <FontAwesome name="lock" size={20} color={DarkTheme.colors.text} />
            <Text style={styles.lockedRegisterText}>Register now</Text>
          </TouchableOpacity>
        );
      }
      if (shouldShowRegisterButton) {
        return (
          <TouchableOpacity
            onPress={handleJoinOrSubmitButtonPress}
            style={[
              styles.fab,
              { backgroundColor: dark.colors.secondary, width: '90%' },
            ]}
          >
            <Text style={styles.fabText}>
              {isSubmitting ? 'Registering...' : 'Register Now'}
            </Text>
          </TouchableOpacity>
        );
      }
      if (contestIsAboutToStartOrLiveAndUserIsRegistered) {
        return (
          <TouchableOpacity
            onPress={handleJoinOrSubmitButtonPress}
            style={[
              styles.fab,
              { backgroundColor: dark.colors.secondary, width: '90%' },
            ]}
          >
            <Text style={styles.fabText}>
              {isLive ? 'Join Now' : 'Join Waiting Room'}
            </Text>
          </TouchableOpacity>
        );
      }

      if (canUserWithdrawRegistration) {
        return (
          <TouchableOpacity
            style={[styles.fab, styles.unRegisterButton]}
            onPress={onPressUnRegister}
          >
            <Text style={styles.unregisterText}>Unregister</Text>
          </TouchableOpacity>
        );
      }
      return null;
    }, [
      isGuest,
      contestIsAboutToStartOrLiveAndUserIsRegistered,
      shouldShowRegisterButton,
      isLive,
      isSubmitting,
      handleJoinOrSubmitButtonPress,
      onPressUnRegister,
      canUserWithdrawRegistration,
    ]);

    const renderContestCountdownTimer = useMemo(() => {
      if (hasEnded) {
        return null;
      }
      if (contestDidNotStartedYetAndNotEnded) {
        return (
          <View style={{ marginLeft: 16 }}>
            <CountdownTimer targetDate={new Date(contestDetails?.startTime)} />
          </View>
        );
      }
      if (contestIsLiveAndNotEnded) {
        return (
          <View style={{ marginLeft: 16, marginBottom: 15 }}>
            <CountdownTimer
              targetDate={new Date(contestDetails?.endTime)}
              prefix="Ends in"
            />
          </View>
        );
      }
      return null;
    }, [
      contestIsLiveAndNotEnded,
      contestDidNotStartedYetAndNotEnded,
      contestDetails,
      hasEnded,
    ]);

    // const showUnregisterContent = () => {
    //     if (canUserWithdrawRegistration) {
    //         return (
    //             <View style={styles.registeredContainer}>
    //                 <MaterialIcons name="check-circle" size={27} color={dark.colors.success} style={styles.checkedIcon} />
    //                 <Text style={styles.registeredMessageText}>Successfully Registered!   </Text>
    //                 <Button
    //                     title="Unregister"
    //                     type="clear"
    //                     color={'transparent'}
    //                     titleStyle={{
    //                         fontSize: 14,
    //                         fontFamily: 'Montserrat-500',
    //                         color: DarkTheme.colors.red,
    //                     }}
    //                     onPress={onPressUnRegister}
    //                 />
    //             </View>)
    //     }
    //     return null
    // }

    const eventProperties = {
      ...getContestPropertiesToTrack({ contest: contestDetails }),
      [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
    };

    const contestInfoHeader = () => (
      <View style={styles.contestDetails}>
        <LinearGradient
          colors={dark.colors.contestLogoBgGradient}
          style={styles.gradientBox}
        >
          <View style={styles.iconContainer}>
            {contestDetails?.hostedByV2?.logo ? (
              <Image
                source={{ uri: contestDetails?.hostedByV2?.logo }}
                style={styles.hostedByLogo}
              />
            ) : (
              <Text style={styles.headerTitle}>🏆</Text>
            )}
          </View>
        </LinearGradient>
        <View style={styles.contestTimeAndDesc}>
          <View style={styles.detailsRow}>
            <Text style={styles.contestTitle} numberOfLines={1}>
              {contestDetails?.name}
            </Text>
            {isLive && (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Live</Text>
              </View>
            )}
          </View>
          <Text
            style={styles.hostedBy}
          >{`Hosted By ${contestDetails?.hostedByV2?.name ?? 'Matiks'}`}</Text>
          {contestDetails?.startTime && (
            <Text style={styles.detailsText}>
              {new Date(contestDetails.startTime).toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'short',
              })}{' '}
              |{' '}
              {new Date(contestDetails.startTime).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
              })}{' '}
              -{' '}
              {new Date(contestDetails.endTime).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
              })}{' '}
              | {Math.round(contestDetails?.contestDuration / 60)} Min
            </Text>
          )}
        </View>
      </View>
    );

    return (
      <View style={styles.container}>
        <View
          colors={dark.colors.contestMainPageGradient}
          style={styles.gradient}
        >
          <Header title="Contest" isTransparentBg />
          {contestInfoHeader()}
          {renderContestCountdownTimer}
          {/* {showUnregisterContent()} */}
        </View>
        {/* Contest Details Tab Bar View */}
        <CompactContestDetailsTabBarView
          contestDetails={contestDetails}
          hasEnded={hasEnded}
          isLive={isLive}
          hasUserRegistered={hasUserRegistered}
        />
        {renderJoinNowOrRegisterButton}

        {!_isEmpty(formFields) && (
          <FormBottomSheet
            isVisible={isBottomSheetVisible}
            setIsVisible={setIsBottomSheetVisible}
            fields={formFields}
            onSubmit={handleFormSubmit}
            isLoading={isSubmitting}
            eventProperties={eventProperties}
          />
        )}
      </View>
    );
  },
);

const CompactContestDetailsContainer = React.forwardRef((props, ref) => {
  const { contestDetails, loading } = props;
  const { clubId } = contestDetails ?? EMPTY_OBJECT;

  if (loading && _isEmpty(contestDetails)) {
    return <CompactContestDetailsShimmer />;
  }

  if (!_isNil(clubId)) {
    return (
      <WithClubAccess clubId={clubId}>
        <CompactContestDetails ref={ref} {...props} />
      </WithClubAccess>
    );
  }

  return <CompactContestDetails ref={ref} {...props} />;
});

export default React.memo(CompactContestDetailsContainer);
