import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import HtmlRenderer from 'atoms/HtmlRenderer';
import styles from './CompactContestDetail.style';
import dark from '../../../../core/constants/themes/dark';

const ExpandableSection = ({ title, expanded, onToggle, content }) => (
  <View>
    <TouchableOpacity style={styles.expandableHeader} onPress={onToggle}>
      <Text style={styles.expandableText}>{title}</Text>
      <Icon
        name={expanded ? 'chevron-up-outline' : 'chevron-down-outline'}
        size={15}
        color={dark.colors.secondary}
      />
    </TouchableOpacity>
    {expanded && (
      <View style={styles.expandedContent}>
        <HtmlRenderer html={content} />
      </View>
    )}
  </View>
)

export default React.memo(ExpandableSection);
