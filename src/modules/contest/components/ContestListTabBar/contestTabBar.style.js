import { Platform, StyleSheet } from "react-native"
import dark from "core/constants/themes/dark"

const styles = StyleSheet.create({
    scrollContainer: {
      // borderBottomWidth: 1,
      borderBottomColor: dark.colors.tertiary,
      paddingVertical: 0,
      marginLeft: Platform.select({
          android: 8,
          default: 0
        }
      )
    },
    itemContainer: {
      marginHorizontal: 4,
      alignItems: 'center',
      width: 150,
      borderWidth: 1,
      borderRadius: 12,
      height:59
    },
    itemImageContainer: {
        width: 146,
        height: 57,
        borderRadius: 12,
        marginBottom: 8,
        position: 'relative',
        overflow: 'hidden',
      },
    itemImage: {
      width: 160,
      height: 70,
      borderRadius: 10,
      marginBottom: 8,
    },
    backgroundColorView: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      },
    itemText: {
      color: dark.colors.textDark,
      fontSize: 14,
    },
    activeItemText: {
      color: dark.colors.secondary,
      fontSize: 14,
      fontWeight: '600',
    },
    contentContainer:{
        position: 'absolute',
        zIndex: 5,
        flexDirection: 'row',
        flex: 1,
        paddingHorizontal: 12,
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        height: '100%',
    },
    title:{
        fontSize: 10,
        lineHeight: 14,
        fontFamily:'Montserrat-800'
    },
    contentIcon:{
        width: 38,
        height: 42,
    }
  })

  export default styles