import React, { useEffect, useState } from 'react';
import { Pressable, Text, View, Image } from 'react-native';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';

import _isNil from 'lodash/isNil';
import useContestBannerStyles from './ContestBanner/ContestBanner.style';

const ensureMinDigits = (num, minDigits) => {
  let numString = `${num}`;
  while (numString.length < minDigits) {
    numString = `0${numString}`;
  }
  return numString;
};

const StartsInTimer = ({ contest }) => {
  const { endTime } = contest;

  const styles = useContestBannerStyles();

  const [timeLeft, setTimeLeft] = useState();

  useEffect(() => {
    const interval = setInterval(() => {
      const now = getCurrentTimeWithOffset();
      const timeLeft = Math.max(0, new Date(endTime).getTime() - now);
      setTimeLeft(timeLeft);
    }, 1000);

    return () => clearInterval(interval);
  }, [endTime]);

  const renderLiveTimer = ({ label, value }) => (
    <View style={styles.compactTimerContainer}>
      <Text style={styles.timerValue}>{ensureMinDigits(value, 2)}</Text>
      <Text style={styles.timerLabel}>{label}</Text>
    </View>
  );

  if (_isNil(timeLeft)) {
    return null;
  }
  
  const totalHours = Math.floor(timeLeft / 1000 / 60 / 60);
  const daysLeft = Math.floor(totalHours / 24);
  const hoursLeft = totalHours >= 24 ? totalHours % 24 : totalHours;
  const minutesLeft = Math.floor((timeLeft / 1000 / 60) % 60);
  const secondsLeft = Math.floor((timeLeft / 1000) % 60);

  const timeUnits = totalHours >= 24
    ? [
        { label: 'DAYS', value: daysLeft },
        { label: 'HRS', value: hoursLeft },
        { label: 'MIN', value: minutesLeft }
      ]
    : [
        { label: 'HRS', value: hoursLeft },
        { label: 'MIN', value: minutesLeft },
        { label: 'SEC', value: secondsLeft }
    ];

  return (
    <View style={styles.contentContainer}>
      <Text style={styles.startingInText}>STARTING IN</Text>
      <View style={styles.timerContainer}>
        {timeUnits.map((unit, index) => (
          <React.Fragment key={unit.label}>
            {renderLiveTimer(unit)}
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};

export default React.memo(StartsInTimer);