import React, { useCallback, useEffect, useMemo } from 'react';
import { View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';

import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import useMediaQuery from 'core/hooks/useMediaQuery';

import styles from './LiveContest.style';
import Header from './components/Header';
import useContestDetails from '../../hooks/useContestDetails';
import ContestCompletionPage from '../ContestCompletionPage';
import Question from '../../../game/pages/PlayGame/Question';
import Footer from '../../../game/pages/PlayGame/Footer';
import useContestQuestionState from '../../hooks/useContestQuestionState';
import useGetUserSubmissionInContest from '../../hooks/queries/useGetUserSubmissionInContest';
import useUpdateContestParticipantStartTime from '../../hooks/mutations/useUpdateContestParticipantStartTime';
import getCurrentTimeWithOffset from '../../../../core/utils/getCurrentTimeWithOffset';
import { CountdownKeyboardView } from '../VirtualContestPlay/VirtualContestPlay';

const EMPTY_OBJECT = {};

const LiveContest = ({ contest, userSubmission }) => {
  const { isMobile } = useMediaQuery();
  const { updateContestParticipantStartTime } =
    useUpdateContestParticipantStartTime();

  const { endTime, startTime, _id: contestId, contestDuration = 0 } = contest;

  const startDateTime = new Date(startTime);

  const { startTime: userStartTime } = userSubmission ?? EMPTY_OBJECT;
  const startTimeForUser = useMemo(
    () => userStartTime ?? getCurrentTimeWithOffset(),
    [userStartTime],
  );

  const expectedEndTimeForUser =
    new Date(startTimeForUser).getTime() + contestDuration * 1000;
  const endTimeForUser = Math.min(new Date(endTime), expectedEndTimeForUser);

  const hasContestEndedForUser = endTimeForUser <= getCurrentTimeWithOffset();
  const isContestLiveForUser =
    startDateTime <= getCurrentTimeWithOffset() &&
    endTimeForUser >= getCurrentTimeWithOffset();

  const {
    currentQuestion,
    submitAnswer,
    currentScore,
    solvedAllQuestions,
    totalTimeTaken,
    submittedAllAnswersSuccessFully,
  } = useContestQuestionState({
    contest,
    initialUserSubmission: userSubmission,
  });

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer question={currentQuestion} submitAnswer={submitAnswer} />
      </View>
    ),
    [currentQuestion],
  );

  useEffect(() => {
    if (_isNil(userStartTime)) {
      try {
        updateContestParticipantStartTime({
          contestId,
          startTime: startTimeForUser,
        });
      } catch (error) {
        // console.log(error)
      }
    }
  }, [contestId]);

  const renderCompletionPage = useCallback(
    () => (
      <ContestCompletionPage contest={contest} currentScore={currentScore} />
    ),
    [contest, currentScore],
  );

  if (hasContestEndedForUser) {
    return renderCompletionPage();
  }

  if (solvedAllQuestions) {
    if (submittedAllAnswersSuccessFully) {
      return renderCompletionPage();
    }
    return <Loading />;
  }

  if (!isContestLiveForUser) {
    return null;
  }

  return (
    <CountdownKeyboardView>
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={isMobile && styles.mobileHeader}>
            <Header
              contest={contest}
              currentScore={currentScore}
              startTimeForUser={startTimeForUser}
            />
          </View>
          <View style={[styles.question]}>
            <Question question={currentQuestion} />
          </View>
          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </CountdownKeyboardView>
  );
};

const LiveContestContainer = (props) => {
  const { contest, ...restProps } = props;
  const contestId = contest?._id;

  const {
    contestDetails = EMPTY_OBJECT,
    loading: contestLoading,
    error,
    refetch,
  } = useContestDetails({ contestId });

  const { userSubmission, loading: submissionLoading } =
    useGetUserSubmissionInContest({
      contestId: contest?._id,
    });

  if (_isEmpty(contest) || contestLoading || submissionLoading) {
    return <Loading />;
  }

  if (!_isEmpty(error)) {
    return (
      <ErrorView
        errorMessage="Something went wrong while fetching Contest Questions"
        onRetry={refetch}
      />
    );
  }

  const { currentUserParticipation } = contest;

  if (_isEmpty(currentUserParticipation)) {
    return (
      <ErrorView errorMessage="You have not registered for this contest" />
    );
  }

  return (
    <LiveContest
      userSubmission={userSubmission}
      contest={contestDetails}
      {...restProps}
    />
  );
};

export default React.memo(LiveContestContainer);
