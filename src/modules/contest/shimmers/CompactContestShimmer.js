import { ScrollView ,View} from "react-native";
import styles from "../components/CompactContestDetail/CompactContestDetail.style";
import Loading from '@/src/components/atoms/Loading'
const CompactContestDetailsShimmer = () => (
    <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}>
      <Loading label={"Loading Contest Details"}/>
      {/* <View style={styles.header}>
        <ShimmerPlaceHolder style={styles.shimmerHeaderButton} />
        <ShimmerPlaceHolder style={styles.shimmerHeaderText} />
        <ShimmerPlaceHolder style={styles.shimmerHeaderButton} />
      </View>
      <View style={styles.contestDetails}>
        <ShimmerPlaceHolder style={styles.shimmerImage} />
        <ShimmerPlaceHolder style={styles.shimmerTitle} />
        <ShimmerPlaceHolder style={styles.shimmerSubText} />
        <ShimmerPlaceHolder style={styles.shimmerDetailsRow} />
      </View>
      <ShimmerPlaceHolder style={styles.shimmerExpandable} />
  
      <ShimmerPlaceHolder style={styles.shimmerFab} /> */}
    </ScrollView>
  
  );

  export default CompactContestDetailsShimmer