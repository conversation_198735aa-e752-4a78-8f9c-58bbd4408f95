import { gql, useMutation } from '@apollo/client';
import { ObjectId } from 'bson';
import { useStorageState } from 'core/hooks/useStorageState';
import { useCallback } from 'react';
import _isNil from 'lodash/isNil';

import { CURRENT_USER_FRAGMENT } from 'core/graphql/fragments/user';

const GUEST_LOGIN_QUERY = gql`
  ${CURRENT_USER_FRAGMENT}
  mutation LoginAsGuest($guestId: ID!) {
    guestUser: loginAsGuest(guestId: $guestId) {
      ...CurrentUserFields
      isSignup
    }
  }
`;

const useGuestLogin = () => {
  const [loginAsGuestQuery] = useMutation(GUEST_LOGIN_QUERY);

  const [guestId, setGuestId] = useStorageState('guestId');

  const loginAsGuest = useCallback(async () => {
    let newGuestId = guestId;
    if (_isNil(guestId)) {
      newGuestId = new ObjectId().toHexString();
      setGuestId(newGuestId);
    }

    const response = await loginAsGuestQuery({
      variables: { guestId: newGuestId },
    });

    const { data } = response ?? EMPTY_OBJECT;
    const { guestUser } = data ?? EMPTY_OBJECT;

    if (guestUser?._id !== guestId) {
      setGuestId(guestUser?._id);
    }

    webengage?.user?.login?.(guestUser?._id);

    return response;
  }, [guestId]);

  return {
    loginAsGuest,
    setGuestId,
  };
};

export default useGuestLogin;
