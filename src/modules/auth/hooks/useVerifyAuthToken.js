import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';

import { CURRENT_USER_FRAGMENT } from 'core/graphql/fragments/user';

const VERIFY_TOKEN_QUERY = gql`
  ${CURRENT_USER_FRAGMENT}
  query VerifyToken($token: String!) {
    user: verifyToken(token: $token) {
      ...CurrentUserFields
    }
  }
`;

const useVerifyAuthToken = () => {
  const [verifyToken, { loading }] = useLazyQuery(VERIFY_TOKEN_QUERY, {
    fetchPolicy: 'network-only',
  });

  const getLoggedInUser = useCallback(
    ({ token }) => verifyToken({ variables: { token } }),
    [verifyToken],
  );

  return {
    getLoggedInUser,
    fetchingLoggedInUser: loading,
  };
};

export default useVerifyAuthToken;
