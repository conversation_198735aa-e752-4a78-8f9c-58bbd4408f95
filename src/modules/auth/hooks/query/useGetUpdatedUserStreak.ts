import { gql, useMutation } from '@apollo/client';
//     getUpdatedUserStreaks: UserStreaks @auth

const USER_STREAK_MUTATION = gql`
  mutation GetUpdatedUserStreaks {
    getUpdatedUserStreaks {
      currentStreak
      longestStreak
      lastSevenDays
      streakFreezers
    }
  }
`;

const useGetUpdatedUserStreaks = () => {
  const [fetchUserStreaks, { loading, error }] =
    useMutation(USER_STREAK_MUTATION);

  return { fetchUserStreaks, loading, error };
};

export default useGetUpdatedUserStreaks;
