import React from 'react';
import {
  Image,
  ImageSourcePropType,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import dark from 'core/constants/themes/dark';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';

// Define types for our instruction steps
export interface InstructionStep {
  title?: string;
  description: string;
  image?: ImageSourcePropType;
  icon?: {
    name: string;
    size: number;
    color: string;
  };
  rules?: string[];
  examples?: {
    text: string;
    highlight?: boolean;
  }[];
  showStepNumber?: boolean;
}

// Define types for the overall instructions
export interface PuzzleInstructionProps {
  title: string;
  subtitle?: string;
  steps: InstructionStep[];
}

const styles = StyleSheet.create({
  container: {
    padding: 0,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-600',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
    fontStyle: 'italic',
  },
  instruction: {
    fontSize: 16,
    marginBottom: 12,
    lineHeight: 18,
    color: '#d9d9d9',
    fontFamily: 'Montserrat-600',
  },
  bold: {
    fontWeight: 'bold',
  },
  stepContainer: {
    marginBottom: 24,
  },
  stepRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    alignItems: 'center',
  },
  stepImage: {
    width: 200,
    height: 150,
    resizeMode: 'contain',
    marginTop: 12,
    alignSelf: 'center',
  },
  ruleContainer: {
    marginLeft: 12,
    marginTop: 8,
    marginBottom: 8,
  },
  ruleText: {
    fontSize: 12,
    lineHeight: 16,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
    marginBottom: 4,
  },
  exampleContainer: {
    marginLeft: 20,
    marginTop: 8,
    marginBottom: 8,
  },
  exampleText: {
    fontSize: 15,
    lineHeight: 22,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-500',
    marginBottom: 6,
  },
  highlightedExample: {
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDarkHighLighted,
  },
});

const GenericPuzzleInstructionContent: React.FC<PuzzleInstructionProps> = ({
  title,
  subtitle,
  steps,
}) => (
  <ScrollView
    contentContainerStyle={styles.container}
    showsVerticalScrollIndicator={false}
  >
    <Text style={styles.title}>{title}</Text>
    {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}

    {steps.map((step, index) => (
      <View key={index} style={styles.stepContainer}>
        {step.icon ? (
          <View style={styles.stepRow}>
            <Text style={styles.instruction}>
              {step.showStepNumber !== false && `${index + 1}. `}
              {step.title && <Text style={styles.bold}>{step.title}</Text>}
              {step.description && ` ${step.description}`}{' '}
              {step.icon && (
                <MaterialCommunityIcons
                  name={step.icon.name}
                  size={step.icon.size}
                  color={step.icon.color}
                />
              )}
            </Text>
          </View>
        ) : (
          <Text style={styles.instruction}>
            {step.showStepNumber !== false && `${index + 1}. `}
            {step.title && <Text style={styles.bold}>{step.title}</Text>}
            {step.description && ` ${step.description}`}
          </Text>
        )}

        {step.rules && step.rules.length > 0 && (
          <View style={styles.ruleContainer}>
            {step.rules.map((rule, ruleIndex) => (
              <Text key={ruleIndex} style={styles.ruleText}>
                • {rule}
              </Text>
            ))}
          </View>
        )}

        {step.examples && step.examples.length > 0 && (
          <View style={styles.exampleContainer}>
            {step.examples.map((example, exampleIndex) => (
              <Text
                key={exampleIndex}
                style={[
                  styles.exampleText,
                  example.highlight && styles.highlightedExample,
                ]}
              >
                {example.highlight ? '→ ' : '• '}
                {example.text}
              </Text>
            ))}
          </View>
        )}

        {step.image && (
          <Image source={{ uri: step.image }} style={styles.stepImage} />
        )}
      </View>
    ))}
  </ScrollView>
);

export default GenericPuzzleInstructionContent;
