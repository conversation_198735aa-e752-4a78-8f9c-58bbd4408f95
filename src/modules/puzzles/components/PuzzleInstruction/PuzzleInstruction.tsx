import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ReactNode, useCallback } from 'react';
import { router } from 'expo-router';

interface PuzzleInstructionProps {
  instructionContent: ReactNode;
  analyticsEvent?: string;
  buttonLabel?: string;
}

const PuzzleInstruction = ({
  instructionContent,
  analyticsEvent = ANALYTICS_EVENTS.CROSS_MATH_PUZZLE
    .CLICKED_ON_PUZZLE_INFO_ICON,
  buttonLabel = 'How to Play?',
}: PuzzleInstructionProps) => {
  const onPressInfoIcon = useCallback(() => {
    Analytics.track(analyticsEvent);
    router.push('/how-to-play/puzzle-game');
  }, [analyticsEvent]);

  return (
    <InteractiveSecondaryButton
      label={buttonLabel}
      buttonContainerStyle={{ height: 48, width: 120 }}
      labelStyle={{ fontSize: 12, fontFamily: 'Montserrat-600' }}
      onPress={onPressInfoIcon}
    />
  );
};

export default PuzzleInstruction;
