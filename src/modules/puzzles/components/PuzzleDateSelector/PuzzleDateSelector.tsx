import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';
import CalendarView, {
  CalendarMonthSelector,
  getTodaysMonthYear,
  MonthYearTypes,
} from 'shared/CalendarView';

import _includes from 'lodash/includes';
import _map from 'lodash/map';
import { useRouter } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import dark from 'core/constants/themes/dark';
import _isNil from 'lodash/isNil';
import { format, parse } from 'date-fns';
import _toUpper from 'lodash/toUpper';
import _get from 'lodash/get';
import _uniq from 'lodash/uniq';
import CalendarDate from './components/CalendarDate';
import useGetPuzzleSubmissionsByMonth from '../../hooks/queries/useGetPuzzleSubmissionsByMonth';
import { PUZZLE_TYPES } from '../../types/puzzleType';

export const formatPuzzleDate = (dateString: string): string => {
  if (_isNil(dateString)) return '';
  const date = parse(dateString, 'yyyy-MM-dd', new Date());

  return format(date, 'do MMM yyyy');
};

const PuzzleDateSelector = ({
  date: selectedDateFromProps,
  onDateSelect,
  puzzleType,
}) => {
  const [selectedDate, setSelectedDate] = useState<string>(
    selectedDateFromProps,
  );

  const [allSolvedDates, setAllSolvedDates] = useState(EMPTY_ARRAY);
  // Removed console log effect that was causing re-renders
  const [selectedMonthYear, setSelectedMonthYear] = useState<MonthYearTypes>(
    getTodaysMonthYear(selectedDate),
  );

  const router = useRouter();

  const onDateSelected = useCallback(
    ({ date }) => {
      setSelectedDate(date);
    },
    [setSelectedDate],
  );

  const formattedMonthYear = useMemo(
    () =>
      `${selectedMonthYear.year}-${selectedMonthYear.month < 9 ? '0' : ''}${selectedMonthYear.month + 1}`,
    [selectedMonthYear.year, selectedMonthYear.month],
  );

  const queryParams = useMemo(
    () => ({
      yearMonths: [formattedMonthYear],
      puzzleType,
    }),
    [formattedMonthYear, puzzleType],
  );

  const { data, loading, error } = useGetPuzzleSubmissionsByMonth(queryParams);

  useEffect(() => {
    const puzzleSubmissions = _get(data, [0, 'puzzleSubmissions']);
    if (!puzzleSubmissions) return;

    const solvedDatesForCurrentMonth = _map(puzzleSubmissions, 'puzzleDate');
    if (solvedDatesForCurrentMonth.length === 0) return;

    setAllSolvedDates((prevDates) => {
      const newDates = _uniq([...prevDates, ...solvedDatesForCurrentMonth]);
      // Only update state if there are actually new dates
      return newDates.length === prevDates.length ? prevDates : newDates;
    });
  }, [data]);

  const isSelectedDateAlreadySolved = useMemo(
    () => _includes(allSolvedDates, selectedDate),
    [allSolvedDates, selectedDate],
  );

  const navigateToPlayPage = useCallback(() => {
    if (isSelectedDateAlreadySolved) {
      router.replace(
        `/puzzle/daily-challenge/${selectedDate}/result?puzzleType=${puzzleType}`,
      );
      return;
    }

    if (puzzleType === PUZZLE_TYPES.CROSS_MATH_PUZZLE) {
      Analytics.track(ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.CHANGED_PUZZLE_DATE, {
        selectedDate,
      });
    } else if (puzzleType === PUZZLE_TYPES.KEN_KEN_PUZZLE) {
      Analytics.track(ANALYTICS_EVENTS.KEN_KEN_PUZZLE.CHANGED_PUZZLE_DATE, {
        selectedDate,
      });
    }

    if (onDateSelect) {
      onDateSelect?.({ selectedDate });
    } else {
      router.replace(
        `/puzzle/daily-challenge/${selectedDate}?puzzleType=${puzzleType}`,
      );
    }
  }, [
    isSelectedDateAlreadySolved,
    selectedDate,
    router,
    onDateSelect,
    puzzleType,
  ]);

  const renderCalendarDate = useCallback(
    ({ item }) => {
      const isCompleted = _includes(allSolvedDates, item?.dateString);
      return (
        <CalendarDate
          item={item}
          onClick={onDateSelected}
          isCompleted={isCompleted}
          loading={loading}
          isSelected={selectedDate === item?.dateString}
        />
      );
    },
    [allSolvedDates, selectedDate, onDateSelected, loading],
  );

  const formattedSelectedDate = useMemo(
    () => formatPuzzleDate(selectedDate),
    [selectedDate],
  );

  const buttonLabel = useMemo(() => {
    if (isSelectedDateAlreadySolved) {
      return 'SHOW RESULTS';
    }
    return `PLAY ${_toUpper(formattedSelectedDate)}`;
  }, [isSelectedDateAlreadySolved, formattedSelectedDate]);

  return (
    <View style={{ flex: 1, gap: 32, alignItems: 'center' }}>
      <CalendarMonthSelector
        selectedMonthYear={selectedMonthYear}
        setSelectedMonthYear={setSelectedMonthYear}
      />
      <View style={{ height: 302 }}>
        <CalendarView
          year={selectedMonthYear.year}
          onDatePress={onDateSelected}
          month={selectedMonthYear.month}
          renderCalendarDate={renderCalendarDate}
        />
      </View>
      <View style={{ width: '100%', padding: 16, gap: 8 }}>
        <InteractiveSecondaryButton
          label={buttonLabel}
          containerStyle={{ width: '100%' }}
          borderColor={dark.colors.puzzle.primary}
          labelStyle={{
            color: dark.colors.puzzle.primary,
          }}
          onPress={navigateToPlayPage}
        />
      </View>
    </View>
  );
};

export default React.memo(PuzzleDateSelector);
