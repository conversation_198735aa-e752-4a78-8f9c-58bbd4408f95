import React, { useCallback } from 'react';
import Header from 'shared/Header';
import { View } from 'react-native';
import { XStack } from 'tamagui';
import WebBackButton from 'shared/WebBackButton';
import useMediaQuery from 'core/hooks/useMediaQuery';
import KenKenPuzzleQuestion from 'shared/KenKenPuzzleQuestion/KenKenPuzzleQuestion';

const KenKenPuzzleHeader = ({ showTimer = false }) => {
  const renderTrailingComponent = useCallback(
    () => (
      <View style={{ flexDirection: 'row', gap: 8, alignItems: 'center' }}>
        {showTimer ? (
          <>
            <KenKenPuzzleQuestion.PencilToggle />
            <KenKenPuzzleQuestion.Timer />
          </>
        ) : (
          <View />
        )}
      </View>
    ),
    [showTimer],
  );
  const { isMobile: isCompactMode } = useMediaQuery();

  const renderPuzzleHeader = () => {
    if (isCompactMode) return null;

    return (
      <XStack
        alignItems="center"
        jc="space-between"
        paddingHorizontal={16}
        paddingVertical={8}
      >
        <XStack flex={1} style={{ flex: 1 }}>
          {!isCompactMode ? (
            <XStack gap={8}>
              <WebBackButton title="" containerStyle={{ paddingVertical: 4 }} />
            </XStack>
          ) : (
            <View style={{ width: 40, height: 20 }} />
          )}
        </XStack>
        <XStack
          flex={1}
          gap={8}
          style={{
            flex: 1,
            alignItems: 'center',
            gap: 16,
            justifyContent: 'flex-end',
            flexDirection: 'row',
          }}
        >
          {!isCompactMode && (
            <>
              <KenKenPuzzleQuestion.Timer />
              <KenKenPuzzleQuestion.PencilToggle />
            </>
          )}
        </XStack>
      </XStack>
    );
  };

  return (
    <View
      style={{ width: '100%', justifyContent: 'center', flexDirection: 'row' }}
    >
      <View style={{ maxWidth: 600, width: '100%' }}>
        <Header renderTrailingComponent={renderTrailingComponent} />
        {renderPuzzleHeader()}
      </View>
    </View>
  );
};

export default React.memo(KenKenPuzzleHeader);
