import { View } from 'react-native';
import React, { useCallback, useEffect, useRef } from 'react';
import Loading from '@/src/components/atoms/Loading';
import _isEmpty from 'lodash/isEmpty';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import { getFormattedTimeWithMS } from 'core/utils/general';
import puzzleResultReader from 'modules/puzzles/readers/puzzleResultReader';
import usePrevious from 'core/hooks/usePrevious';
import ErrorView from 'atoms/ErrorView';
import _isNil from 'lodash/isNil';
import { router } from 'expo-router';
import useGetDailyPuzzle from 'modules/puzzles/hooks/queries/useGetDailyPuzzle';
import CrossMathPuzzleQuestion from 'shared/CrossMathPuzzleQuestion/CrossMathPuzzleQuestion';
import useSubmitPuzzleSolution from 'modules/puzzles/hooks/mutations/useSubmitPuzzleSolution';
import ACTIVITY_TYPES from 'core/constants/activityTypes';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useUserActivityTracker from 'core/hooks/useUserActivityTracker';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import CrossMathPuzzleHeader from '../../components/CrossMathPuzzleHeader';
import { ScrollView } from 'react-native';

const CrossMathPuzzle = React.memo(({ date }: { date: string }) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const { puzzle, loading, error } = useGetDailyPuzzle({ date });
  const { updateActivity } = useUserActivityTracker();
  const { submitPuzzleSolution } = useSubmitPuzzleSolution({
    puzzleId: puzzleReader.id(puzzle),
    puzzleDate: date,
    puzzleType: PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  });

  const hasSolved = puzzleReader.hasAttempted(puzzle);

  const puzzleResult = puzzleReader.currentUserResult(puzzle);

  const previousPuzzleResult = usePrevious(puzzleResult);

  const previousPuzzleResultRef = useRef(previousPuzzleResult);
  previousPuzzleResultRef.current = previousPuzzleResult;

  const onSubmitPuzzle = useCallback(
    ({ timeSpent }) => {
      if (hasSolved) return;
      updateActivity({
        activityType: ACTIVITY_TYPES.DAILY_PUZZLE,
        duration: timeSpent,
      });

      Analytics.track(
        ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.SOLVED_CROSS_MATH_PUZZLE,
        {
          timeSpent,
          date,
        },
      );
      submitPuzzleSolution({ timeSpent })
        .then(() =>
          router.replace(
            `/puzzle/daily-challenge/${date}/result?puzzleType=${PUZZLE_TYPES.CROSS_MATH_PUZZLE}`,
          ),
        )
        .catch((e) => {
          Analytics.track(
            ANALYTICS_EVENTS.CROSS_MATH_PUZZLE
              .ERROR_WHILE_SUBMITTING_CROSS_MATH_PUZZLE,
            { error: e },
          );
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Something went wrong while submitting the puzzle',
          });
        });
    },
    [hasSolved, updateActivity, date, submitPuzzleSolution],
  );

  useEffect(() => {
    if (hasSolved && !_isEmpty(puzzleResult)) {
      router.replace(
        `/puzzle/daily-challenge/${date}/result?puzzleType=${PUZZLE_TYPES.CROSS_MATH_PUZZLE}`,
      );
    }
  }, [hasSolved, puzzleResult]);

  const timeTaken = puzzleResultReader.timeSpent(puzzleResult);
  const formattedTimeTaken = timeTaken ? getFormattedTimeWithMS(timeTaken) : '';

  if (loading || _isNil(loading)) {
    return <Loading label="Loading Puzzle..." />;
  }

  if (error) {
    return (
      <ErrorView errorMessage="Something went wrong while loading puzzle" />
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <CrossMathPuzzleQuestion
        puzzle={puzzle}
        onSubmitPuzzle={onSubmitPuzzle}
        shouldCacheTime
      >
        <CrossMathPuzzleHeader showTimer />
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            padding: 8,
            gap: isCompactMode ? 32 : 64,
            justifyContent: isCompactMode ? 'space-around' : 'center',
            alignItems: 'center',
          }}
          showsVerticalScrollIndicator={false}
        >
          <CrossMathPuzzleQuestion.Grid />
          <CrossMathPuzzleQuestion.Actions />
          <CrossMathPuzzleQuestion.Options />
        </ScrollView>
      </CrossMathPuzzleQuestion>
    </View>
  );
});

export default CrossMathPuzzle;
