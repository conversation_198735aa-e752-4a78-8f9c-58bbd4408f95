import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _toNumber from 'lodash/toNumber';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import {
  updateDailyPuzzleCache,
  updatePuzzleSubmissionByMonthCache,
} from '../cacheUpdate/updateCacheOnPuzzleSubmissionUtils';

const SUBMIT_PUZZLE_SOLUTION_MUTATION_QUERY = gql`
  mutation Mutation($puzzleId: ID!, $timeSpent: Int!) {
    submitPuzzleSolution(puzzleId: $puzzleId, timeSpent: $timeSpent) {
      id
      userId
      puzzleId
      completedAt
      statikCoinsEarned
      puzzleDate
      timeSpent
    }
  }
`;

interface SubmitPuzzleSolutionResponse {
  submitPuzzleSolution: ({ timeSpent }: { timeSpent: number }) => Promise<any>;
}

const useSubmitPuzzleSolution = ({
  puzzleId,
  puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  puzzleDate,
}: {
  puzzleId: string;
  puzzleDate: string;
  puzzleType?: string;
}): SubmitPuzzleSolutionResponse => {
  const [submitPuzzleSolutionQuery] = useMutation(
    SUBMIT_PUZZLE_SOLUTION_MUTATION_QUERY,
    {
      update(cache, result) {
        const puzzleSubmissionResult = result.data?.submitPuzzleSolution;
        updateDailyPuzzleCache({
          cache,
          puzzleSubmissionResult,
          puzzleDate,
        });

        updatePuzzleSubmissionByMonthCache({
          cache,
          puzzleSubmissionResult,
          puzzleDate,
          puzzleType,
        });
      },
    },
  );

  const submitPuzzleSolution = useCallback(
    ({ timeSpent }: { timeSpent: number }) => {
      if (_toNumber(timeSpent) <= 0) return Promise.reject();
      return submitPuzzleSolutionQuery({
        variables: {
          puzzleId,
          timeSpent: Math.floor(_toNumber(timeSpent)),
        },
      });
    },
    [puzzleId],
  );

  return {
    submitPuzzleSolution,
  };
};

export default useSubmitPuzzleSolution;
