import { useCallback, useMemo, useRef, useState } from 'react';
import _get from 'lodash/get';
import _toNumber from 'lodash/toNumber';
import _map from 'lodash/map';
import _find from 'lodash/find';
import { useRouter } from 'expo-router';
import useCaptureView from 'core/hooks/useCaptureView';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { closePopover } from 'molecules/Popover/Popover';
import gameReader from 'core/readers/gameReader';
import gameLeaderboardReader from 'core/readers/gameLeaderboardReader';
import { stringifyQueryParams } from '@/src/core/utils/general';
import _every from 'lodash/every';
import { useSession } from '../../auth/containers/AuthProvider';
import useGameContext from './useGameContext';
import useRequestRematch from './mutations/useRequestRematch';
import useGoBack from '../../../navigator/hooks/useGoBack';
import { GAME_RESULT_STATUS } from '../constants/game';
import { GAME_TYPES } from '../../home/<USER>/gameTypes';

const useHandleGameLeaderboard = () => {
  const { game, players, gameMeta } = useGameContext();

  const { user } = useSession();

  const router = useRouter();
  const gameRef = useRef(game);
  const gameConfigRef = useRef();
  const { captureView } = useCaptureView();
  const { requestRematch } = useRequestRematch({ gameId: gameReader.id(game) });
  const [isRequesting, setIsRequesting] = useState(false);
  const { goBackToHome } = useGoBack();

  const gameConfig = gameReader.config(game);
  const leaderBoard = gameReader.leaderBoard(game);
  const gameType = gameReader.gameType(game);
  const gamePlayers = gameReader.players(game);

  const { timeLimit = 0 } = gameConfig ?? EMPTY_OBJECT;

  const isFlashAnzan = useMemo(
    () => gameType === GAME_TYPES.FLASH_ANZAN,
    [gameType],
  );

  const adaptedPlayers = useMemo(
    () =>
      _map(players, (player) => {
        const { _id: playerId } = player;
        const leaderBoardEntry = gameLeaderboardReader.findEntryForPlayer(
          leaderBoard,
          playerId,
        );

        return {
          ...player,
          ratingChange: gameLeaderboardReader.ratingChange(leaderBoardEntry),
          statikCoinsEarned:
            gameLeaderboardReader.statikCoinsEarned(leaderBoardEntry),
          score: gameLeaderboardReader.getScore(leaderBoardEntry, isFlashAnzan),
          isWinner: gameLeaderboardReader.isWinner(leaderBoardEntry),
        };
      }),
    [players, leaderBoard, isFlashAnzan],
  );

  const currentPlayer = useMemo(
    () => _find(adaptedPlayers, (player) => player?._id === user?._id),
    [adaptedPlayers, user],
  );
  const currentPlayerStatus = currentPlayer?.isWinner
    ? GAME_RESULT_STATUS.WIN
    : GAME_RESULT_STATUS.LOSS;

  const currentPlayerOriginalRating = useMemo(
    () =>
      _get(
        _find(gamePlayers, (player) => player?.userId === user?._id),
        'rating',
      ),
    [gamePlayers, user],
  );

  const currentPlayerOriginalStatikCoins = useMemo(
    () =>
      _get(
        _find(gamePlayers, (player) => player?.userId === user?._id),
        'statikCoins',
        0,
      ),
    [gamePlayers, user],
  );

  const player1 = useMemo(() => adaptedPlayers[0], [adaptedPlayers]);
  const player2 = useMemo(() => adaptedPlayers[1], [adaptedPlayers]);

  const navigateToNewGame = useCallback(() => {
    closePopover?.();
    Analytics.track(
      ANALYTICS_EVENTS.RESULT_MODAL.CLICKED_ON_NEW_GAME_ON_RESULT_MODAL,
    );
    if (gameType === GAME_TYPES.FLASH_ANZAN) {
      const queryParams = {
        timeLimit: 1.5,
        gameType,
      };
      const stringifiedQueryParams = stringifyQueryParams(queryParams);
      router.replace(`/search?${stringifiedQueryParams}`);
      return;
    }
    router.replace(
      `/search?timeLimit=${_toNumber(timeLimit) / 60}&gameType=${gameType}`,
    );
  }, [router, timeLimit, gameType]);

  const navigateToHome = useCallback(() => {
    closePopover?.();
    Analytics.track(
      ANALYTICS_EVENTS.RESULT_MODAL.CLICKED_ON_GO_HOME_ON_RESULT_MODAL,
    );
    goBackToHome();
  }, [goBackToHome]);

  const onPressShareButton = useCallback(() => {
    const winningStatusMessage =
      currentPlayerStatus === GAME_RESULT_STATUS.WIN
        ? 'WON! the Mental Aptitude game on Matiks 😇'
        : 'I am improving my Mental Aptitude 🧠 on Matiks';
    const playWithMeMessage = 'Challenge me on https://www.matiks.in';
    const message = `${winningStatusMessage}, ${playWithMeMessage}`;
    captureView({ viewRef: gameConfigRef, message });

    const { gameType, config } = gameRef.current ?? {};
    Analytics.track(ANALYTICS_EVENTS.SHARE_RESULT, { gameType, ...config });
  }, [captureView, currentPlayerStatus]);

  const rematchWithSamePlayer = useCallback(async () => {
    closePopover?.();
    Analytics.track(ANALYTICS_EVENTS.RESULT_MODAL.CLICKED_ON_REMATCH_REQUEST);
    try {
      if (isRequesting) return;
      setIsRequesting(true);
      await requestRematch();
    } catch (e) {
      setIsRequesting(false);
      // console.log(`ERROR IS :- ${e}`);
    } finally {
      setIsRequesting(false);
    }
  }, [requestRematch, isRequesting]);

  const isCurrPlayerWinner = useMemo(
    () => currentPlayer?.isWinner,
    [currentPlayer],
  );

  const isMatchTied = useMemo(() => {
    if (!adaptedPlayers?.length) return false;
    const player1Score = adaptedPlayers[0]?.score;
    return _every(adaptedPlayers, (player) => player?.score === player1Score);
  }, [adaptedPlayers]);

  return {
    game,
    currentPlayer,
    player1,
    player2,
    currentPlayerOriginalRating,
    currentPlayerOriginalStatikCoins,
    currentPlayerStatus,
    players,
    adaptedPlayers,
    isCurrPlayerWinner,
    isRequesting,
    timeLimit,
    isFlashAnzan,
    gameMeta,
    navigateToHome,
    rematchWithSamePlayer,
    navigateToNewGame,
    onPressShareButton,
    isMatchTied,
  };
};

export default useHandleGameLeaderboard;
