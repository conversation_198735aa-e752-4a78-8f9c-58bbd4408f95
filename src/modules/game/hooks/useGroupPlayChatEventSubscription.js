import { useCallback, useEffect, useMemo, useState } from 'react';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _toNumber from 'lodash/toNumber';
import _compact from 'lodash/compact';
import { WEBSOCKET_EVENTS } from 'core/constants/events';
import userReader from 'core/readers/userReader';
import { useSession } from '../../auth/containers/AuthProvider';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';
import useWebsocketStore from 'store/useWebSocketStore';

export const useGroupPlayChatEventSubscription = ({ gameId }) => {
  const {
    isConnected,
    lastMessage,
    sendMessage: publishMessage,
    joinChannel,
    leaveChannel,
  } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
    lastMessage: state.lastMessage,
    sendMessage: state.sendMessage,
    joinChannel: state.joinChannel,
    leaveChannel: state.leaveChannel,
  }));

  const [messages, setMessages] = useState([]);

  const { user, userId } = useSession();
  const channel = WEBSOCKET_CHANNELS.GroupChatEvents(gameId);

  useEffect(() => {
    if (isConnected) {
      joinChannel(channel);
    }
  }, [isConnected]);

  const sendMessage = useCallback(
    ({ message }) => {
      publishMessage({
        type: 'sendMessage',
        channel,
        data: {
          gameId,
          message,
          userName: userReader.username(user),
          userId,
        },
      });
    },
    [gameId, user, userId],
  );

  const message = useMemo(() => {
    const data = lastMessage?.[channel];
    return data;
  }, [lastMessage?.[channel]]);

  useEffect(() => {
    if (!_isEmpty(message)) {
      setMessages((prev) => _compact([...prev, message]));
    }
  }, [message]);

  return {
    messages,
    sendMessage,
  };
};

export default useGroupPlayChatEventSubscription;
