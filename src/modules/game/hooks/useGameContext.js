import React, { useContext, useEffect, useMemo, useState } from 'react';

import _values from 'lodash/values';
import _reduce from 'lodash/reduce';
import _isNil from 'lodash/isNil';
import usePrevious from 'core/hooks/usePrevious';
import _isEqual from 'lodash/isEqual';
import gameReader from 'core/readers/gameReader';
import _includes from 'lodash/includes';
import useHandleGameEvents from './useHandleGameEvents';
import useGamePlayers from './useGamePlayers';
import useGameEventSubscription from './useGameEventSubscription';
import useFetchGameQuery from './useFetchGameQuery';
import GameContext, { GameContextProvider } from '../context/gameContext';

const VALID_GAME_KEYS_TO_UPDATE = ['gameStatus', 'players', 'leaderBoard'];

const useGame = ({ gameId }) => {
  const {
    game: initialGame,
    loading,
    error,
    reFetchGame,
  } = useFetchGameQuery({ gameId });

  const prevInitialGame = usePrevious(initialGame);

  const [currentGame, setCurrentGame] = useState(initialGame);

  const {
    game: updatedGame,
    event,
    submitAnswer,
  } = useGameEventSubscription({
    gameId,
    gameStatus: gameReader.gameStatus(currentGame),
  });

  useHandleGameEvents();

  const game = useMemo(() => {
    const tempGame = currentGame ?? initialGame ?? updatedGame;
    if (gameReader.id(tempGame) !== gameId) {
      return undefined;
    }
    return tempGame;
  }, [gameId, currentGame, initialGame, updatedGame]);

  const { players, refreshAllPlayers } = useGamePlayers({
    game,
  });

  useEffect(() => {
    if (
      (_isNil(prevInitialGame) && !_isNil(initialGame)) ||
      (!_isEqual(prevInitialGame, initialGame) && !_isNil(initialGame))
    ) {
      setCurrentGame((prevData) => {
        const gameDataToMerge = _reduce(
          initialGame,
          (acc, val, key) => {
            if (
              !_isNil(val) &&
              (_isNil(prevData?.[key]) ||
                (!_isNil(val) && _includes(VALID_GAME_KEYS_TO_UPDATE, key)))
            ) {
              acc[key] = val;
            }
            return acc;
          },
          {},
        );
        return { ...prevData, ...gameDataToMerge, questions: initialGame?.questions ?? updatedGame?.questions };
      });
    }
  }, [prevInitialGame, initialGame]);

  useEffect(() => {
    if (_isNil(updatedGame)) return;
    const gameDataToMerge = _reduce(
      updatedGame,
      (acc, val, key) => {
        if (!_isNil(val)) {
          acc[key] = val;
        }
        return acc;
      },
      {},
    );
    setCurrentGame((prevGame) => ({ ...prevGame, ...gameDataToMerge }));
  }, [updatedGame]);

  useEffect(() => {
    setCurrentGame((prevGame) => {
      if (gameReader.id(prevGame) !== gameId) {
        return undefined;
      }
      return prevGame;
    });
  }, [gameId]);

  return {
    game,
    updatedGame: updatedGame ?? game,
    gameEvent: event,
    players: _values(players),
    refreshAllPlayers,
    submitAnswer,
    reFetchGame,
    gameMeta: {
      loading,
      error,
    },
  };
};

export const WithGameContext = (Component) => {
  const GameContextWrapper = (props) => {
    const { gameId } = props;

    const contextValue = useGame({ gameId });
    return (
      <GameContextProvider value={contextValue}>
        <Component {...props} />
      </GameContextProvider>
    );
  };

  return React.memo(GameContextWrapper);
};

const useGameContext = () => useContext(GameContext);

export default useGameContext;
