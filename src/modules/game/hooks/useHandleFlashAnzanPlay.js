import { useCallback, useEffect, useMemo, useState } from 'react';
import _reduce from 'lodash/reduce';

import _map from 'lodash/map';
import _isNil from 'lodash/isNil';
import _toString from 'lodash/toString';
import _toNumber from 'lodash/toNumber';
import _keyBy from 'lodash/keyBy';
import _get from 'lodash/get';
import {
  getStorageState,
  setStorageItemAsync,
} from 'core/hooks/useStorageState';
import FLASH_ANZAN_GAME_PHASES from '../constants/flashAnzanGamePhases';
import { FLASH_ANZAN_DEFAULT_CONFIG } from '../../practice/constants/presetConfig';
import getCurrentTimeWithOffset from '../../../core/utils/getCurrentTimeWithOffset';
import { useSession } from '../../auth/containers/AuthProvider';
import { getIdentifierStringFromConfig } from '../../practice/utils/getIdentifierStringFromConfig';
import { PRESET_CATEGORY } from '../../../core/constants/presets';
import useSubmitFlashAnzanAnswer from './mutations/useSubmitFlashAnzanAnswer';
import useGameContext from './useGameContext';
import getMaxScoreOfFlashAnzanQueFromConfig from '../utils/getMaxScoreOfFlashAnzanQueByConfig';
import { SOLUTION_STATUS } from '../pages/PlayGame/Footer/AnswerEvaluators';

export const generateNumbers = ({ config }) => {
  const { digits, numberCount, includeSubstraction } = config;
  const max = 10 ** digits - 1;
  const min = 10 ** (digits - 1);
  let previousNum = null;
  let cumulativeSum = 0;
  return Array.from({ length: numberCount }, () => {
    let num;
    do {
      num = Math.floor(Math.random() * (max - min + 1)) + min;
      if (includeSubstraction && Math.random() < 0.5) {
        num = -num;
      }
    } while (num === previousNum);

    if (cumulativeSum + num <= 0) {
      num = Math.abs(num);
    }

    cumulativeSum += num;
    previousNum = num;
    return num;
  });
};

const getCurrentQuestionIndex = ({ game, currentUserId }) => {
  const { leaderBoard, players } = game;
  const leaderBoardByUserId = _keyBy(leaderBoard, (entry) =>
    _get(entry, 'userId'),
  );

  const scores = _map(players, (player) => ({
    userId: player?.userId,
    correct: _get(leaderBoardByUserId[player?.userId], 'correct', 0),
    incorrect: _get(leaderBoardByUserId[player?.userId], 'incorrect', 0),
  }));
  const playersScore = _reduce(
    scores,
    (acc, { userId, correct, incorrect }) => {
      acc[userId] = {
        incorrect,
        correct,
      };
      return acc;
    },
    {},
  );

  const queIndex =
    (playersScore[currentUserId].incorrect ?? 0) +
    (playersScore[currentUserId]?.correct ?? 0);

  return queIndex + 1;
};

const FLASH_ANZAN_CONFIG_KEY = 'FLASH_ANZAN_CONFIG';

const useHandleFlashAnzanPlay = () => {
  const { game } = useGameContext();
  const { userId } = useSession();
  const { submitFlashAnzanAnswer } = useSubmitFlashAnzanAnswer();

  const initialQuestion = useMemo(
    () => getCurrentQuestionIndex({ currentUserId: userId, game }),
    [game],
  );

  const [updatedConfig, setConfig] = useState({
    ...FLASH_ANZAN_DEFAULT_CONFIG,
    noOfQuestions: 3,
    numberCount: 10,
    configSelectionTime: 7,
    startingCountdownTime: 3,
    answerTime: 5,
  });

  const loadSavedConfig = useCallback(async () => {
    try {
      const savedConfig = await getStorageState(FLASH_ANZAN_CONFIG_KEY);
      if (!_isNil(savedConfig)) {
        return JSON.parse(savedConfig);
      }
    } catch (error) {
      console.info('Error loading saved config:', error);
    }
    return null;
  }, []);

  const saveConfig = useCallback(async ({ configToSave }) => {
    try {
      await setStorageItemAsync(
        FLASH_ANZAN_CONFIG_KEY,
        JSON.stringify(configToSave),
      );
    } catch (error) {
      console.info('Error saving config:', error);
    }
  }, []);

  const updateConfig = useCallback(
    async (key, value) => {
      const newConfig = { ...updatedConfig, [key]: value };
      const newConfigWithNumbersCount = {
        ...newConfig,
        numberCount: Math.floor((15 * 1000) / newConfig.flashSpeed),
      };
      setConfig(newConfigWithNumbersCount);

      await saveConfig({ configToSave: newConfigWithNumbersCount });
    },
    [setConfig, updatedConfig],
  );

  const initializeConfig = useCallback(async () => {
    const savedConfig = await loadSavedConfig();
    if (savedConfig) {
      setConfig(savedConfig);
    }
  }, [loadSavedConfig]);

  useEffect(() => {
    initializeConfig();
  }, []);

  const {
    numberCount,
    noOfQuestions,
    flashSpeed,
    configSelectionTime,
    startingCountdownTime,
    answerTime,
  } = updatedConfig;

  const [gamePhase, setGamePhase] = useState(FLASH_ANZAN_GAME_PHASES.CONFIG);
  const [phaseCountdown, setPhaseCountdown] = useState(configSelectionTime);
  const [isGameCompleted, setIsGameCompleted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(initialQuestion);
  const [numbers, setNumbers] = useState([]);
  const [currentNumberIndex, setCurrentNumberIndex] = useState(-1);
  const [userAnswer, setUserAnswer] = useState('');
  const [isFlashing, setIsFlashing] = useState(false);
  const [isInputWrong, setIsInputWrong] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState(false);
  const [gameResults, setGameResults] = useState([]);
  const [incorrectAttempts, setIncorrectAttempts] = useState(0);
  const [solutionStatus, setSolutionStatus] = useState(null);

  useEffect(() => {
    let countdownInterval;
    if (phaseCountdown > 0) {
      countdownInterval = setInterval(() => {
        setPhaseCountdown((prev) => prev - 1);
      }, 1000);
    } else {
      switch (gamePhase) {
        case FLASH_ANZAN_GAME_PHASES.CONFIG:
          setGamePhase(FLASH_ANZAN_GAME_PHASES.STARTING);
          setPhaseCountdown(startingCountdownTime);
          break;
        case FLASH_ANZAN_GAME_PHASES.STARTING:
          setGamePhase(FLASH_ANZAN_GAME_PHASES.FLASHING);
          startNextQuestion();
          break;
        case FLASH_ANZAN_GAME_PHASES.ANSWERING:
          submitAnswer({ answer: null });
          break;
      }
    }

    return () => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
    };
  }, [gamePhase, phaseCountdown]);

  const startNextQuestion = useCallback(() => {
    const newNumbers = generateNumbers({ config: updatedConfig });
    setNumbers(newNumbers);
    setCurrentNumberIndex(0);
    setIsFlashing(true);
    setUserAnswer('');
    setIncorrectAttempts(0);
    setIsInputWrong(false);
    setIsCorrect(false);
    setSolutionStatus(SOLUTION_STATUS.NOT_ANSWERED);
  }, [updatedConfig]);

  useEffect(() => {
    if (isFlashing && currentNumberIndex < numberCount) {
      const numberTimer = setTimeout(() => {
        setCurrentNumberIndex((prev) => prev + 1);
      }, flashSpeed);

      return () => {
        clearTimeout(numberTimer);
      };
    }
    if (isFlashing && currentNumberIndex >= numberCount) {
      setIsFlashing(false);
      setGamePhase(FLASH_ANZAN_GAME_PHASES.ANSWERING);
      setPhaseCountdown(answerTime);
    }
  }, [currentNumberIndex, isFlashing, numberCount, flashSpeed]);

  const submitAnswer = useCallback(
    async ({ answer }) => {
      if (isSubmittingAnswer) {
        return;
      }
      setIsSubmittingAnswer(true);
      const remainingTime = phaseCountdown;
      setPhaseCountdown(0);
      const correctAnswer = _reduce(numbers, (acc, num) => acc + num, 0);
      const isCorrect = _toNumber(answer) === correctAnswer;

      try {
        await submitFlashAnzanAnswer({
          gameId: game?._id,
          submittedValue: answer ?? userAnswer,
          timeOfSubmission: getCurrentTimeWithOffset(),
          isCorrect,
          questionIdentifier: getIdentifierStringFromConfig({
            config: updatedConfig,
            tag: updatedConfig?.includeSubstraction
              ? PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB
              : PRESET_CATEGORY.FLASH_ANZAN,
          }),
          maxScore: getMaxScoreOfFlashAnzanQueFromConfig({
            config: updatedConfig,
          }),
        });
        setIsSubmittingAnswer(false);
        if (currentQuestion < noOfQuestions) {
          setPhaseCountdown(configSelectionTime + remainingTime);
          setCurrentQuestion((prev) => prev + 1);
          setGamePhase(FLASH_ANZAN_GAME_PHASES.CONFIG);
        } else {
          setGamePhase(FLASH_ANZAN_GAME_PHASES.COMPLETED);
          setIsGameCompleted(true);
        }
      } catch (e) {
        setIsSubmittingAnswer(false);
      } finally {
        setIsSubmittingAnswer(false);
      }
    },
    [
      currentQuestion,
      noOfQuestions,
      configSelectionTime,
      userAnswer,
      game?._id,
      updatedConfig,
      numbers,
      userAnswer,
      phaseCountdown,
      isSubmittingAnswer,
    ],
  );

  const handleInputChange = useCallback(
    (text) => {
      setUserAnswer(text);

      const correctAnswer = _reduce(numbers, (acc, num) => acc + num, 0);
      if (_toString(text).length !== `${correctAnswer}`.length) {
        return;
      }

      const isCorrect = _toNumber(text) === correctAnswer;
      setIsCorrect(isCorrect);
      setIsInputWrong(!isCorrect);

      if (isCorrect) {
        setSolutionStatus(SOLUTION_STATUS.CORRECT);
      } else {
        setSolutionStatus(SOLUTION_STATUS.INCORRECT);
      }

      if (isCorrect && gamePhase === FLASH_ANZAN_GAME_PHASES.ANSWERING) {
        submitAnswer({ answer: text });
      }
    },
    [
      numbers,
      phaseCountdown,
      configSelectionTime,
      currentQuestion,
      noOfQuestions,
      gamePhase,
      submitAnswer,
      solutionStatus,
    ],
  );

  return {
    config: updatedConfig,
    gamePhase,
    phaseCountdown,
    isGameCompleted,
    isFlashing,
    isInputWrong,
    isCorrect,
    userAnswer,
    currentQuestion,
    currentNumberIndex,
    numbers,
    gameResults,
    updateConfig,
    setUserAnswer: handleInputChange,
    solutionStatus,
  };
};

export default useHandleFlashAnzanPlay;
