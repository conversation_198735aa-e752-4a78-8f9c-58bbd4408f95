import { useCallback, useEffect, useMemo, useRef } from 'react';
import { hideToast } from 'molecules/Toast';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _toNumber from 'lodash/toNumber';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import { useUserActivityContext } from 'core/contexts/UserActivityContext';
import { GAME_EVENTS, GAME_STATUS } from '../constants/game';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useUpdateCurrentUserOnGameEnd from './useUpdateCurrentUserOnGameEnd';
import { WEBSOCKET_EVENTS } from 'core/constants/events';
import useHandleGameEvents from './useHandleGameEvents';
import gameReader from '@/src/core/readers/gameReader';
import _includes from "lodash/includes"
import { WEBSOCKET_CHANNELS } from 'core/constants/websocket';
import useWebsocketStore from 'store/useWebSocketStore';

const GAME_END_TRIGGERED_FOR_GAME_IDS = {};

const useGameEventSubscription = ({ gameId }) => {
  const { updateUserOnGameEnd } = useUpdateCurrentUserOnGameEnd();
  const userActivityContextData = useUserActivityContext();
  const { updateActivity } = userActivityContextData ?? EMPTY_OBJECT;

  const updateUserOnGameEndRef = useRef(updateUserOnGameEnd);
  updateUserOnGameEndRef.current = updateUserOnGameEnd;

  const { isConnected, lastMessage, sendMessage, joinChannel, leaveChannel } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
    lastMessage: state.lastMessage,
    sendMessage: state.sendMessage,
    joinChannel: state.joinChannel,
    leaveChannel: state.leaveChannel,
  }));
  const channel = WEBSOCKET_CHANNELS.GameEvents(gameId);
  useEffect(() => {
    if (isConnected) {
      joinChannel(channel);
    }
  }, [isConnected]);

  const submitAnswer = (data) => {
    sendMessage({
      type: 'submitAnswer',
      channel,
      data: {
        ...data,
      },
    });
  };

  const { game, event } = useMemo(() => {
    const data = lastMessage?.[channel];
    const game = data?.game;
    const event = data?.event;
    return { event, game };
  }, [lastMessage?.[channel]]);

  const currentGameStatus = gameReader.gameStatus(game);

  const isActiveGame = !_includes([GAME_STATUS.ENDED, GAME_STATUS.CANCELLED], currentGameStatus)

  useEffect(() => {
    if (isConnected && isActiveGame) {
      joinChannel(channel);
    }
    if (!isActiveGame) {
      leaveChannel(channel);
    }
  }, [isConnected, isActiveGame]);

  const decryptedGame = useMemo(() => {
    if (_isEmpty(game)) {
      return game;
    }

    const { encryptedQuestions } = game ?? EMPTY_OBJECT;

    if (_isNil(encryptedQuestions)) {
      return game;
    }

    const questions = _map(encryptedQuestions, decryptJsonData);

    return { ...game, questions };
  }, [game]);

  const { handleUserJoinEvent, handleRemovePlayerEvent } =
    useHandleGameEvents();

  const handleUserJoinEventRef = useRef(handleUserJoinEvent);
  handleUserJoinEventRef.current = handleUserJoinEvent;

  const handleRemovePlayerEventRef = useRef(handleRemovePlayerEvent);
  handleRemovePlayerEventRef.current = handleRemovePlayerEvent;

  const updateActivityRef = useRef(updateActivity);
  updateActivityRef.current = updateActivity;

  const gameRef = useRef(game);
  gameRef.current = game;

  useEffect(() => {
    const { config, gameType } = game ?? EMPTY_OBJECT;
    const { timeLimit, numPlayers } = config ?? EMPTY_OBJECT;

    switch (event) {
      case GAME_EVENTS.USER_JOINED: {
        handleUserJoinEventRef.current({ game });
        break;
      }
      case GAME_EVENTS.GAME_STARTED: {
        Analytics.track(ANALYTICS_EVENTS.GAME_STARTED, {
          gameType,
          timeLimit,
          numPlayers,
        });
        hideToast();
        break;
      }
      case GAME_EVENTS.GAME_ENDED: {
        if (GAME_END_TRIGGERED_FOR_GAME_IDS[gameId]) {
          return;
        }
        GAME_END_TRIGGERED_FOR_GAME_IDS[gameId] = true;
        Analytics.track(ANALYTICS_EVENTS.GAME_ENDED, {
          gameType,
          timeLimit,
          numPlayers,
        });
        updateUserOnGameEndRef.current({ game: gameRef.current });
        updateActivityRef.current({
          activityType: `${gameType}`,
          duration: _toNumber(timeLimit) * 1000,
        });
        break;
      }
      case GAME_EVENTS.PLAYER_REMOVED: {
        handleRemovePlayerEventRef.current({ game });
        break;
      }
      default: {
        // do nothing
      }
    }
  }, [event]);

  return {
    event,
    game: decryptedGame,
    submitAnswer,
  };
};

export default useGameEventSubscription;
