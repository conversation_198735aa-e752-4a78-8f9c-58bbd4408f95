import { StyleSheet } from "react-native";
import dark from "core/constants/themes/dark";

const styles = StyleSheet.create({
    container: {
        paddingVertical: 16,
        paddingHorizontal: 12,
        backgroundColor: dark.colors.primary,
        borderRadius: 12,
        gap: 12,
        flexDirection: "row",
        marginBottom:12
    },
    shareInfoText: {
        fontFamily: "Montserrat-500",
        fontSize: 14,
        lineHeight: 20,
        color: "white",
        flexWrap:"wrap",
    },
    buttonText: {
        fontFamily: "Montserrat-600",
        fontSize: 13,
        lineHeight: 20,
        color: dark.colors.secondary
    },
    buttonContainer: {
        gap: 24,
        flexDirection: "row",
        alignItems: "center"
    },
    infoSection: {
        gap: 8
    },
    linkIconContainer: {
        height: 36,
        width: 36,
        borderRadius: 18,
        backgroundColor: dark.colors.tertiary,
        justifyContent:"center",
        alignItems:"center"
    },
    linkIcon: {
        height: 22,
        width: 22
    }
})

export default styles