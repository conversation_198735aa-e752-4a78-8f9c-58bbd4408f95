import PropTypes from 'prop-types';
import React, { useEffect, useRef } from 'react';
import { Animated, View } from 'react-native';
import { Text } from '@rneui/themed';
import DotAnimation from 'shared/DotAnimation';
import styles from './WaitingForAdminToStart.style';

const WaitingForAdminToStart = () => {
  const rotateValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        delay: 0,
        duration: 2000,
        useNativeDriver: true,
        isInteraction: true,
      }),
    );
    rotateAnimation.start();

    return () => rotateAnimation.stop();
  }, [rotateValue]);

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Animated.Text style={[styles.timer, { transform: [{ rotate }] }]}>
          ⏳
        </Animated.Text>
      </View>
      <Text style={styles.waitingText}>
        Waiting for Admin to Start Game...
        <DotAnimation />
      </Text>
    </View>
  );
};

WaitingForAdminToStart.propTypes = {
  game: PropTypes.object.isRequired,
};

export default React.memo(WaitingForAdminToStart);
