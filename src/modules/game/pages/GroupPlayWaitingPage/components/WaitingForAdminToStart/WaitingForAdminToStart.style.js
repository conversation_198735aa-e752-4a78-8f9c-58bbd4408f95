import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    maxHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timer: {
    fontSize: 36,
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  waitingText: {
    color: Dark.colors.textDark,
    fontSize: 18,
    margin: 16,
    textAlign: 'center',
  },
});

export default styles;
