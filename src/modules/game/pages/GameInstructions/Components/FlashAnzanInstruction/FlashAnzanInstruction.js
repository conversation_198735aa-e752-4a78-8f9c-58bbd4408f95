import React, { useCallback, useState } from 'react';
import { Dimensions, ScrollView, Text, View } from 'react-native';
import Instruction1Image from '@/assets/images/flashAnzan/instruction1.png';
import Instruction2Image from '@/assets/images/flashAnzan/instruction2.png';
import Instruction3Image from '@/assets/images/flashAnzan/instruction3.png';
import Instruction4Image from '@/assets/images/flashAnzan/instruction4.png';
import Instruction5Image from '@/assets/images/flashAnzan/instruction5.png';
import Instruction6Image from '@/assets/images/flashAnzan/instruction6.png';
import _map from 'lodash/map';
import LinearGradient from 'atoms/LinearGradient';
import Header from 'shared/Header';
import PrimaryButton from 'atoms/PrimaryButton';
import { useRouter } from 'expo-router';
import _isNil from 'lodash/isNil';
import useChallengeUser from '../../../../../friendsAndFollowers/hooks/mutations/useChallengeUser';
import { GAME_TYPES } from '../../../../../home/<USER>/gameTypes';
import useFlashAnzanInstructionStyles from './FlashAnzanInstruction.style';
import InfoCardWithImage from '../../../../../../components/shared/InfoCardWithImage';
import useMediaQuery from '../../../../../../core/hooks/useMediaQuery';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { stringifyQueryParams } from '@/src/core/utils/general';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import dark from '@/src/core/constants/themes/dark';
const FLASH_ANZAN_GAME_INSTRUCTION = [
  {
    image: Instruction6Image,
    description:
      'Both players have 7 seconds to choose their Flash Anzan settings.',
  },
  {
    image: Instruction5Image,
    description: `Each setting shows the maximum possible score you can earn for that round.`,
  },
  {
    image: Instruction4Image,
    description:
      "Your opponent's settings are hidden, so you won't know their choice.",
  },
];

const FLASH_ANZAN_ROUNDS_INSTRUCTION = [
  {
    image: Instruction1Image,
    description:
      'Numbers flash as per your settings. Correct answers earn full points; wrong answers score zero.',
  },
  {
    image: Instruction3Image,
    description: `For rounds 2 and 3, you'll have 7 seconds to set up and see your opponent's previous round score.`,
  },
];

const FLASH_ANZAN_WIN_INSTRUCTION = [
  {
    image: Instruction2Image,
    description:
      'Both players complete three rounds.The player with the highest total score at the end of the rounds wins!',
  },
];

const FlashAnzanInstructions = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const router = useRouter();
  const styles = useFlashAnzanInstructionStyles();
  const linearGradientColors = ['#1E1E1E', '#3A3A3A'];
  const { challengeUser } = useChallengeUser();

  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const onPressCreateLink = useCallback(async () => {
    try {
      if (isChallengingFriend) {
        return;
      }
      const response = await challengeUser({
        userId: null,
        gameConfig: {
          timeLimit: 95,
          numPlayers: 2,
          gameType: GAME_TYPES.FLASH_ANZAN,
        },
      });
      const { data } = response ?? EMPTY_OBJECT;
      const { challengeUser: challenegFriendResObj } = data ?? EMPTY_OBJECT;
      if (!_isNil(challenegFriendResObj?._id)) {
        router.push(`/game/${challenegFriendResObj?._id}/play`);
      }
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [isChallengingFriend, router, challengeUser]);

  const onPressFlashAnzan = useCallback(() => {
    const queryParams = {
      timeLimit: 1.5,
      gameType: GAME_TYPES.FLASH_ANZAN,
    };
    const stringifiedQueryParams = stringifyQueryParams(queryParams);
    router.replace(`/search?${stringifiedQueryParams}`);
  }, [router]);

  const renderSearchMatheleteButton = () => {
    return (
      <InteractiveSecondaryButton
        testID='searchMatheleteButton'
        label="Search Mathelete"
        onPress={onPressFlashAnzan}
        borderColor={dark.colors.green}
        buttonContainerStyle={[
          styles.buttonContainerStyle,
          !isCompactMode && { width: 162 },
        ]}
        labelStyle={styles.labelStyle}
        buttonStyle={styles.buttonStyle}
      />
    );
  };

  return (
    <View style={styles.mainContainer}>
      <Header title="Flash Anzan" />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <View style={[styles.headerRow, !isCompactMode && { marginTop: 25 }]}>
          <LinearGradient
            colors={linearGradientColors}
            style={styles.linearGradient}
          >
            <View style={styles.linearGradient} />
          </LinearGradient>
          <Text style={styles.headerText}>GAME START</Text>
          <LinearGradient
            colors={linearGradientColors}
            style={styles.linearGradient}
          />
        </View>
        <View style={[styles.infoItems, !isCompactMode && styles.infoItemsWeb]}>
          {_map(FLASH_ANZAN_GAME_INSTRUCTION, (instruction, index) => (
            <InfoCardWithImage
              description={instruction.description}
              numberOfLinesInDescription={3}
              infoImage={instruction.image}
              title={null}
              containerStyle={!isCompactMode ? { maxWidth: 328 } : null}
            />
          ))}
        </View>
        <View style={[styles.headerRow, { marginTop: 15 }]}>
          <LinearGradient
            colors={linearGradientColors}
            style={styles.linearGradient}
          />
          <Text style={styles.headerText}>ROUNDS</Text>
          <LinearGradient
            colors={linearGradientColors}
            style={styles.linearGradient}
          />
        </View>
        <View style={[styles.infoItems, !isCompactMode && styles.infoItemsWeb]}>
          {_map(FLASH_ANZAN_ROUNDS_INSTRUCTION, (instruction, index) => (
            <InfoCardWithImage
              description={instruction.description}
              infoImage={instruction.image}
              numberOfLinesInDescription={3}
              title={null}
              containerStyle={!isCompactMode ? { maxWidth: 328 } : null}
            />
          ))}
        </View>

        <View style={[styles.headerRow, { marginTop: 15 }]}>
          <LinearGradient
            colors={linearGradientColors}
            style={styles.linearGradient}
          />
          <Text style={styles.headerText}>WINNING THE GAME</Text>
          <LinearGradient
            colors={linearGradientColors}
            style={styles.linearGradient}
          />
        </View>
        <View style={[styles.infoItems, !isCompactMode && styles.infoItemsWeb]}>
          {_map(FLASH_ANZAN_WIN_INSTRUCTION, (instruction, index) => (
            <InfoCardWithImage
              description={instruction.description}
              infoImage={instruction.image}
              numberOfLinesInDescription={3}
              title={null}
              containerStyle={!isCompactMode ? { maxWidth: 328 } : null}
            />
          ))}
        </View>

        {!isCompactMode && (
          <View style={styles.buttonOuterContainerWeb}>
            {renderSearchMatheleteButton()}
          </View>
        )}
      </ScrollView>
      {isCompactMode && (
        <View style={styles.buttonOuterContainer}>
          {renderSearchMatheleteButton()}
        </View>
      )}
    </View>
  );
};

export default React.memo(FlashAnzanInstructions);
