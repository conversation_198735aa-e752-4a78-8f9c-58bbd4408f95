import React, { useCallback } from 'react';
import { StyleSheet, View } from 'react-native';
import DarkTheme from '@/src/core/constants/themes/dark';

import { showToast, TOAST_TYPE } from 'molecules/Toast';
import LinearGradientButton from '@/src/components/atoms/LinearGradientButton';
import useStartGameQuery from '../../../home/<USER>/useStartGameQuery';
import Analytics from '../../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../../core/analytics/const';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 50,
    marginHorizontal: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonBox: {
    width: '100%',
    maxWidth: 360,
    minWidth: 300,
    justifyContent: 'center',
    borderRadius: 8,
  },
  leaveGameStyle: {
    color: DarkTheme.colors.secondary,
    fontSize: 16,
    marginTop: 16,
    fontFamily: 'Montserrat-500',
  },
});

const StartGame = (props) => {
  const { game, config, gameType } = props;
  const { _id: gameId } = game;
  const { startGame } = useStartGameQuery();

  const onPressStartGame = useCallback(() => {
    startGame(gameId);
    Analytics.track(ANALYTICS_EVENTS.CLICK_START_GAME, {
      gameId,
      gameType,
      ...config,
    });
    showToast({
      type: TOAST_TYPE.LOADING,
      description: 'Starting Game',
    });
  }, [config, gameType, startGame, gameId]);

  return (
    <View style={styles.container}>
      <LinearGradientButton
        label="Start Game"
        onPress={onPressStartGame}
        buttonStyle={styles.buttonBox}
      />
    </View>
  );
};

export default React.memo(StartGame);
