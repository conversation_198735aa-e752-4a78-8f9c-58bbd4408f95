import React, { useCallback, useMemo, useState } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _toNumber from 'lodash/toNumber';
import _isEmpty from 'lodash/isEmpty';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import QuestionsRenderer from 'shared/QuestionsRenderer';
import { QUESTION_CATEGORIES } from 'core/constants/questionCategories';
import Header from './Header';
import Footer from './Footer';
import useGameQuestionsState from '../../hooks/useGameQuestionsState';
import styles from './PlayGame.style';
import Loading from '../../../../components/atoms/Loading';
import useGameWaitingTimer from '../../../../components/shared/game/hooks/useGameWaitingTimer';
import { GAME_TYPES } from '../../../home/<USER>/gameTypes';
import AbilityQuestion from './AbilityQuestion';
import { SecurityType } from '@/src/core/constants/wasm';

const keyboardAvoidingViewProps = {
  behavior: Platform.OS === 'ios' ? 'padding' : 'height',
  style: { flex: 1 },
  keyboardVerticalOffset: Platform.OS === 'ios' ? 48 : 36,
};

const PlayGame = ({ game }) => {
  const gameData = useMemo(() => {
    const { startTime, config, gameType } = game;
    const startTimeDate = new Date(startTime);
    const { timeLimit } = config;
    const currentTime = getCurrentTimeWithOffset();
    const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;
    const hasGameEnded = endTime <= currentTime;

    return {
      startTime,
      config,
      gameType,
      startTimeDate,
      timeLimit,
      currentTime,
      endTime,
      hasGameEnded,
    };
  }, [game]);

  const { gameType } = gameData;
  const { isMobile } = useMediaQuery();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const { isReady, renderQuestionOverlay } = useGameWaitingTimer({ game });

  const { currentQuestion, playersScores, submitAnswer } =
    useGameQuestionsState();

  const onGameEnded = useCallback(() => {
    setIsRedirecting(true);
  }, []);

  const footerComponent = useMemo(
    () => (
      <Footer
        question={currentQuestion}
        submitAnswer={submitAnswer}
        isGameActive={isReady}
        gameType={gameType}
      />
    ),
    [currentQuestion, submitAnswer, isReady, gameType],
  );

  const renderFooter = useCallback(
    () => <View style={[styles.footerContainer]}>{footerComponent}</View>,
    [footerComponent],
  );

  const questionView = useMemo(() => {
    if (game?.gameType === GAME_TYPES.ABILITY_DUELS) {
      return (
        <AbilityQuestion
          question={currentQuestion}
          renderQuestionOverlay={renderQuestionOverlay}
        />
      );
    }
    return (
      <QuestionsRenderer
        question={currentQuestion}
        renderQuestionOverlay={renderQuestionOverlay}
        category={QUESTION_CATEGORIES.DMAS}
        securityType={SecurityType.WebSecured}
        gameType={gameType}
      />
    );
  }, [game?.gameType, currentQuestion, renderQuestionOverlay]);

  if (_isEmpty(currentQuestion)) {
    return <Loading />;
  }

  return (
    <KeyboardAvoidingView {...keyboardAvoidingViewProps}>
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={[isMobile && styles.mobileHeader]}>
            <Header playersScores={playersScores} onGameEnded={onGameEnded} />
          </View>

          <View style={styles.question}>{questionView}</View>

          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
};

export default React.memo(PlayGame);
