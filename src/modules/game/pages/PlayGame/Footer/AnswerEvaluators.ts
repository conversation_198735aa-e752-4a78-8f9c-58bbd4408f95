import _toString from 'lodash/toString';
import _split from 'lodash/split';
import _map from 'lodash/map';
import _sortBy from 'lodash/sortBy';
import _sumBy from 'lodash/sumBy';
import _trim from 'lodash/trim';
import _join from 'lodash/join';
import _size from 'lodash/size';
import { ABILITY_QUESTION_CATEGORY } from 'core/constants/questionCategories';
import trimSpacesAndLeadingZeros from 'core/utils/stringUtils';

export const SOLUTION_STATUS = {
  CORRECT: 'correct',
  INCORRECT: 'incorrect',
  NOT_ANSWERED: 'not_answered',
};

const evaluatePrimeFactorization = (text, validAnswer) => {
  if (!text || !validAnswer) {
    return {
      processedValue: _toString(text),
      isCorrect: false,
      shouldCheck: false,
    };
  }
  const userFactors = _sortBy(_map(_split(text, ' '), Number));
  return {
    processedValue: userFactors,
    isCorrect:
      _size(userFactors) === _size(validAnswer) &&
      userFactors.every(
        (factor, index) => _toString(factor) === validAnswer[index],
      ),
    shouldCheck: _size(_trim(text)) >= _size(_join(validAnswer, ',')),
  };
};

const evaluateSumOfSquares = (text, validAnswer, expression) => {
  if (!text || !expression) {
    return {
      processedValue: [],
      isCorrect: false,
      shouldCheck: false,
    };
  }
  const userNumbers = _map(_split(text, ' '), Number);
  const sumOfSquares = _sumBy(userNumbers, (num) => num * num);
  const targetSum = Number(expression[0]);

  return {
    processedValue: userNumbers,
    isCorrect: sumOfSquares === targetSum,
    shouldCheck: _size(_trim(text)) >= targetSum,
  };
};

const evaluateDefault = (text, validAnswer) => {
  if (!text || !validAnswer) {
    return {
      processedValue: _toString(text),
      isCorrect: false,
      shouldCheck: false,
    };
  }
  return {
    processedValue: _toString(text),
    isCorrect:
      _toString(validAnswer[0]) === _toString(text) ||
      _toString(validAnswer[0]) === trimSpacesAndLeadingZeros(text),
    shouldCheck: _size(text) >= _size(validAnswer[0]),
  };
};

export const evaluateAnswer = (params) => {
  const { category, text, validAnswer, expression = [] } = params;
  switch (category) {
    case ABILITY_QUESTION_CATEGORY.PRIME_FACTORIZATION:
      return evaluatePrimeFactorization(text, validAnswer);
    case ABILITY_QUESTION_CATEGORY.SUM_OF_SQUARES:
      return evaluateSumOfSquares(text, validAnswer, expression);
    default:
      return evaluateDefault(text, validAnswer);
  }
};

export type EvaluationResult = {
  processedValue: number[] | string;
  isCorrect: boolean;
  shouldCheck: boolean;
};
