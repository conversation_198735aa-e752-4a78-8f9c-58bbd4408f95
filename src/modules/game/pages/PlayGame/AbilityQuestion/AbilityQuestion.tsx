import React, { useMemo } from 'react';
import { ImageBackground, StyleSheet, View } from 'react-native';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import { PRESET_IDENTIFIER_MAP } from 'core/constants/questionCategories';
import _split from 'lodash/split';
import QuestionRenderer from 'shared/QuestionsRenderer';
import { adaptAbilityQuestion } from 'modules/game/pages/PlayGame/AbilityQuestion/utils';
import questionBackgroundImage from 'assets/images/questionBackground.png';

const styles = StyleSheet.create({
  image: {
    flex: 1,
    width: '100%',
    maxWidth: 400,
    maxHeight: 400,
    height: '100%',
    resizeMode: 'cover',
    justifyContent: 'center',
  },
});

const AbilityQuestion = ({
  question = EMPTY_OBJECT,
  renderQuestionOverlay,
}) => {
  const { expression, tags, presetIdentifier } = question;

  const category = useMemo(() => {
    if (!_isNil(tags) && !_isEmpty(tags)) {
      return tags[0];
    }
    const preset = _split(presetIdentifier, '_')?.[0];
    return PRESET_IDENTIFIER_MAP[preset];
  }, [tags, presetIdentifier]);

  const questionWithCategory = {
    category,
    ...question,
  };

  const adaptedQuestion = adaptAbilityQuestion(questionWithCategory);

  if (_isNil(expression)) {
    return null;
  }

  return (
    <View style={{ flex: 1, justifyContent: 'center' }}>
      <ImageBackground source={questionBackgroundImage} style={styles.image}>
        <QuestionRenderer question={adaptedQuestion} />
        {renderQuestionOverlay?.()}
      </ImageBackground>
    </View>
  );
};

export default React.memo(AbilityQuestion);
