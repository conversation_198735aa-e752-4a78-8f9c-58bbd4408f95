import React from 'react';
import { StyleSheet } from 'react-native';
import _includes from 'lodash/includes';
import Questions<PERSON>enderer from 'shared/QuestionsRenderer';
import { QUESTION_CATEGORIES } from 'core/constants/questionCategories';
import dark from '../../../../../core/constants/themes/dark';
import { SecurityType } from '@/src/core/constants/wasm';

const styles = StyleSheet.create({
  image: {
    flex: 1,
    width: '100%',
    maxWidth: 400,
    maxHeight: 400,
    height: '100%',
    resizeMode: 'cover',
    justifyContent: 'center',
  },
  questionExpression: {
    fontSize: 24,
    lineHeight: 29,
    fontFamily: 'Montserrat-500',
    color: 'white',
  },
  expressionContainer: {
    gap: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  expressionRow: {
    flexDirection: 'row',
    gap: 8,
    height: 32,
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: 120,
    paddingRight: 36,
  },
  operatorContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  operator: {
    width: 24,
    minWidth: 20,
    maxWidth: 24,
    fontSize: 24,
    lineHeight: 29,
    textAlign: 'right',
    color: 'white',
  },
  numberContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  timerText: {
    fontSize: 20,
    color: 'white',
  },
  time: {
    fontSize: 24,
    fontFamily: 'Montserrat-700',
    color: dark.colors.stroke,
  },
});

const ALL_OPERATORS = ['+', '-', '×', '*', '÷'];
const isOperator = (operator) => _includes(ALL_OPERATORS, operator);

const Question = ({
  question,
  renderQuestionOverlay,
  category = QUESTION_CATEGORIES.DMAS,
  securityType = SecurityType.Normal,
}) => (
  <QuestionsRenderer
    question={question}
    renderQuestionOverlay={renderQuestionOverlay}
    category={category}
    securityType={securityType}
  />
);

export default React.memo(Question);
