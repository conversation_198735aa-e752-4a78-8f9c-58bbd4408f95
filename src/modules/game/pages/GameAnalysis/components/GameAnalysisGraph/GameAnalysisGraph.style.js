import { StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useMemo } from "react";
import dark from "core/constants/themes/dark";

const createStyles = (isCompactMode) => StyleSheet.create({
    sectionTitle: {
        fontSize: 10,
        fontFamily: "Montserrat-600",
        color: dark.colors.textDark,
        marginBottom: 5,
        letterSpacing: 1,
        lineHeight: 13,
        textAlign: "center"
    },
    highlightedText: {
        fontSize: 10,
        fontFamily: "Montserrat-400",
        // lineHeight: 9,
        color: 'white',
        marginBottom: 10,
        textAlign: "center"
    },
    noDataText: {
        fontFamily: "Montserrat-500",
        fontSize: 14,
        lineHeight: 20,
        color: dark.colors.textDark
    },
    chart: {
        marginVertical: 10,
        borderRadius: 8,
        backgroundColor: dark.colors.gradientBackground
    },
    section: {
        marginTop: 4,
        width: '100%'
    },
    detailedAnalysisButtonRow: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "flex-end",
        width: "100%"
    },
    detailedAnalysisButtonText:{
        color: dark.colors.secondary,
         fontSize: 13 ,
         fontFamily: "Montserrat-400",
    },
    detailedAnalysisButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 20,
        borderColor: dark.colors.tertiary,
        borderWidth: 1,
        flexDirection:"row",
        gap:5,
        justifyContent:"center",
        alignItems:"center"
    }
})

const useGameAnalysisGraphStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles

}

export default useGameAnalysisGraphStyles