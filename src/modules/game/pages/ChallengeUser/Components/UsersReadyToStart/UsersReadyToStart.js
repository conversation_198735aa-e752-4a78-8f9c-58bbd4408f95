import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import StartGame from '../../../../pages/StartGame';
import WaitingForGameCreatorToStart from '../../../../pages/WaitingToStart';
import { GAME_STATUS } from '../../../../constants/game';
import _size from 'lodash/size';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useRouter } from 'expo-router';
import Analytics from '../../../../../../core/analytics';
import { Text, Button } from '@rneui/themed';
import { ANALYTICS_EVENTS } from '../../../../../../core/analytics/const';
import styles from './UsersReadyToStart.style';
import {
  PAGE_NAME_KEY,
  PAGE_NAMES,
} from '../../../../../../core/constants/pageNames';
import GameLobbyPlayerCards from '../../../../components/GameLobbyPlayerCards';
import useHandleLeaveGame from '../../../../hooks/useHandleLeaveGame';

const UsersReadyToStart = (props) => {
  const { game } = props;

  const { players, createdBy, gameStatus, _id: gameId } = game ?? EMPTY_OBJECT;

  const router = useRouter();

  const { userId } = useSession();

  const isGameOwner = useMemo(
    () =>
      gameStatus === GAME_STATUS.READY &&
      _size(players) === 2 &&
      userId === createdBy,
    [gameStatus, players, createdBy, userId],
  );

  const { onPressLeaveGame, isCancellingGame } = useHandleLeaveGame({ gameId });

  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.lobbyContainer}>
            <GameLobbyPlayerCards game={game} />
          </View>
          {isGameOwner ? (
            <StartGame game={game} />
          ) : (
            <WaitingForGameCreatorToStart game={game} />
          )}
        </View>
      </View>
      <Button type="clear" onPress={onPressLeaveGame}>
        <Text style={styles.leaveGameStyle}>Leave game</Text>
      </Button>
    </View>
  );
};

export const UsersReadyToStartContainer = (props) => {
  const { game } = props;
  const { gameStatus } = game;

  if (gameStatus !== GAME_STATUS.READY) {
    return null;
  }

  return <UsersReadyToStart {...props} />;
};

export default UsersReadyToStartContainer;
