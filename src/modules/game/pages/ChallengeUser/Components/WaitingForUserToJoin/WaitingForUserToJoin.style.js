import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  timer: {
    fontSize: 36,
  },
  waitingText: {
    color: Dark.colors.textDark,
    fontSize: 18,
    margin: 16,
    textAlign: 'center',
  },
  linkButton: {
    backgroundColor: Dark.colors.primary,
    borderColor: 'transparent',
    borderWidth: 0,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  linkButtonContainer: {
    marginHorizontal: 16,
    marginVertical: 10,
    flexDirection: 'row',
  },
  inviteFriendButton: {
    margin: 16,
    width: 300,
  },
  shareLinkLabel: {
    marginTop: 24,
    marginHorizontal: 8,
    color: Dark.colors.textDark,
    fontSize: 16,
  },
  linkText: {
    fontSize: 12,
    fontFamily: 'Montserrat-300',
    color: Dark.colors.textDark,
  },
  cancel: {
    color: Dark.colors.textDark,
    fontSize: 16,
    marginTop: 48,
    fontFamily: 'Montserrat-500',
  },
});

export default styles;
