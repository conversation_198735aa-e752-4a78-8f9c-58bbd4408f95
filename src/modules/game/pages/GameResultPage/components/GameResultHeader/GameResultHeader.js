import React, { useCallback, useEffect, useRef } from 'react';
import { Animated, Platform, View } from 'react-native';
import dark from '@/src/core/constants/themes/dark';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import PropTypes from 'prop-types';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import _get from 'lodash/get';
import userReader from 'core/readers/userReader';
import { closePopover } from 'molecules/Popover/Popover';
import useGoBack from '@/src/navigator/hooks/useGoBack';
import { openShareableCardFlow } from 'shared/ShareResultModal';
import ResultShareCard from 'shared/ResultShareCard';
import useHandleGameLeaderboard from 'modules/game/hooks/useHandleGameLeaderboard';
import gameReader from 'core/readers/gameReader';
import victory from 'assets/images/game/victory.png';
import defeat from 'assets/images/game/defeat.png';
import tie from 'assets/images/game/tie.png';
import styles from './GameResultHeader.style';

const GameResultHeader = (props) => {
  const { isCurrentPlayerWinner, isMinifiedQuesAvailable } = props;

  const {
    players,
    game,
    player1,
    player2,
    adaptedPlayers,
    isCurrPlayerWinner,
    isMatchTied,
  } = useHandleGameLeaderboard();
  const { gameType } = game ?? EMPTY_OBJECT;

  const gameConfig = gameReader.config(game);
  const timeLimit = _get(gameConfig, ['timeLimit'], 60);

  const currentUserUsername = userReader.username(_get(players, [0]));
  const { goBack } = useGoBack();

  const linkToShare = `https://www.matiks.com/apps?utm_source=matiks_apps&utm_campaign=game_result_share&utm_referrer=${currentUserUsername}`;

  const { handleShare: handleShareLink } = useNativeUrlSharing({
    url: linkToShare,
  });

  const opponentPlayerUserName = userReader.username(_get(players, [1]));

  const renderGameResultSharableCard = useCallback(
    () => (
      <View>
        <ResultShareCard
          gameType={gameType}
          adaptedPlayers={adaptedPlayers}
          isCurrPlayerWinner={isCurrPlayerWinner}
          player1Score={player1?.score}
          player2Score={player2?.score}
          timeLimit={timeLimit}
        />
      </View>
    ),
    [
      adaptedPlayers,
      gameType,
      isCurrPlayerWinner,
      player1?.score,
      player2?.score,
      timeLimit,
    ],
  );

  const handleShareInNative = useCallback(
    ({ message = '' } = EMPTY_OBJECT) => {
      closePopover();
      openShareableCardFlow({
        renderResultCard: renderGameResultSharableCard,
        message,
        storyBackgroundColors: {
          backgroundBottomColor:
            dark.colors.game.share.storyBackgroundColorBottom,
          backgroundTopColor: dark.colors.game.share.storyBackgroundColorTop,
        },
      });
    },
    [renderGameResultSharableCard],
  );

  const handleShareGameResult = useCallback(() => {
    const player1Score = _get(adaptedPlayers, [0, 'score']);
    const player2Score = _get(adaptedPlayers, [1, 'score']);
    const label = isCurrentPlayerWinner
      ? `I beat ${opponentPlayerUserName} with a score of ${player1Score} - ${player2Score}. Think you can do better? Challenge me at`
      : `Well played ${opponentPlayerUserName}! 💪 Close match!. Wanna play a game with me? Challenge me at`;

    if (Platform.OS === 'web') {
      handleShareLink({ label });
      return;
    }
    handleShareInNative({ message: `${label} ${linkToShare}` });
  }, [
    linkToShare,
    handleShareInNative,
    handleShareLink,
    isCurrentPlayerWinner,
    opponentPlayerUserName,
    adaptedPlayers,
  ]);

  const imageOpacity = useRef(new Animated.Value(0.2)).current;

  useEffect(() => {
    Animated.timing(imageOpacity, {
      toValue: 1,
      duration: 800,
      delay: 200,
      useNativeDriver: true,
    }).start();
  }, [isCurrentPlayerWinner]);

  const getImageIcon = useCallback(() => {
    if (isMatchTied) {
      return tie;
    }
    if (isCurrentPlayerWinner) {
      return victory;
    }
    return defeat;
  }, [isCurrentPlayerWinner, isMatchTied]);

  return (
    <View style={[styles.container]}>
      <InteractiveSecondaryButton
        testID='close-popover'
        onPress={isMinifiedQuesAvailable ? closePopover : goBack}
        iconConfig={{
          type: ICON_TYPES.MATERIAL_ICONS,
          name: 'close',
          size: 16,
          color: dark.colors.textLight,
        }}
        buttonContainerStyle={{ width: 40, height: 40 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
      <View
        style={[styles.imageContainer, isCurrentPlayerWinner && { width: 180 }]}
      >
        <Animated.Image
          source={getImageIcon()}
          style={[styles.image, { opacity: imageOpacity }]}
          resizeMode="contain"
        />
      </View>
      <InteractiveSecondaryButton
        onPress={handleShareGameResult}
        iconConfig={{
          type: ICON_TYPES.ENTYPO,
          name: 'share',
          size: 16,
          color: dark.colors.textLight,
        }}
        buttonContainerStyle={{ width: 40, height: 40 }}
        buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
      />
    </View>
  );
};

GameResultHeader.propTypes = {
  isCurrentPlayerWinner: PropTypes.bool,
  gameId: PropTypes.string,
};

export default GameResultHeader;
