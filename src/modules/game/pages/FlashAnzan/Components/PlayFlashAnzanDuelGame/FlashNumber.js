import React, { useEffect } from 'react';
import { Animated, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import fa_audio from '@/assets/audio/fa_audio.wav';
import _isNil from 'lodash/isNil';
import useSound from 'core/hooks/useSound';

const styles = StyleSheet.create({
  flashNumber: {
    fontSize: 96,
    fontFamily: 'Montserrat-600',
    color: 'white',
    lineHeight: 117,
  },
});

const FlashNumber = ({ number }) => {
  const { playSound } = useSound({
    soundFile: fa_audio,
  });

  useEffect(() => {
    if (!_isNil(number)) {
      playSound();
    }
  }, [number, playSound]);

  return <Animated.Text style={styles.flashNumber}>{number}</Animated.Text>;
};

FlashNumber.propTypes = {
  number: PropTypes.number.isRequired,
};

export default React.memo(FlashNumber);
