import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { View } from 'react-native';
import _size from 'lodash/size';
import _toString from 'lodash/toString';
import _get from 'lodash/get';
import useMediaQuery from 'core/hooks/useMediaQuery';
import CustomTextInput from 'shared/CustomTextInput';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _isEmpty from 'lodash/isEmpty';
import dark from 'core/constants/themes/dark';
import { KEYBOARD_TYPES } from 'shared/CustomKeyboard';
import trimSpacesAndLeadingZeros from 'core/utils/stringUtils';
import styles from './Footer.style';
import { SOLUTION_STATUS } from '../../PlayGame/Footer/AnswerEvaluators';

const Footer = ({
  question = EMPTY_OBJECT,
  submitAnswer,
  isGameActive = true,
  startTime,
  handleForceQuestionSubmission,
}) => {
  const [value, setValue] = useState('');
  const [isIncorrect, setIsIncorrect] = useState(false);
  const [solutionStatus, setSolutionStatus] = useState(null);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const { id: questionId, answers } = question;
  const validAnswer = answers?.[0];

  const inputRef = useRef(null);

  const { isMobile, isMobileBrowser } = useMediaQuery();

  const maxTimeLimitOfCurrQuestion = useMemo(
    () => _get(question, 'maxTimeLimit', 0) * 1000,
    [question],
  );

  const focusInput = useCallback(() => {
    if (inputRef.current && !hasSubmitted) {
      inputRef.current?.focus?.();
    }
  }, [inputRef, hasSubmitted]);

  const editable = isGameActive && !hasSubmitted;

  const onChangeText = useCallback(
    (inputText) => {
      const trimmedText = _toString(trimSpacesAndLeadingZeros(inputText));

      if (editable) {
        setValue(inputText);
        const currTime = getCurrentTimeWithOffset();
        if (
          _toString(validAnswer) === _toString(inputText) ||
          _toString(validAnswer) === _toString(trimmedText)
        ) {
          submitAnswer({
            questionId,
            value: _toString(trimmedText),
            timeTaken: currTime - startTime,
          });
          setIsIncorrect(false);
          setSolutionStatus(SOLUTION_STATUS.CORRECT);
          setHasSubmitted(true);
        } else if (_size(inputText) >= _size(validAnswer)) {
          setIsIncorrect(true);
          setSolutionStatus(SOLUTION_STATUS.INCORRECT);
        } else {
          setSolutionStatus(null);
          setIsIncorrect(false);
        }
      }
    },
    [startTime, submitAnswer, validAnswer, questionId, editable],
  );

  const onPressClearAll = useCallback(() => {
    if (!hasSubmitted) {
      onChangeText('');
      focusInput();
    }
  }, [onChangeText, focusInput, hasSubmitted]);

  useEffect(() => {
    if (isGameActive) {
      setValue('');
      setIsIncorrect(false);
      setHasSubmitted(false);
      setSolutionStatus(null);
      setTimeout(focusInput, 100);
    }
  }, [question, isGameActive]);

  useEffect(() => {
    if (!isGameActive) {
      return;
    }

    const timeoutQuestionId = questionId;
    const timeoutId = setTimeout(() => {
      if (!hasSubmitted) {
        handleForceQuestionSubmission(timeoutQuestionId);
        setHasSubmitted(true);
      }
    }, maxTimeLimitOfCurrQuestion);

    return () => clearTimeout(timeoutId);
  }, [startTime, isGameActive, questionId, hasSubmitted]);

  useEffect(() => {
    if (!hasSubmitted) {
      inputRef.current?.focus?.();
    }
  }, [hasSubmitted]);

  const setInputRef = useCallback((ref) => {
    inputRef.current = ref;
  }, []);

  const inputProps = {
    value,
    style: styles.inputStyle,
    placeholder: 'Enter Answer',
    keyboardAppearance: 'dark',
    placeholderTextColor: dark.colors.inputPlaceholder,
    onFocus: focusInput,
    solutionStatus,
    customKeyboard: isMobile,
    onChangeText,
    keyboardType: 'number-pad',
    customKeyboardType: KEYBOARD_TYPES.NUMBERS,
    autoFocus: true,
    editable: true,
  };

  return (
    <View style={styles.container}>
      <CustomTextInput {...inputProps} />
    </View>
  );
};

const FooterContainer = (props) => {
  const { question } = props;

  if (_isEmpty(question)) return null;

  return <Footer {...props} />;
};

export default React.memo(FooterContainer);
