import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {KeyboardAvoidingView, Platform, Text, View} from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _get from 'lodash/get';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import Header from './Header';
import Footer from './Footer';
import Question from './Question';
import styles from './GroupGamePlay.style';
import useGameWaitingTimer from 'shared/game/hooks/useGameWaitingTimer';
import useGroupPlayQuestionState from '../../hooks/useGroupPlayQuestionState';
import LeaderboardInBetweenGame from './LeaderboardInBetweenGame';
import PointGainedInCurrRoundInfo from './PointGainedInCurrRoundInfo';
import dark from '@/src/core/constants/themes/dark';
import WaitingPhaseTimer from './WaitingPhaseTimer';
import Dark from "core/constants/themes/dark";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

const WAITING_TIME = 5000;

const formatTime = (ms) => {
  const seconds = Math.ceil(ms / 1000);
  return `${seconds}`;
};

const GROUP_PLAY_GAME_PHASES = {
  QUESTION_PHASE: "QUESTION_PHASE",
  WAITING_PHASE: "WAITING_PHASE",
  INITIAL_WAITING_PHASE: "INITIAL_WAITING_PHASE"
}

const GroupGamePlay = ({game}) => {
  const {startTime, config, players, userSubmissionsWithQuestion} = game ?? EMPTY_OBJECT
  const gameStartTime = new Date(startTime).getTime();
  const {timeLimit} = config;

  const {
    currentQuestion, currentQuestionId, submitAnswer, handleForceQuestionSubmission,
    currentQuestionIndex,
    allQuestionsCount
  } = useGroupPlayQuestionState();

  const timePerQuestion = useMemo(() => _get(config, 'maxTimePerQuestion', 10) * 1000, [config]);

  const currentTime = getCurrentTimeWithOffset();

  const {isMobile} = useMediaQuery();

  const {isReady, renderQuestionOverlay: renderWaitingTimeOverlay} = useGameWaitingTimer({game});

  const calculateCurrentCycle = useCallback(() => {
    const timeSinceStart = currentTime - gameStartTime;
    const cycleTime = timePerQuestion + WAITING_TIME;
    return Math.floor(timeSinceStart / cycleTime);
  }, [currentTime, gameStartTime, timePerQuestion]);

  const calculateTimeRemainingInQuestion = useCallback(() => {
    if (currentTime < gameStartTime) {
      return timePerQuestion;
    }

    const cycleTime = timePerQuestion + WAITING_TIME;
    const currentCycle = calculateCurrentCycle();
    const currentQuestionStartTime = gameStartTime + (currentCycle * cycleTime);
    const timeIntoCurrentPhase = currentTime - currentQuestionStartTime;

    if (timeIntoCurrentPhase < timePerQuestion) {
      return timePerQuestion - timeIntoCurrentPhase;
    }

    return 0;
  }, [currentTime, gameStartTime, timePerQuestion, calculateCurrentCycle]);

  const [timeLeft, setTimeLeft] = useState(calculateTimeRemainingInQuestion());

  const getPhase = useCallback(() => {
    if (!isReady) {
      return GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE
    }

    const cycleTime = timePerQuestion + WAITING_TIME;
    const currentCycle = calculateCurrentCycle();
    const currentQuestionStartTime = gameStartTime + (currentCycle * cycleTime);
    const timeIntoCurrentPhase = currentTime - currentQuestionStartTime;

    if (timeIntoCurrentPhase < timePerQuestion) {
      return GROUP_PLAY_GAME_PHASES.QUESTION_PHASE;
    }
    return GROUP_PLAY_GAME_PHASES.WAITING_PHASE;
  }, [currentTime, gameStartTime, timePerQuestion, calculateCurrentCycle]);

  const [currentPhase, setCurrentPhase] = useState(getPhase());
  const [lastAnsweredQuestion, setLastAnsweredQuestion] = useState(null);

  useEffect(() => {
    const timer = setInterval(() => {
      const newPhase = getPhase();
      const remaining = calculateTimeRemainingInQuestion();
      if(currentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE && 
        newPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE){
          setLastAnsweredQuestion(currentQuestion);
        }

      setCurrentPhase(newPhase);

      setTimeLeft(remaining);
    }, 100);

    return () => clearInterval(timer);
  }, [getPhase, calculateTimeRemainingInQuestion, currentPhase, currentQuestion]);

  const renderQuestionOverlay = useCallback(() => {
    if (currentPhase === GROUP_PLAY_GAME_PHASES.INITIAL_WAITING_PHASE) {
      return renderWaitingTimeOverlay?.();
    }

    if (currentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE) {
      const questionToShowAnswer = lastAnsweredQuestion || currentQuestion;
      return (
        <View style={[styles.overlay]}>
          {/* <WaitingPhaseTimer /> */}
          <Text style={{fontFamily: "Montserrat-600", fontSize: 14, color: dark.colors.textDark, textAlign: "center"}}>
            The Answer Was <Text style={{color: dark.colors.secondary}}>{questionToShowAnswer?.answers?.[0]}</Text>
          </Text>
          <PointGainedInCurrRoundInfo currentQuestionId={currentQuestionId}/>
        </View>
      );
    }

    return null;
  }, [currentPhase, renderWaitingTimeOverlay, currentQuestionId, currentQuestion]);

  const renderTimerSection = useCallback(() => {
    if (currentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE) {
      return (<View style={{flexDirection: 'row', gap: 4}}>
        <MaterialIcons name={'timer'} color={Dark.colors.textDark} size={20}/>
        <Text style={[styles.timerText]}>
          {formatTime(timeLeft)}
        </Text>
      </View>)
    }
    if (currentPhase === GROUP_PLAY_GAME_PHASES.WAITING_PHASE) {
      return <WaitingPhaseTimer/>
    }
  }, [currentPhase, currentQuestion, timeLeft])

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer
          question={currentQuestion}
          submitAnswer={submitAnswer}
          isGameActive={currentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE}
          startTime={gameStartTime}
          handleForceQuestionSubmission={handleForceQuestionSubmission}
          timePerQuestion={timePerQuestion}
        />
      </View>
    ),
    [
      currentQuestion,
      submitAnswer,
      currentPhase,
      gameStartTime,
      handleForceQuestionSubmission,
      timePerQuestion,
    ],
  );

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{flex: 1}}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}>
        <LeaderboardInBetweenGame
          currentQuestionId={currentQuestionId}
        />
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={styles.mobileHeader}>
            <Header currentQuestionId={currentQuestionId}/>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              maxWidth: 420,
              paddingHorizontal: 16,
            }}>
              <View style={{flex: 1, flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'}}>
                <Text style={{fontSize: 14, fontFamily: 'Montserrat-500', color: dark.colors.textDark}}>
                  {`${currentQuestionIndex + 1} / ${allQuestionsCount}`}
                </Text>
              </View>
              <View style={{flex: 1.5, flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                {renderTimerSection()}
              </View>
              <View style={{flex: 1, flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}/>
            </View>

          </View>

          <View style={[styles.question, {position: 'relative'}]}>
            <Question
              question={currentQuestion}
              isVisible={currentPhase === GROUP_PLAY_GAME_PHASES.QUESTION_PHASE}
              renderQuestionOverlay={renderQuestionOverlay}
            />
          </View>

          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
};

export default React.memo(GroupGamePlay);