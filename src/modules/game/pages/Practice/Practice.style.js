import { StyleSheet } from 'react-native'
import Dark from '@/src/core/constants/themes/dark'

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: Dark.colors.background,
    },
    container: {
        flex: 1,
        flexDirection: 'column',
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        backgroundColor: Dark.colors.background,
    },
    header: {
        width: '100%',
        alignItems: 'center',
        marginBottom: 0,
    },
    timerContainer: {
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 8,
    },
    timerContentContainer: {
        width: '100%',
        height: 90,
    },
    gameTimeContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    gameTime: {
        marginTop: 8,
        fontSize: 22,
        color: 'white',
        fontFamily: 'Montserrat-600',
    },
    question: {
        flex: 1,
        width: '100%',
        paddingHorizontal: 16,
        maxWidth: 420,
        maxHeight: 400,
    },
    footerContainer: {
        width: '100%',
        maxWidth: 420,
    },
    leaveGameStyle: {
        color: Dark.colors.textDark,
        fontSize: 16,
        marginTop: 16,
        fontFamily: 'Montserrat-500',
    },
})

export default styles
