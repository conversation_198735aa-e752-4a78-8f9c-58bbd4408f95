import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import _size from 'lodash/size';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useRouter } from 'expo-router';
import { But<PERSON>, Text } from '@rneui/themed';
import PropTypes from 'prop-types';
import styles from './ReadyToStart.style';
import { GAME_STATUS } from '../../../../constants/game';
import WaitingForGameCreatorToStart from '../../../WaitingToStart';
import StartGame from '../../../StartGame';
import GameLobbyPlayerCards from '../../../../components/GameLobbyPlayerCards';
import useHandleLeaveGame from '../../../../hooks/useHandleLeaveGame';

const PlayWithFriendReadyToStart = (props) => {
  const { game } = props;
  const { players, createdBy, gameStatus, _id: gameId } = game ?? EMPTY_OBJECT;
  const router = useRouter();

  const { userId } = useSession();

  const isGameOwner = useMemo(
    () =>
      gameStatus === GAME_STATUS.READY &&
      _size(players) === 2 &&
      userId === createdBy,
    [gameStatus, userId, players, createdBy],
  );

  const { onPressLeaveGame, isCancellingGame } = useHandleLeaveGame({ gameId });

  const renderStartButtonOrWaitingAnimatedText = useCallback(() => {
    if (isGameOwner) {
      return <StartGame game={game} />;
    }
    return <WaitingForGameCreatorToStart game={game} />;
  }, [game, isGameOwner]);

  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <GameLobbyPlayerCards game={game} />
          {renderStartButtonOrWaitingAnimatedText()}
        </View>
      </View>
      <Button type="clear" onPress={onPressLeaveGame}>
        <Text style={styles.leaveGameStyle}>Leave game</Text>
      </Button>
    </View>
  );
};

PlayWithFriendReadyToStart.propTypes = {
  game: PropTypes.object,
};

export default React.memo(PlayWithFriendReadyToStart);
