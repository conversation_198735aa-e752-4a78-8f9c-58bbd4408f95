import React from 'react';
import _get from 'lodash/get';
import { Image, View } from 'react-native';
import { Text } from '@rneui/themed';
import rec1 from '@/assets/images/Rectangle5.png';
import rec2 from '@/assets/images/Rectangle6.png';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _isEmpty from 'lodash/isEmpty';
import UserImage from '../../../../components/atoms/UserImage';
import { mobileStyles, webStyles } from './UserCard.style';
import EmptyUserCard from './EmptyUserCard';
import userReader from '../../../../core/readers/userReader';
import { GAME_TYPES } from '../../../../core/constants/gameTypes';

const UserCard = ({ user = EMPTY_OBJECT, gameType }) => {
  const { name, rating } = user;
  const { user: currentUser } = useSession();
  const isCurrentUser = user?._id === currentUser?._id;
  const { isMobile } = useMediaQuery();

  let userRating = _get(user, 'rating', 0);
  if (gameType === GAME_TYPES.FLASH_ANZAN) {
    userRating = _get(user, ['ratingV2', 'flashAnzanRating'], 0);
  }

  const styles = isMobile ? mobileStyles : webStyles;

  if (_isEmpty(user)) {
    return <EmptyUserCard showAnimation styles={styles} />;
  }

  return (
    <View style={styles.background}>
      <Image
        source={rec1}
        style={[styles.image1, !isCurrentUser && { opacity: 0 }]}
      />
      <Image
        source={rec2}
        style={[styles.image2, !isCurrentUser && { opacity: 0 }]}
      />
      <View style={styles.card}>
        <UserImage user={user} size={44} />
        <View style={styles.userInfo}>
          <Text style={styles.userName} numberOfLines={1}>
            {isCurrentUser ? 'You' : userReader.username(user) ?? name}
          </Text>
          <Text style={styles.userRating}>{userRating}</Text>
        </View>
      </View>
    </View>
  );
};

export default React.memo(UserCard);
