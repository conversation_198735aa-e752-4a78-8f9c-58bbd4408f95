import { useCallback, useMemo, useState } from 'react';
import _get from 'lodash/get';
import { GAME_MODE_VS_GAME_TYPES } from '../constants/gameModes';
import {
  DEFAULT_TIME_CONFIGS,
  GAME_TYPES_TIME_CONFIG,
} from '../constants/gameTimeConfig';
import { GAME_CATEGORIES } from 'modules/home/<USER>/gameTypes';

const useCreateLobbyConfig = () => {
  const [gameMode, setGameMode] = useState(GAME_CATEGORIES.BLITZ);
  const [gameType, setGameType] = useState(
    _get(GAME_MODE_VS_GAME_TYPES, [GAME_CATEGORIES.BLITZ, 0]),
  );
  const [timeConfig, setTimeConfig] = useState(
    _get(GAME_TYPES_TIME_CONFIG, [gameType, 0, 'key'], 1),
  );

  const updateGameMode = useCallback((newMode) => {
    setGameMode(newMode);
    const newGameType = _get(GAME_MODE_VS_GAME_TYPES, [newMode, 0]);
    setGameType(newGameType);
    const newTimeConfig = _get(
      GAME_TYPES_TIME_CONFIG,
      [newGameType, 0, 'key'],
      1,
    );
    setTimeConfig(newTimeConfig);
  }, []);

  const updateGameType = useCallback((newGameType) => {
    setGameType(newGameType);
  }, []);

  const updateTimeConfig = useCallback((value) => {
    setTimeConfig(value);
  }, []);

  const gameTypeOptions = GAME_MODE_VS_GAME_TYPES[gameMode] ?? EMPTY_ARRAY;

  const timeConfigOptions = useMemo(
    () => GAME_TYPES_TIME_CONFIG[gameType] || DEFAULT_TIME_CONFIGS,
    [gameType],
  );

  return {
    gameMode,
    gameType,
    timeConfig,
    timeConfigOptions,
    updateGameMode,
    updateGameType,
    updateTimeConfig,
    gameTypeOptions,
  };
};

export default useCreateLobbyConfig;
