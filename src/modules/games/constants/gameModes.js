import { GAME_CATEGORIES, GAME_TYPES } from 'modules/home/<USER>/gameTypes';
import blitzIcon from '@/assets/images/icons/blitz_home.png';
import classicIcon from '@/assets/images/icons/classic_home.png';
import memoryIcon from '@/assets/images/icons/memory.png';
import playAFriendIcon from '@/assets/images/icons/play_with_friend.png';
import blitzDisabled from '@/assets/images/icons/blitz_disabled.png';
import classicDisabled from '@/assets/images/icons/classic_disabled.png';
import memoryDisabled from '@/assets/images/icons/memory_disabled.png';
import playFriendDisabled from '@/assets/images/icons/play_friend_disabled.png';

export const GAME_MODE = {
  BLITZ: GAME_CATEGORIES.BLITZ,
  CLASSICAL: GAME_CATEGORIES.CLASSIC,
  MEMORY: GAME_CATEGORIES.MEMORY,
  VS_FRIEND: 'VS_FRIEND',
};

export const gameModes = [
  {
    key: GAME_MODE.BLITZ,
    title: GAME_CATEGORIES.BLITZ,
    icon: blitzIcon,
    iconDisabled: blitzDisabled,
  },
  {
    key: GAME_MODE.CLASSICAL,
    title: GAME_CATEGORIES.CLASSIC,
    icon: classicIcon,
    iconDisabled: classicDisabled,
  },
  {
    key: GAME_MODE.MEMORY,
    title: GAME_CATEGORIES.MEMORY,
    icon: memoryIcon,
    iconDisabled: memoryDisabled,
  },
  {
    key: GAME_MODE.VS_FRIEND,
    title: 'VS FRIEND',
    icon: playAFriendIcon,
    iconDisabled: playFriendDisabled,
  },
];

export const modeRatingLabels = {
  [GAME_MODE.BLITZ]: 'BLITZ RATING',
  [GAME_MODE.CLASSICAL]: 'CLASSICAL RATING',
  [GAME_MODE.MEMORY]: 'MEMORY RATING',
  [GAME_MODE.VS_FRIEND]: 'Vs Friend Rating',
};

export const BLITZ_GAME_TYPES = [
  GAME_TYPES.PLAY_ONLINE,
  GAME_TYPES.FASTEST_FINGER,
];

export const MEMORY_GAME_TYPES = [GAME_TYPES.FLASH_ANZAN];

export const CLASSICAL_GAME_TYPES = [GAME_TYPES.ABILITY_DUELS];

export const GAME_MODE_VS_GAME_TYPES = {
  [GAME_CATEGORIES.BLITZ]: [GAME_TYPES.PLAY_ONLINE, GAME_TYPES.FASTEST_FINGER],
  [GAME_CATEGORIES.CLASSIC]: CLASSICAL_GAME_TYPES,
  [GAME_CATEGORIES.MEMORY]: MEMORY_GAME_TYPES,
  [GAME_CATEGORIES.PUZZLE]: [GAME_TYPES.PUZZLE_DUELS],
};

export const GAME_TYPES_VS_LABEL = {
  [GAME_TYPES.PLAY_ONLINE]: 'PLAY ONLINE',
  [GAME_TYPES.FASTEST_FINGER]: 'FASTEST FINGER',
  [GAME_TYPES.ABILITY_DUELS]: 'ABILITY DUELS',
  [GAME_TYPES.FLASH_ANZAN]: 'FLASH ANZAN',
  [GAME_TYPES.PUZZLE_DUELS]: 'CROSS MATH',
};
