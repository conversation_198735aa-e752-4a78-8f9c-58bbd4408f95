import { StyleSheet } from 'react-native'
import dark from 'core/constants/themes/dark'

const styles = StyleSheet.create({
  infoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 15,
    padding: 20,
    borderWidth: 2,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },
  iconContainer: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    borderRadius: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
  },
  infoItem: {
    display: 'flex',
    gap: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  info: {
    gap: 4,
    display: 'flex',
    alignContent: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  infoText: {
    fontFamily: 'Montserrat-500',
    fontSize: 12,
    lineHeight: 12,
    color: dark.colors.textDark,
  },
  infoTextMobile: {
    fontSize: 11,
    lineHeight: 12,
  },
  infoNumber: {
    lineHeight: 16,
    color: 'white',
    fontSize: 14,
    marginTop: 5,
  },
  infoNumberMobile: {
    fontSize: 12,
    lineHeight: 12,
  },

})

export default styles
