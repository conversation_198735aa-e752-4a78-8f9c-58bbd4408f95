/* eslint-disable react/require-default-props */

import React from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import Ionicons from '@expo/vector-icons/Ionicons'
import HtmlRenderer from 'atoms/HtmlRenderer'
import PropTypes from 'prop-types'
import styles from './CompactShowdownDetail.style'
import dark from '../../../../core/constants/themes/dark'

const ExpandableSection = ({ title, expanded, onToggle, content }) => (
  <View style={styles.expandableContainer}>
    <TouchableOpacity style={styles.expandableHeader} onPress={onToggle}>
      <Text style={styles.expandableText}>{title}</Text>
      <Ionicons
        name={expanded ? 'chevron-up-outline' : 'chevron-down-outline'}
        size={15}
        color={dark.colors.secondary}
      />
    </TouchableOpacity>
    {expanded && (
      <View style={styles.expandedContent}>
        <HtmlRenderer html={content} />
      </View>
    )}
  </View>
)

ExpandableSection.propTypes = {
  title: PropTypes.string,
  expanded: PropTypes.bool,
  onToggle: PropTypes.func,
  // eslint-disable-next-line react/forbid-prop-types
  // content: PropTypes.string,
}

export default React.memo(ExpandableSection)
