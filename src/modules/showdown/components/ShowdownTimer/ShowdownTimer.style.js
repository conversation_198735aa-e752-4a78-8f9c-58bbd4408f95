import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  infoItem: {
    display: 'flex',
    gap: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    borderRadius: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
  },
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    padding: 20,
  },

  infoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 15,
    padding: 20,
    borderWidth: 2,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },
  info: {
    gap: 4,
    display: 'flex',
    alignContent: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  infoText: {
    fontFamily: 'Montserrat-500',
    lineHeight: 12,
    fontSize: 12,
    color: dark.colors.textDark,
    // marginTop: 5,
  },
  icon: {
    fontSize: 24,
    color: '#FFF',
  },
  infoNumber: {
    lineHeight: 14,
    color: '#FFF',
    fontSize: 13.5,
    marginTop: 5,
  },
  infoNumberMobile: {
    lineHeight: 12,
    color: '#FFF',
    fontSize: 12,
    marginTop: 5,
  },
});

export default styles;
