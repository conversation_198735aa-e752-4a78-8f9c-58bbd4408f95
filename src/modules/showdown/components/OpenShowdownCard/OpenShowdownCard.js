import React from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import Entypo from '@expo/vector-icons/Entypo'
import dark from 'core/constants/themes/dark'
import UserImage from 'atoms/UserImage/UserImage'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import useRequestCardStyles from './OpenShowdownCard.style'

// eslint-disable-next-line react/prop-types
function OpenShowdownCard({ user }) {
    const styles = useRequestCardStyles()

    // const [isVisible, setIsVisible] = useState(true);
    // const [isLoading, setIsLoading] = useState(false);

    // if (!isVisible) return null;

    return (
        <View style={styles.requestCard}>
            <View style={styles.container}>
                <UserImage
                    size={34}
                    user={user}
                    style={styles.image}
                    rounded={false}
                />
                <Text style={{ color: 'white', fontFamily: 'Montserrat-500' }}>
                    {/* {`${userReader.username(user)} has requested you for a rematch.`} */}
                    Test Config
                </Text>
            </View>
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={styles.button}
                    // onPress={handleReject}
                    // disabled={isLoading}
                >
                    <Entypo name="cross" size={20} color={dark.colors.error} />
                    <Text style={styles.rejectText}>Close</Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.button}
                    // onPress={handleAccept}
                    // disabled={isLoading}
                >
                    {/* <AntDesign
                        name="check"
                        size={20}
                    /> */}
                    <MaterialIcons
                        name="open-in-new"
                        size={20}
                        color={dark.colors.success}
                    />
                    <Text style={styles.acceptText}>Show</Text>
                </TouchableOpacity>
            </View>
        </View>
    )
}

export default React.memo(OpenShowdownCard)
