import _isEmpty from 'lodash/isEmpty';
import { SHOWDOWN_STATUS } from '../constants';
import showdownReader from '../readers/showdownReader';
import { useSession } from '../../auth/containers/AuthProvider';
import userReader from '../../../core/readers/userReader';

const useShowdownCurrentStatus = ({ state }) => {
  const { user } = useSession();
  const isGuest = userReader.isGuest(user);
  const { showdown, hasEnded, isLive, hasOpponentNotShown, hasUserGotBye } =
    state;

  const hasUserRegistered = !_isEmpty(
    showdownReader.currentUserParticipation(showdown),
  );

  const canParticipateInTournament =
    userReader.canParticipateInTournaments(user);

  const shouldShowRegisterButton =
    !hasEnded &&
    !isLive &&
    !hasUserRegistered &&
    showdown?.status === SHOWDOWN_STATUS.REGISTRATION_OPEN;

  const canUserWithdrawRegistration = hasUserRegistered && !hasEnded && !isLive;

  const showLockedRegisterButtonForGuest =
    !canParticipateInTournament && !hasEnded;
  const contestIsLiveAndNotEnded = !hasEnded && isLive;
  const contestIsLiveAndUserNotRegistered =
    !hasUserRegistered && isLive && !hasEnded;

  const contestNotStarted = !isLive && !hasEnded;

  return {
    isGuest,
    hasUserRegistered,
    contestNotStarted,
    shouldShowRegisterButton,
    canUserWithdrawRegistration,
    contestIsLiveAndNotEnded,
    contestIsLiveAndUserNotRegistered,
    showLockedRegisterButtonForGuest,

    // user got bonus point +1
    hasUserGotBye,
    hasOpponentNotShown,
  };
};

export default useShowdownCurrentStatus;
