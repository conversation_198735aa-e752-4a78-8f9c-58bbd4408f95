import { useQuery, gql } from '@apollo/client';
import { useEffect, useMemo, useRef } from 'react';
import useRefetchOnAppFocus from 'core/hooks/useRefetchOnAppFocus';
import { useSession } from '../../../auth/containers/AuthProvider';
import EventManager from '@/src/core/event';
import { events, listenersNamespace } from '@/src/core/event/constants';

const FICTURES_DETAILS_QUERY = gql`
  query GetFicturesByShowdownId($showdownId: ID!) {
    getFicturesByShowdownId(showdownId: $showdownId) {
      currentUserFicture {
        showdownId
        users {
          showdownParticipant {
            _id
            userID
            rounds {
              opponent
              round
              score
              games
            }
            stats {
              currentScore
            }
            rank
            userInfo {
              name
              username
              profileImageUrl
              rating
            }
          }
          currentRound {
            opponent
            round
            score
            wins
          }
        }
        round
      }
    }
  }
`;

const useShowdownFixtures = ({ showdownId }) => {
  const { data, loading, error, refetch } = useQuery(FICTURES_DETAILS_QUERY, {
    variables: { showdownId },
    // fetchPolicy: 'cache-and-network',
  });
  const { userId } = useSession();

  useRefetchOnAppFocus(refetch);
  const refetchRef = useRef(refetch);
  refetchRef.current = refetch;

  useEffect(() => {
    const eventManager = new EventManager();
    const subscription = eventManager.on(
      events.ShowdownFicturesCreated,
      listenersNamespace.ShowdownFicturesCreated,
      () => {
        refetchRef?.current?.();
      },
    );
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fixtures = useMemo(() => {
    const _fixtures = data?.getFicturesByShowdownId;
    return {
      ..._fixtures,
    };
  }, [data?.getFicturesByShowdownId]);

  return {
    loading,
    error,
    refetch,
    fixtures,
  };
};

export default useShowdownFixtures;
