import { useQuery, gql } from '@apollo/client';
import { useEffect, useMemo, useState } from 'react';
import useRefetchOnAppFocus from 'core/hooks/useRefetchOnAppFocus';
import SHOWDOWN_DETAIL_FRAGMENT from '../../../core/graphql/fragments/showdown';
import { useSession } from '../../auth/containers/AuthProvider';
import EventManager from '@/src/core/event';
import { events, listenersNamespace } from '@/src/core/event/constants';

const SHOWDOWN_DETAILS_QUERY = gql`
  ${SHOWDOWN_DETAIL_FRAGMENT}
  query GetShowdownByID($showdownId: ID!) {
    getShowdownById(showdownId: $showdownId) {
      ...ShowdownDetailFields
    }
  }
`;
const SHOWDOWN_USER_EVENTS = {
  SHOWDOWN_FIXTURES_CREATED: 'ShowdownFicturesCreated',
  SHOWDOWN_PARTICIPANT_UPDATED_EVENT: 'ShowdownParticipantUpdatedEvent',
};

const useShowdownQuery = ({ showdownId }) => {
  const { data, loading, error, refetch } = useQuery(SHOWDOWN_DETAILS_QUERY, {
    variables: { showdownId },
    fetchPolicy: 'network-only',
  });
  const { userId } = useSession();

  const [showdownDetails, setShowdownDetails] = useState(
    data?.getShowdownById ?? EMPTY_OBJECT,
  );

  useEffect(() => {
    setShowdownDetails((prev) => ({
      ...prev,
      ...data?.getShowdownById,
    }));
  }, [data]);

  useRefetchOnAppFocus(refetch);

  useEffect(() => {
    const eventManager = new EventManager();
    const subscriptionFixturesCreated = eventManager.on(
      events.ShowdownFicturesCreated,
      listenersNamespace.ShowdownFicturesCreated,
      (payload) => {
        const _showdownDetails = payload?.showdown ?? EMPTY_OBJECT;
        setShowdownDetails((prev) => ({
          ...prev,
          ..._showdownDetails,
        }));
      },
    );
    const subscriptionParticipantUpdatedEvent = eventManager.on(
      events.ShowdownParticipantUpdatedEvent,
      listenersNamespace.ShowdownParticipantUpdatedEvent,
      (payload) => {
        const _participant = payload?.participant ?? EMPTY_OBJECT;
        setShowdownDetails((prev) => ({
          ...prev,
          currentUserParticipation: {
            ...prev?.currentUserParticipation,
            ..._participant,
          },
        }));
      },
    );
    return () => {
      subscriptionFixturesCreated.unsubscribe();
      subscriptionParticipantUpdatedEvent.unsubscribe();
    };
  }, []);

  return {
    loading,
    error,
    refetch,
    showdownDetails,
  };
};

export default useShowdownQuery;
