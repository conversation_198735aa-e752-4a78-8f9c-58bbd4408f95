import {useEffect, useMemo, useRef} from "react";
import { WEBSOCKET_CHANNELS } from 'core/constants/websocket';
import useWebsocketStore from 'store/useWebSocketStore';

const useShowdownEventSubscription = (showdownId) => {
    const { isConnected, lastMessage, joinChannel } = useWebsocketStore((state) => ({
      isConnected: state.isConnected,
      lastMessage: state.lastMessage,
      joinChannel: state.joinChannel,
    }));
    const channel =  WEBSOCKET_CHANNELS.ShowdownEvents(showdownId);

  const joinChannelRef = useRef(joinChannel);
  joinChannelRef.current = joinChannel;
  useEffect(() => {
    if (isConnected) {
      joinChannelRef?.current?.(channel);
    }
  }, [isConnected]);

  const {event, payload} = useMemo(() => {
    const data = lastMessage?.[channel];
    const event = data?.type || "";
    const payload = data?.payload || EMPTY_OBJECT;
    return {event, payload};
}, [lastMessage?.[channel]]);

  return { event, payload };
};

export default useShowdownEventSubscription;
