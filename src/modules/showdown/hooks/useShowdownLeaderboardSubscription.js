import { useEffect, useMemo, useRef } from 'react';
import useWebsocketStore from '@/src/store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';

const useShowdownLeaderboardSubscription = (showdownId, page = 1) => {
  const { isConnected, lastMessage, joinChannel, leaveChannel } = useWebsocketStore((state) => ({
    isConnected: state.isConnected,
    lastMessage: state.lastMessage,
    joinChannel: state.joinChannel,
    leaveChannel: state.leaveChannel,
  }));
  const channel = WEBSOCKET_CHANNELS.ShowdownLeaderboard(showdownId, page);

  const joinChannelRef = useRef(joinChannel);
  joinChannelRef.current = joinChannel;
  const leaveChannelRef = useRef(leaveChannel);
  leaveChannelRef.current = leaveChannel;

  useEffect(() => {
    if (isConnected && showdownId) {
      joinChannelRef?.current?.(channel);
    }
    return () => {
      leaveChannelRef?.current?.(channel);
    }
  }, [isConnected, showdownId, page]);

  const { event, payload } = useMemo(() => {
    const data = lastMessage?.[channel];
    const event = data?.type || '';
    const payload = data?.payload || EMPTY_OBJECT;
    return { event, payload };
  }, [lastMessage?.[channel]]);

  return { event, payload };
};

export default useShowdownLeaderboardSubscription;
