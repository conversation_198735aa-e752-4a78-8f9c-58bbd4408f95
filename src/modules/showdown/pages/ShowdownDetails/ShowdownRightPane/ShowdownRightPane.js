import React from 'react'
import PropTypes from 'prop-types'
import { View, StyleSheet } from 'react-native'

import ShowDownEligibilityCriteria from '../../../components/ShowDownEligibilityCriteria'
import JoinNowOrRegisterButtonsAndInfos from '../../../components/JoinNowOrRegisterButtonsAndInfo'

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 16,
  },
})

const ShowdownDetailsRightPane = (props) => {
  const { state, onAction } = props

  return (
    <View style={styles.container}>
      <JoinNowOrRegisterButtonsAndInfos state={state} onAction={onAction} />
      {/*<ShowDownEligibilityCriteria />*/}
    </View>
  )
}

ShowdownDetailsRightPane.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  state: PropTypes.object.isRequired,
  onAction: PropTypes.func.isRequired,
}

export default ShowdownDetailsRightPane
