import React, { useEffect, useRef } from 'react';
import { Text, View } from 'react-native';
import _map from 'lodash/map';
import { useLocalSearchParams } from 'expo-router';
import Loading from '@/src/components/atoms/Loading';
import ErrorView from '@/src/components/atoms/ErrorView';
import _isEmpty from 'lodash/isEmpty';
import AnnouncementItem from '../AnnouncementItem/AnnouncementItem';
import useGetClubAnnouncements, {
  Announcement,
} from '../../hooks/queries/useGetClubAnnouncements';
import styles from './ClubAnnouncementsView.style';
import ScrollView from '@/src/components/atoms/Scrollview';

const ClubAnnouncementsView = ({
  announcements,
}: {
  announcements: Announcement[];
}) => {
  if (_isEmpty(announcements)) {
    return (
      <View style={{ paddingVertical: 15 }}>
        <Text style={styles.noAnnouncementsText}>No Announcements</Text>
      </View>
    );
  }
  return (
    <ScrollView style={styles.container}>
      {_map(announcements, (announcement, index) => (
        <AnnouncementItem
          key={`${announcement?.clubId}-${index}`}
          announcement={announcement}
        />
      ))}
    </ScrollView>
  );
};

const ClubAnnouncementsViewContainer = () => {
  const { id: clubId }: { id: string } = useLocalSearchParams();
  const { announcements, loading, error, fetchPage } = useGetClubAnnouncements({
    clubId,
    pageSize: 50,
  });

  const fetchPageRef = useRef(fetchPage);
  fetchPageRef.current = fetchPage;

  useEffect(() => {
    fetchPageRef.current(1);
  }, []);

  if (loading) {
    return <Loading label="Loading Announcements..." />;
  }

  if (error && !loading) {
    return <ErrorView errorMessage="Something went wrong!!" />;
  }

  return <ClubAnnouncementsView announcements={announcements} />;
};

export default React.memo(ClubAnnouncementsViewContainer);
