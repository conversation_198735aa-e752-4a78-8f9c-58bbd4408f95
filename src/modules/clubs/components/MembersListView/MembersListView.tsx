import React, { useCallback } from 'react';
import { Text, View } from 'react-native';
import PaginatedList from 'shared/PaginatedList/PaginatedList';
import useGetClubMembersByStatus from '../../hooks/queries/useGetClubMembers';
import { CLUB_MEMBERSHIP_STATUS } from '../../constants/clubsTabDetails';
import MemberListItem from '../MemberListItem';
import InvitationListItem from '../InvitationListItem';

const PAGE_SIZE = 50;

const MembersListView = ({
  status,
  onPendingCountFetched,
  clubId,
}: {
  status: string;
  onPendingCountFetched: Function;
  clubId: string;
}) => {
  const { fetchClubMembers, updateClubMembersCache } =
    useGetClubMembersByStatus({
      pageSize: PAGE_SIZE,
      clubMembershipStatus: status,
      clubId,
    });

  const renderFriendsListItem = useCallback(
    ({ item, onRemove }: { item: any; onRemove: any }) => {
      if (status === CLUB_MEMBERSHIP_STATUS.PENDING) {
        return <InvitationListItem onRemove={onRemove} memberInfo={item} />;
      }
      return <MemberListItem onRemove={onRemove} memberInfo={item} />;
    },
    [status],
  );

  const fetchClubMembersData = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      const response = await fetchClubMembers({ pageNumber });
      const { data } = response ?? EMPTY_OBJECT;
      const { clubMembers: membersObject } = data ?? EMPTY_OBJECT;
      const { results, totalResults } = membersObject;

      if (status === CLUB_MEMBERSHIP_STATUS.PENDING) {
        onPendingCountFetched(totalResults);
      }
      return { data: results, totalItems: totalResults };
    },
    [fetchClubMembers, onPendingCountFetched, status],
  );

  const renderEmptyListComponent = () => (
    <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Text
        style={{ fontFamily: 'Montserrat-500', fontSize: 13, color: 'white' }}
      >
        No Pending Requests{' '}
      </Text>
    </View>
  );

  return (
    <PaginatedList
      emptyListComponent={renderEmptyListComponent()}
      placeholderComponent={null}
      fetchData={fetchClubMembersData}
      renderItem={renderFriendsListItem}
      renderHeader={() => null}
      pageSize={PAGE_SIZE}
      keyExtractor={(item: any, index: number) => ` ${index}`}
      contentContainerStyle={{}}
      dataKey="clubMembers"
      listFooterComponent={<View style={{ height: 80 }} />}
      updateCacheFunction={updateClubMembersCache}
    />
  );
};

export default React.memo(MembersListView);
