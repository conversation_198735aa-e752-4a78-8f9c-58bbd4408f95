import PaginatedList from '@/src/components/shared/PaginatedList';
import { useLocalSearchParams } from 'expo-router';
import React, { useCallback } from 'react';
import { View, Text } from 'react-native';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import Loading from '@/src/components/atoms/Loading';
import useGetClubLeaderboard from '../../hooks/queries/useGetClubLeaderboard';
import ClubLeaderboardItemView from '../ClubLeaderboardItemView';

const DEFAULT_PAGE_SIZE = 50;
const ClubLeaderboardView = () => {
  const { id: clubId }: { id: string } = useLocalSearchParams();

  const { fetchClubLeaderboard } = useGetClubLeaderboard({
    pageSize: DEFAULT_PAGE_SIZE,
  });

  const fetchClubLeaderboardData = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      const response = await fetchClubLeaderboard({
        clubId,
        pageNumber,
      });
      const { data } = response ?? EMPTY_OBJECT;
      const { getClubLeaderboard } = data ?? EMPTY_OBJECT;
      const { results, totalResults } = getClubLeaderboard;

      return { data: results, totalItems: totalResults };
    },
    [fetchClubLeaderboard, clubId],
  );

  const renderClubLeaderboardRowItem = useCallback(
    ({
      item,
      index,
      onRemove,
    }: {
      item: any;
      index: number;
      onRemove: Function;
    }) => (
      <ClubLeaderboardItemView
        userInfo={_get(item, 'user')}
        rank={_get(item, 'rank', '-')}
      />
    ),
    [],
  );

  return (
    <PaginatedList
      emptyListComponent={
        <Text
          style={{ color: 'white', fontFamily: 'Montserrat-400', fontSize: 10 }}
        >
          No Data
        </Text>
      }
      fetchData={fetchClubLeaderboardData}
      renderItem={renderClubLeaderboardRowItem}
      renderHeader={() => null}
      pageSize={DEFAULT_PAGE_SIZE}
      keyExtractor={(item: any, index: number) => ` ${index}`}
      contentContainerStyle={{}}
      listFooterComponent={<View style={{ height: 80 }} />}
      dataKey="getClubLeaderboard"
    />
  );
};

export default React.memo(ClubLeaderboardView);
