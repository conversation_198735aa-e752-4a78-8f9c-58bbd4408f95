import React, { useEffect, useRef } from 'react';
import _map from 'lodash/map';
import Loading from '@/src/components/atoms/Loading';
import ErrorView from '@/src/components/atoms/ErrorView';
import styles from './ClubForumView.style';
import {
  Thread,
  useGetForumThreads,
} from '../../hooks/queries/useGetForumThreads';
import ThreadItem from '../ThreadItem';
import ScrollView from '@/src/components/atoms/Scrollview';

const PAGE_SIZE = 50;

const ClubForumView = ({ threads }: { threads: Thread[] }) => (
  <ScrollView style={styles.container}>
    {_map(threads, (thread, index) => (
      <ThreadItem item={thread} />
    ))}
  </ScrollView>
);

const ClubForumViewContainer = ({ forumId }: { forumId: string }) => {
  const { fetchThreads, error, loading, threads } = useGetForumThreads({
    forumId,
    pageSize: PAGE_SIZE,
  });

  const fetchThreadsRef = useRef(fetchThreads);
  fetchThreadsRef.current = fetchThreads;

  useEffect(() => {
    fetchThreadsRef.current({ pageNumber: 1 });
  }, []);

  if (loading) {
    return <Loading label="Loading Forums..." />;
  }

  if (error && !loading) {
    return <ErrorView errorMessage="Something went wrong!!" />;
  }

  return <ClubForumView threads={threads} />;
};

export default React.memo(ClubForumViewContainer);
