import React, { useCallback, useEffect, useRef } from 'react';
import { Text, View } from 'react-native';
import Loading from '@/src/components/atoms/Loading';
import ErrorView from '@/src/components/atoms/ErrorView';

import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import Header from '@/src/components/shared/Header';
import useMediaQuery from 'core/hooks/useMediaQuery';
import PrimaryButton from 'atoms/PrimaryButton';
import { showRightPane } from 'molecules/RightPane/RightPane';
import CreateClub from 'modules/clubs/pages/CreateClub/CreateClub';
import { ClubInfo } from '../../hooks/queries/useGetClubById';
import styles from './ClubsHomePage.style';
import useGetClubs from '../../hooks/queries/useGetClubs';
import ClubCard from '../../components/ClubCard';
import ClubsHomeMenuButton from '../../components/ClubsHomeMenuButton';
import ScrollView from '@/src/components/atoms/Scrollview';

const ClubsHomePage = ({ clubs }: { clubs: ClubInfo[] }) => {
  const renderTrailingComponent = useCallback(
    () => <ClubsHomeMenuButton />,
    [],
  );

  const { isMobile: isCompactMode } = useMediaQuery();

  const openRightPaneToCreateClub = useCallback(() => {
    showRightPane({
      content: <CreateClub />,
    });
  }, []);

  const renderCreateClubButton = () => (
    <PrimaryButton
      label="Create Club"
      onPress={openRightPaneToCreateClub}
      buttonLabelStyle={styles.createClubButtonText}
      buttonStyle={styles.createClubButton}
    />
  );

  return (
    <View style={styles.container}>
      <Header title="Clubs" renderTrailingComponent={renderTrailingComponent} />
      <ScrollView contentContainerStyle={{ paddingBottom: 30 }}>
        <View style={styles.innerContainer}>
          <View
            style={[
              !isCompactMode && {
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingVertical: 10,
              },
            ]}
          >
            <Text style={styles.clubCategoryText}>All Clubs</Text>
            {!isCompactMode && renderCreateClubButton()}
          </View>
          <ScrollView
            contentContainerStyle={{ gap: 15 }}
            horizontal
            showsHorizontalScrollIndicator={false}
          >
            {_map(clubs, (club) => (
              <ClubCard club={club} key={club.id} />
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </View>
  );
};

const ClubsHomePageContainer = () => {
  const { clubs, fetchPage, error, loading } = useGetClubs({ pageSize: 50 });

  const fetchPageRef = useRef(fetchPage);
  fetchPageRef.current = fetchPage;

  useEffect(() => {
    fetchPageRef.current(1);
  }, []);

  if (loading) {
    return <Loading label="Loading Clubs..." />;
  }

  if (error && !loading) {
    return <ErrorView errorMessage="Something went wrong" />;
  }

  if (!_isEmpty(clubs)) {
    return <ClubsHomePage clubs={clubs} />;
  }

  return (
    <View
      style={[
        styles.container,
        { justifyContent: 'center', alignItems: 'center' },
      ]}
    >
      <Text style={styles.noClubsText}>No clubs found</Text>
    </View>
  );
};

export default React.memo(ClubsHomePageContainer);
