import Header from '@/src/components/shared/Header';
import { useLocalSearchParams } from 'expo-router';
import React, { useCallback, useRef, useState } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import Loading from '@/src/components/atoms/Loading';
import ErrorView from '@/src/components/atoms/ErrorView';
import PaginatedList from '@/src/components/shared/PaginatedList';
import _trim from 'lodash/trim';
import Ionicons from '@expo/vector-icons/Ionicons';
import dark from '@/src/core/constants/themes/dark';

import _captialize from 'lodash/capitalize';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import uuid from 'react-native-uuid';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useCreateThreadReply from '../../hooks/mutations/useCreateForumReply';
import calculateDuration from '../../utils/calculateDuration';
import ThreadReplyItem from '../../components/ThreadReplyItem';
import {
  ThreadReply,
  useGetThreadReplies,
} from '../../hooks/queries/useGetThreadReplies';
import useGetThreadsById from '../../hooks/queries/useGetThreadsById';
import styles from './ThreadDetailedInfoReplies.style';
import { Thread } from '../../hooks/queries/useGetForumThreads';

const PAGE_SIZE = 20;

const ThreadDetailedInfoReplies = ({ threadInfo }: { threadInfo: Thread }) => {
  const { fetchThreadReplies, updateForumRepliesCache } = useGetThreadReplies({
    threadId: threadInfo.id,
    pageSize: PAGE_SIZE,
  });

  const { createThreadReply, error, isReplyingOnThread } =
    useCreateThreadReply();

  const { isMobile: isCompactMode } = useMediaQuery();

  const [refreshKey, setRefreshKey] = useState(0);
  const commentTextRef = useRef(null);

  const { user } = useSession();

  const [replyText, setReplyText] = useState<string>('');
  const [isFocused, setIsFocused] = useState<boolean>(false);

  const [showAddCommmentTextBox, setShowAddCommentTextBox] =
    useState<boolean>(false);

  const sendThreadMessage = useCallback(async () => {
    if (_trim(replyText).length !== 0) {
      await createThreadReply({
        input: { threadId: threadInfo.id, content: replyText },
      });
      updateForumRepliesCache({
        addedItems: [
          {
            content: replyText,
            createdAt: new Date().toISOString(),
            createdBy: user?._id,
            creatorInfo: {
              username: user?.username,
              profileImageUrl: user?.profileImageUrl,
            },
            id: uuid.v4() as string,
            threadId: threadInfo.id,
          },
        ],
        pageNumber: 1,
        removedItemIds: [],
      });
      setRefreshKey((prev) => prev + 1);
      setReplyText('');
    }
  }, [replyText, createThreadReply, updateForumRepliesCache, user, threadInfo]);

  const fetchThreadRepliesData = useCallback(
    async ({ pageNumber }: { pageNumber: number }) => {
      const response = await fetchThreadReplies({ pageNumber });
      const { data } = response ?? EMPTY_OBJECT;
      const { forumReplies: forumThreadRepliesObject } = data ?? EMPTY_OBJECT;
      const { results, totalResults } = forumThreadRepliesObject;
      return { data: results, totalItems: totalResults };
    },
    [fetchThreadReplies],
  );

  const renderThreadReplyItem = useCallback(
    ({ item }: { item: ThreadReply }) => <ThreadReplyItem threadReply={item} />,
    [],
  );

  const handleKeyPress = useCallback(
    (e: any) => {
      if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
        sendThreadMessage();
        e.preventDefault();
      }
    },
    [sendThreadMessage],
  );

  return (
    <View
      style={[
        styles.container,
        !isCompactMode && { paddingHorizontal: 50, paddingTop: 10 },
      ]}
    >
      <Header
        title={_captialize(threadInfo.title)}
        renderTrailingComponent={() => (
          <MaterialIcons
            name="add-comment"
            size={20}
            color="white"
            onPress={() => {
              commentTextRef?.current?.focus();
              setShowAddCommentTextBox(true);
            }}
          />
        )}
      />

      <View style={styles.threadInfo}>
        <Text style={[styles.titleText]}>{threadInfo.title}</Text>

        <Text style={styles.contentText}>{threadInfo.content}</Text>

        <Text
          style={styles.creatorText}
        >{`Posted By ${threadInfo.creatorInfo.username}`}</Text>
        <Text style={styles.creatorText}>
          {calculateDuration({ createdAt: threadInfo.createdAt })} ago
        </Text>
        {!isCompactMode && (
          <View
            style={{
              flexDirection: 'row',
              gap: 10,
              alignItems: 'center',
              paddingVertical: 10,
            }}
          >
            <TextInput
              ref={commentTextRef}
              style={[
                styles.textFormField,
                isFocused && styles.focusedInputField,
              ]}
              multiline
              value={replyText}
              placeholder="Type Your Thought..."
              placeholderTextColor={dark.colors.tertiary}
              onFocus={() => setIsFocused(true)}
              onKeyPress={handleKeyPress}
              onChangeText={(text) => setReplyText(text)}
            />
            <TouchableOpacity
              onPress={sendThreadMessage}
              style={[
                styles.sendButton,
                isReplyingOnThread && { backgroundColor: dark.colors.textDark },
              ]}
            >
              <Ionicons
                name="send-sharp"
                size={12}
                color={dark.colors.background}
              />
            </TouchableOpacity>
          </View>
        )}
      </View>

      <View style={{ flex: 1, paddingHorizontal: 16, paddingTop: 10 }}>
        <Text style={styles.commentTitle}>Comments :-</Text>
        <PaginatedList
          key={`replies-${refreshKey}`}
          emptyListComponent={
            <Text style={styles.noCommentsText}>No Comments</Text>
          }
          placeholderComponent={null}
          fetchData={fetchThreadRepliesData}
          // updateCacheFunction={updateForumRepliesCache}
          renderItem={renderThreadReplyItem}
          listFooterComponent={<View style={{ height: 50 }} />}
          renderHeader={() => null}
          pageSize={PAGE_SIZE}
          keyExtractor={(item, index) => ` ${index}`}
          contentContainerStyle={{}}
          dataKey="forumReplies"
        />
      </View>

      <View
        style={[
          styles.replyTextFieldContainer,
          !showAddCommmentTextBox && { opacity: 0 },
        ]}
      >
        <TextInput
          ref={commentTextRef}
          style={[styles.textFormField, isFocused && styles.focusedInputField]}
          value={replyText}
          placeholder="Type Your Thought..."
          placeholderTextColor={dark.colors.tertiary}
          onFocus={() => setIsFocused(true)}
          onChangeText={(text) => setReplyText(text)}
        />
        <TouchableOpacity
          onPress={sendThreadMessage}
          style={[
            styles.sendButton,
            isReplyingOnThread && { backgroundColor: dark.colors.textDark },
          ]}
        >
          <Ionicons
            name="send-sharp"
            size={12}
            color={dark.colors.background}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const ThreadDetailedInfoRepliesContainer = () => {
  const { threadId }: { threadId: string } = useLocalSearchParams();
  const { threadInfo, loading, error } = useGetThreadsById({ threadId });

  if (loading) {
    return <Loading label="Loading Thread..." />;
  }
  if (error && !loading) {
    return <ErrorView errorMessage="Something went Wrong | Thread Not Found" />;
  }

  return <ThreadDetailedInfoReplies threadInfo={threadInfo} />;
};

export default React.memo(ThreadDetailedInfoRepliesContainer);
