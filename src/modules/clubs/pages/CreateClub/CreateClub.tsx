import React, { useCallback, useState } from 'react';
import { Image, TouchableOpacity, View } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import _map from 'lodash/map';
import _isEqual from 'lodash/isEqual';
import _toString from 'lodash/toString';
import dark from '@/src/core/constants/themes/dark';
import DropdownComponent from '@/src/components/atoms/Dropdown';
import useMediaQuery from 'core/hooks/useMediaQuery';
import usePickAndCompressImage from 'core/hooks/usePickAndCompressImage';
import CreationPageHeader from 'modules/clubs/CreationPageHeader';
import useCreateClub from 'modules/clubs/hooks/mutations/useCreateClub';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import FormView from '@/src/components/shared/Form/FormView/FormView';
import styles from './CreateClub.style';
import { getClubCreationInfoWithDefaultValues } from '../../constants/clubRegConstants';

const CreateClub = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const { createClub, isCreatingClub, error } = useCreateClub();

  const [clubInfo, setClubInfo] = useState({
    logoImage: null,
    bannerImage: null,
  });

  const handleOnInputFieldChange = useCallback(
    ({ key, value }: { key: string; value: any }) => {
      setClubInfo((prev) => ({ ...prev, [key]: value }));
    },
    [],
  );

  const { isLoading: isImageLoading, pickAndCompressImage } =
    usePickAndCompressImage();

  const handlePickBackgroundImage = useCallback(async () => {
    try {
      const uri = await pickAndCompressImage();
      if (uri) {
        handleOnInputFieldChange({ key: 'bannerImage', value: uri });
      }
    } catch (e) {
      console.info(e);
    }
  }, [handleOnInputFieldChange, pickAndCompressImage]);

  const handlePickLogoImage = useCallback(async () => {
    const uri = await pickAndCompressImage();
    if (uri) {
      handleOnInputFieldChange({ key: 'logoImage', value: uri });
    }
  }, [handleOnInputFieldChange, pickAndCompressImage]);

  const onPressCreateClub = useCallback(
    async (
      formState: any,
      validateFormStateData: Function,
      enableValidation: Function,
    ) => {
      const isValid = validateFormStateData();
      if (!isValid) {
        enableValidation();
        return;
      }

      if (isCreatingClub) {
        return;
      }
      try {
        await createClub({
          clubInfo: {
            name: formState.name,
            description: formState.description,
            visibility: formState.visibility,
            logoImage: clubInfo.logoImage,
            bannerImage: clubInfo.bannerImage,
          },
        });
        closeRightPane();
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: 'Club Created Successfully',
        });
      } catch (e) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `Failed to Create Club ${_toString(e)}`,
        });
      }
    },
    [isCreatingClub, createClub, clubInfo],
  );

  const renderHeaderComponent = (
    formState: any,
    validateFormStateData: Function,
    enableValidation: Function,
  ) => (
    <View style={styles.topBarContainer}>
      <View style={styles.backgroundImage}>
        {clubInfo.bannerImage ? (
          <Image
            source={{ uri: clubInfo.bannerImage }}
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
            }}
          />
        ) : null}

        <CreationPageHeader
          title={isCreatingClub ? 'Creating...' : 'Create Club'}
          buttonLabel="Create"
          isTransparentBg
          onPress={() =>
            onPressCreateClub(
              formState,
              validateFormStateData,
              enableValidation,
            )
          }
        />
      </View>

      <View style={styles.clubImageContainer}>
        <TouchableOpacity
          style={{
            backgroundColor: dark.colors.secondary,
            borderRadius: 20,
            justifyContent: 'center',
            alignItems: 'center',
            width: 30,
            height: 30,
            position: 'absolute',
            right: 10,
            top: 10,
          }}
          onPress={handlePickBackgroundImage}
          disabled={isImageLoading}
        >
          <MaterialIcons name="edit" size={17} color={dark.colors.background} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.clubImageView, { overflow: 'hidden' }]}
          onPress={handlePickLogoImage}
          disabled={isImageLoading}
        >
          {clubInfo.logoImage ? (
            <Image
              source={{ uri: clubInfo.logoImage }}
              style={{ width: '100%', height: '100%' }}
            />
          ) : (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                flex: 1,
              }}
            >
              <MaterialIcons
                name="add-a-photo"
                size={24}
                color={dark.colors.textDark}
              />
            </View>
          )}

          <View
            style={{
              position: 'absolute',
              right: 5,
              bottom: 5,
              backgroundColor: dark.colors.secondary,
              borderRadius: 12,
              padding: 4,
            }}
          >
            <MaterialIcons
              name="edit"
              size={16}
              color={dark.colors.background}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <FormView
      fields={getClubCreationInfoWithDefaultValues({})}
      hideSubmitButton
      hideHeader
      renderAdditionalComponent={renderHeaderComponent}
    />
  );
};

export default React.memo(CreateClub);
