{"name": "apollo-upload-client", "version": "18.0.1", "description": "A terminating Apollo Link for Apollo Client that fetches a GraphQL multipart request if the GraphQL variables contain files (by default FileList, File, or Blob instances), or else fetches a regular GraphQL POST or GET request (depending on the config and GraphQL operation).", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://jaydenseric.com"}, "repository": {"type": "git", "url": "git+https://github.com/jaydenseric/apollo-upload-client.git"}, "homepage": "https://github.com/jaydenseric/apollo-upload-client#readme", "bugs": "https://github.com/jaydenseric/apollo-upload-client/issues", "funding": "https://github.com/sponsors/jaydenseric", "keywords": ["graphql", "multipart", "request", "file", "upload", "apollo", "client", "link", "react", "esm", "mjs"], "files": ["createUploadLink.mjs", "formDataAppendFile.mjs", "isExtractableFile.mjs"], "sideEffects": false, "exports": {"./createUploadLink.mjs": "./createUploadLink.mjs", "./formDataAppendFile.mjs": "./formDataAppendFile.mjs", "./isExtractableFile.mjs": "./isExtractableFile.mjs", "./package.json": "./package.json"}, "engines": {"node": "^18.15.0 || >=20.4.0"}, "browserslist": "Node 18.15 - 19 and <PERSON><PERSON> < 19, Node >= 20.4, > 0.5%, not OperaMini all, not IE > 0, not dead", "peerDependencies": {"@apollo/client": "^3.8.0", "graphql": "14 - 16"}, "dependencies": {"extract-files": "^13.0.0"}, "devDependencies": {"@apollo/client": "^3.8.6", "@types/node": "^20.8.7", "coverage-node": "^8.0.0", "esbuild": "^0.19.5", "eslint": "^8.52.0", "eslint-plugin-optimal-modules": "^1.0.2", "eslint-plugin-simple-import-sort": "^10.0.0", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "gzip-size": "^7.0.0", "prettier": "^3.0.3", "revertable-globals": "^4.0.0", "typescript": "^5.2.2"}, "scripts": {"eslint": "eslint .", "prettier": "prettier -c .", "types": "tsc -p jsconfig.json", "tests": "coverage-node --test-reporter=spec --test", "test": "npm run eslint && npm run prettier && npm run types && npm run tests", "prepublishOnly": "npm test"}}