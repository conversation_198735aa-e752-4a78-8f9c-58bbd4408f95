import { Linking, Pressable, View } from 'react-native';
import React, { useCallback, useMemo } from 'react';
import { Text } from '@rneui/themed';

import { isValidURL } from 'core/utils/general';
import _map from 'lodash/map';
import { usePathname, useRouter } from 'expo-router';
import Analytics from 'core/analytics';
import Dark from '@/src/core/constants/themes/dark';
import { useSession } from 'modules/auth/containers/AuthProvider';
import Icon from 'atoms/Icon';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import useMediaQuery from 'core/hooks/useMediaQuery';
import styles from './LeftPaneNavigator.style';
import { getLeftPaneNavigatorTabs } from '../constants/routeConstants';

import CommunityNavOption from '../components/CommunityNavOption';

const LeftPaneNavigator = () => {
  const router = useRouter();
  const { user } = useSession();
  const { isTablet } = useMediaQuery();

  const LEFT_PANE_NAVIGATOR_TABS = useMemo(
    () => getLeftPaneNavigatorTabs({ user }),
    [user],
  );

  const selectedPath = usePathname();

  const handleNavigation = useCallback(
    (item) => {
      const { route, event } = item;
      Analytics.track(event, {
        [PAGE_NAME_KEY]: PAGE_NAMES.LEFT_PANE_NAV,
      });
      if (isValidURL(route)) {
        Linking.openURL(route);
      } else {
        router.navigate(route);
      }
    },
    [router],
  );

  const renderNavigatorTab = useCallback(
    (tab, index) => {
      const isSelectedTab = tab.route === selectedPath;
      const color = isSelectedTab
        ? Dark.colors.secondary
        : Dark.colors.textDark;
      return (
        <Pressable
          testID={`left-pane-navigator-${index}`}
          key={index}
          style={({ hovered }) => [
            styles.navItem,
            hovered && styles.hoveredNavItem,
          ]}
          onPress={() => handleNavigation(tab)}
        >
          <View
            style={[
              styles.screenContainer,
              isSelectedTab && styles.selectedTabStyle,
              isTablet && { width: 'auto' },
            ]}
          >
            <View style={styles.iconContainer}>
              <Icon size={15} color={color} {...tab.iconConfig} />
            </View>
            {!isTablet && (
              <Text style={[styles.modalItemText, { color }]}>{tab.text}</Text>
            )}
          </View>
        </Pressable>
      );
    },
    [handleNavigation, selectedPath, isTablet],
  );

  const renderPeopleNavItem = () => <CommunityNavOption />;

  return (
    <View style={[styles.container, isTablet && { maxWidth: 80 }]}>
      <View style={styles.primaryNavOptionsContainer}>
        {_map(LEFT_PANE_NAVIGATOR_TABS, renderNavigatorTab)}
        {renderPeopleNavItem()}
      </View>
    </View>
  );
};

export default React.memo(LeftPaneNavigator);
