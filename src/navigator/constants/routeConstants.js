import { ICON_TYPES } from 'atoms/Icon';
import userReader from 'core/readers/userReader';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import {
  CREATOR_PROGRAMME_URL,
  DISCORD_COMMUNITY_URL,
  FEEDBACK_FORM_URL,
} from 'core/constants/appConstants';

export const getHomeNavigatorTabs = ({ user }) => [
  {
    key: 'friends',
    iconConfig: {
      name: 'people',
      type: ICON_TYPES.IONICON,
    },
    text: 'Friends',
    route: `profile/${userReader.username(user)}/friends`,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_FRIENDS,
  },
  {
    key: 'leaderboard',
    iconConfig: {
      name: 'leaderboard',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Leaderboard',
    route: 'leaderboard',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_LEADERBOARD,
  },
  {
    key: 'dailyChallenge',
    iconConfig: {
      name: 'calendar-check',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    text: 'Daily Challenge',
    route: 'daily-challenge-leaderboard',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_DAILY_CHALLENGE,
  },
  {
    key: 'feedback',
    iconConfig: {
      name: 'feedback',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Feedback',
    route: FEEDBACK_FORM_URL,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_FEEDBACK,
  },
  {
    key: 'messages',
    iconConfig: {
      name: 'chat',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Messages',
    route: 'chat',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_FEEDBACK,
  },
  {
    key: 'creators',
    showNewTag: true,
    iconConfig: {
      name: 'account-music-outline',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
    },
    text: 'Creators Programme',
    route: CREATOR_PROGRAMME_URL,
    event: ANALYTICS_EVENTS.NAV.CREATOR_PROGRAMME_URL,
  },
  {
    key: 'community',
    iconConfig: {
      name: 'discord',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Community',
    route: DISCORD_COMMUNITY_URL,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_JOIN_COMMUNITY,
  },
  {
    key: 'referAFriend',
    iconConfig: {
      name: 'adduser',
      type: ICON_TYPES.ANT_DESIGN,
    },
    text: 'Refer a Friend',
    route: `profile/${userReader.username(user)}/referral`,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_JOIN_COMMUNITY,
  },
];

export const getLeftPaneNavigatorTabs = ({ user }) => [
  {
    iconConfig: {
      name: 'home',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    text: 'Arena',
    route: '/home',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_ARENA,
  },
  {
    iconConfig: {
      name: 'puzzle-piece',
      type: ICON_TYPES.FONT_AWESOME_6,
    },
    text: 'Puzzles',
    route: '/puzzle-home',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_DAILY_CHALLENGE,
  },
  {
    iconConfig: {
      name: 'calendar-check',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    text: 'Daily Challenge',
    route: '/daily-challenge-leaderboard',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_DAILY_CHALLENGE,
  },
  {
    iconConfig: {
      name: 'trophy',
      type: ICON_TYPES.FONT_AWESOME_6,
    },
    text: 'Compete',
    route: '/contests',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_CONTEST,
  },
  {
    iconConfig: {
      name: 'dumbbell',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    text: 'Nets',
    route: '/nets',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_PRACTICE,
  },
  {
    iconConfig: {
      name: 'leaderboard',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Leaderboard',
    route: '/leaderboard',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_LEADERBOARD,
  },
  {
    iconConfig: {
      name: 'person',
      type: ICON_TYPES.IONICON,
    },
    text: 'My Profile',
    route: `/profile/${user?.username}`,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_PROFILE,
  },

  {
    iconConfig: {
      name: 'feedback',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Feedback',
    route: FEEDBACK_FORM_URL,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_FEEDBACK,
  },
  {
    iconConfig: {
      name: 'account-music-outline',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
    },
    text: 'Creators Programme',
    route: CREATOR_PROGRAMME_URL,
    event: ANALYTICS_EVENTS.NAV.CREATOR_PROGRAMME_URL,
  },
];

export const LEFT_PANE_NAVIGATOR_TABS = [
  {
    iconConfig: {
      name: 'home',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    text: 'Arena',
    route: '/home',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_ARENA,
  },
  {
    iconConfig: {
      name: 'dumbbell',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    text: 'Nets',
    route: '/nets',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_PRACTICE,
  },
  {
    iconConfig: {
      name: 'leaderboard',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Leaderboard',
    route: '/leaderboard',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_LEADERBOARD,
  },
  {
    iconConfig: {
      name: 'trophy',
      type: ICON_TYPES.FONT_AWESOME_6,
    },
    text: 'Contest',
    route: '/contests',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_CONTEST,
  },
  {
    iconConfig: {
      name: 'person',
      type: ICON_TYPES.IONICON,
    },
    text: 'My Profile',
    route: '/profile/',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_PROFILE,
  },
  {
    iconConfig: {
      name: 'calendar-check',
      type: ICON_TYPES.FONT_AWESOME_5,
    },
    text: 'Daily Challenge',
    route: '/daily-challenge-leaderboard',
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_DAILY_CHALLENGE,
  },
  {
    iconConfig: {
      name: 'feedback',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Feedback',
    route: FEEDBACK_FORM_URL,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_FEEDBACK,
  },
  {
    iconConfig: {
      name: 'discord',
      type: ICON_TYPES.MATERIAL_ICONS,
    },
    text: 'Community',
    route: DISCORD_COMMUNITY_URL,
    event: ANALYTICS_EVENTS.NAV.CLICKED_ON_JOIN_COMMUNITY,
  },
];
