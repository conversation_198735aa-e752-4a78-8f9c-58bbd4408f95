import React from 'react';
import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';

const styles = StyleSheet.create({
  googleLoginButton: {
    width: '100%',
    minWidth: 200,
    justifyContent: 'center',
    alignItems: 'center',
    maxWidth: 400,
    height: 45,
    borderRadius: 12,
  },
  googleLoginButtonStyle: {
    borderWidth: 0.8,
    borderColor: Dark.colors.victoryColor,
    borderRadius: 10,
    height: 45,
  },
  googleLoginLabel: {
    fontSize: 12,
    letterSpacing: 2,
    fontFamily: 'Montserrat-700',
    color: 'white',
  },
  googleLoginButtonBackground: {
    flex: 1,
    backgroundColor: Dark.colors.victoryColor,
  },
});

const SignInWithGoogleButton = ({
  onPressGoogleLogin,
  styles: customStyles,
}) => (
  <InteractivePrimaryButton
    label="SIGN IN USING GOOGLE"
    radius={20}
    buttonContainerStyle={[
      styles.googleLoginButton,
      customStyles?.buttonContainerStyle,
    ]}
    buttonStyle={[styles.googleLoginButtonStyle, customStyles?.buttonStyle]}
    labelStyle={[styles.googleLoginLabel, customStyles?.labelStyle]}
    onPress={onPressGoogleLogin}
    buttonBorderBackgroundStyle={styles.googleLoginButtonBackground}
  />
);

export default SignInWithGoogleButton;
