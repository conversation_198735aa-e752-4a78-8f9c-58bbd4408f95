import { useGoogleLogin } from '@react-oauth/google';
import React, { useCallback } from 'react';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';

import { Pressable } from 'react-native';
import _isNil from 'lodash/isNil';
import _isFunction from 'lodash/isFunction';
import useGoogleLoginQuery from '../../hooks/useGoogleLoginQuery';
import SignInWithGoogleButton from '../SignInWithGoogleButton';
import Analytics from '../../../analytics';
import { ANALYTICS_EVENTS } from '../../../analytics/const';

const GoogleLoginButton = ({
  onSuccess,
  onError,
  renderButtonComponent,
  onPress: onPressFromProps,
  styles: customStyles,
} = EMPTY_OBJECT) => {
  const { loginWithGoogle } = useGoogleLoginQuery();

  const { session, onUserSignIn } = useSession();

  const onGoogleLoginSuccess = useCallback(
    (credentialResponse) => {
      showToast({
        type: TOAST_TYPE.LOADING,
        description: 'Logging in...',
      });
      loginWithGoogle(credentialResponse)
        .then(async (response) => {
          const loggedInUser = response?.data?.googleLogin;
          await onUserSignIn({ user: loggedInUser });
          hideToast();
          Analytics.track(ANALYTICS_EVENTS.GOOGLE_LOGIN_SUCCESS);
        })
        .catch((error) => {
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Something went wrong',
          });
          Analytics.track(ANALYTICS_EVENTS.GOOGLE_LOGIN_FAILURE_SERVER, {
            error,
          });
        });
      onSuccess?.(credentialResponse);
    },
    [loginWithGoogle, onUserSignIn],
  );

  const onGoogleLoginError = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.GOOGLE_LOGIN_FAILURE_CLIENT, {
      error: 'Login Failed/Cancelled on Client Side',
    });
    showToast({
      type: TOAST_TYPE.ERROR,
      description: 'Login Failed',
    });
    onError?.();
  }, []);

  const login = useGoogleLogin({
    onSuccess: onGoogleLoginSuccess,
    onError: onGoogleLoginError,
    flow: 'auth-code',
  });

  const onPressGoogleLogin = useCallback(() => {
    onPressFromProps?.();
    Analytics.track(ANALYTICS_EVENTS.CLICKED_GOOGLE_LOGIN);
    login();
  }, [login, onPressFromProps]);

  if (_isFunction(renderButtonComponent) && !_isNil(renderButtonComponent)) {
    return (
      <Pressable onPress={onPressGoogleLogin}>
        {({ hovered }) => renderButtonComponent({ hovered })}
      </Pressable>
    );
  }

  return (
    <SignInWithGoogleButton
      onPressGoogleLogin={onPressGoogleLogin}
      styles={customStyles}
    />
  );
};

export default GoogleLoginButton;
