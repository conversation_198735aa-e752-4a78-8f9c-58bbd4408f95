import React, { useCallback } from 'react';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';

import { GoogleSignin } from '@react-native-google-signin/google-signin';
import PrimaryButton from 'atoms/PrimaryButton';
import { TouchableOpacity } from 'react-native';
import _isFunction from 'lodash/isFunction';
import _isNil from 'lodash/isNil';
import SignInWithGoogleButton from '../SignInWithGoogleButton';
import useLegacyGoogleLoginQuery from '../../hooks/useLegacyGoogleLoginQuery';
import Analytics from '../../../analytics';
import { ANALYTICS_EVENTS } from '../../../analytics/const';
import { PAGE_NAMES } from '../../../constants/pageNames';

GoogleSignin.configure({
  webClientId: process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID,
  iosClientId: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
  forceCodeForRefreshToken: true,
});

const GoogleLoginButton = ({
  onSuccess,
  onError,
  toShowTextOnly = false,
  renderButtonComponent,
} = EMPTY_OBJECT) => {
  const { loginWithGoogle } = useLegacyGoogleLoginQuery();

  const { session, onUserSignIn } = useSession();

  const onGoogleLoginSuccess = useCallback(
    (userInfo) => {
      const { idToken, user } = userInfo;
      showToast({
        type: TOAST_TYPE.LOADING,
        description: 'Logging in...',
      });
      loginWithGoogle({ idToken })
        .then((response) => {
          const loggedInUser = response?.data?.legacyGoogleLogin;
          onUserSignIn({ user: loggedInUser });
          Analytics.track(ANALYTICS_EVENTS.GOOGLE_LOGIN_SUCCESS);
          hideToast();
        })
        .catch((error) => {
          Analytics.track(ANALYTICS_EVENTS.GOOGLE_LOGIN_FAILURE_SERVER, {
            error,
          });
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Something went wrong',
          });
        });
      onSuccess?.(user);
    },
    [loginWithGoogle, onUserSignIn],
  );

  const onGoogleLoginError = useCallback(() => {
    showToast({
      type: TOAST_TYPE.ERROR,
      description: 'Login Failed',
    });
    onError?.();
  }, []);

  const signIn = useCallback(async () => {
    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      onGoogleLoginSuccess(userInfo);
    } catch (error) {
      Analytics.track(ANALYTICS_EVENTS.GOOGLE_LOGIN_FAILURE_CLIENT, {
        error: 'Login Failed/Cancelled on Client Side',
        errorMessage: error.message,
      });
      onGoogleLoginError();
    }
  }, [onGoogleLoginSuccess, onGoogleLoginError]);

  const onPressGoogleLogin = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.ONBOARDING.CLICKED_ON_GOOGLE_LOGIN, {
      pageName: PAGE_NAMES.ONBOARDING_ANIMATION,
    });
    signIn();
  }, [signIn]);

  if (toShowTextOnly) {
    return (
      <PrimaryButton
        onPress={onPressGoogleLogin}
        label="Login as existing user"
        labelStyle={{ fontSize: 16 }}
        radius={20}
        buttonStyle={{ height: 50, paddingHorizontal: 46, borderRadius: 30 }}
      />
    );
  }

  if (_isFunction(renderButtonComponent) && !_isNil(renderButtonComponent)) {
    return (
      <TouchableOpacity onPress={onPressGoogleLogin}>
        {renderButtonComponent()}
      </TouchableOpacity>
    );
  }

  return <SignInWithGoogleButton onPressGoogleLogin={onPressGoogleLogin} />;
};

export default GoogleLoginButton;
