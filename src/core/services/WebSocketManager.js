import _toString from 'lodash/toString';
import _toNumber from 'lodash/toNumber';
import _isNil from 'lodash/isNil';
import _filter from 'lodash/filter';
import _uniq from 'lodash/uniq';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import { getUserCurrentActivity } from '../utils/getUserCurrentActivity';
import { WEBSOCKET_EVENTS } from '../constants/events';

const LINEAR_ATTEMPT_THRESHOLD = 50;
const RECONNECT_ATTEMPTS = 75;
const RECONNECT_INTERVAL = 500;

export const WEBSOCKET_CHANNELS = {
  UserEvents: (userId) => `${WEBSOCKET_EVENTS.USER_EVENT}_${userId}`,
  GameEvents: (gameId) => `${WEBSOCKET_EVENTS.GAME_EVENT}_${gameId}_V2`,
  PuzzleGameEvents: (gameId) =>
    `${WEBSOCKET_EVENTS.PUZZLE_GAME_EVENT}_${gameId}`,
  ShowdownEvents: (showdownId) =>
    `${WEBSOCKET_EVENTS.SHOWDOWN_EVENT}_${showdownId}`,
  OnlineUsers: `${WEBSOCKET_EVENTS.ONLINE_USERS}`,
};

class WebSocketManager {
  constructor() {
    if (WebSocketManager.instance) {
      return WebSocketManager.instance;
    }

    this.socket = null;
    this.isConnected = false;
    this.url = '';
    this.token = '';
    this.userId = '';
    this.currentUrl = '';
    this.reconnectCount = 0;
    this.reconnectTimeout = null;
    this.pingPongTimeout = null;
    this.messageQueue = [];
    this.channels = [];
    this.listeners = new Map();
    this.serverOffset = 0;
    this.serverOffsetSet = false;
    this.pingChannel = `ping-${Date.now()}`;

    global.getCurrentTime = () => Date.now() + _toNumber(this.serverOffset);
    if (typeof window !== 'undefined') {
      window.getCurrentTime = global.getCurrentTime;
    }

    WebSocketManager.instance = this;
  }

  initialize(url, token, userId) {
    this.url = url;
    this.token = token;
    this.userId = userId;

    if (!_isNil(userId) && !_isNil(token)) {
      this.connect();
    }
  }

  setCurrentUrl(url) {
    this.currentUrl = url;
  }

  connect() {
    try {
      if (_isNil(this.url) || _isNil(this.token)) {
        console.warn('Cannot connect to WebSocket: missing URL or token');
        return;
      }

      if (
        this.socket?.readyState === WebSocket.CONNECTING ||
        this.socket?.readyState === WebSocket.OPEN
      ) {
        return;
      }

      const wsUrl = `${this.url}?token=${this.token}`;
      this.socket = new WebSocket(wsUrl);

      this.socket.onopen = () => {
        if (_isNil(this.userId) || _isNil(this.token)) return;

        this.isConnected = true;
        this.reconnectCount = 0;
        this.joinChannelsOnReconnect();
        this.processCachedMessages();

        this.notifyConnectionListeners(true);
      };

      this.socket.onclose = () => {
        this.isConnected = false;
        this.notifyConnectionListeners(false);

        if (this.reconnectCount >= RECONNECT_ATTEMPTS) {
          console.error('Max reconnect attempts reached. Unable to reconnect.');
          return;
        }

        const reconnectDelay =
          this.reconnectCount <= LINEAR_ATTEMPT_THRESHOLD
            ? RECONNECT_INTERVAL
            : 2 ** (this.reconnectCount - LINEAR_ATTEMPT_THRESHOLD) *
              RECONNECT_INTERVAL;

        this.reconnectTimeout = setTimeout(() => {
          if (this.socket?.readyState !== WebSocket.OPEN) {
            this.reconnectCount += 1;
            this.connect();
          }
        }, reconnectDelay);
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      this.socket.onmessage = (event) => {
        try {
          const parsed = JSON.parse(event.data);
          const data = JSON.parse(parsed?.data);
          const channel = parsed?.channel;

          if (channel === this.pingChannel) {
            this.handleServerTimeOffset(data);
          }

          this.notifyChannelListeners(channel, data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
    }
  }

  disconnect() {
    this.isConnected = false;
    this.notifyConnectionListeners(false);

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    if (this.pingPongTimeout) {
      clearInterval(this.pingPongTimeout);
    }
  }

  handleServerTimeOffset(data) {
    if (data && !this.serverOffsetSet) {
      const clientTime = Date.now();
      const { clientRequestTime, serverResponseTime } = data || {};
      const roundTripTime = clientTime - clientRequestTime;

      this.serverOffset = serverResponseTime + roundTripTime / 2 - clientTime;
      this.serverOffsetSet = true;

      global.getCurrentTime = () => Date.now() + _toNumber(this.serverOffset);

      if (typeof window !== 'undefined') {
        window.getCurrentTime = global.getCurrentTime;
      }
    }
  }

  joinChannel(channel) {
    if (!channel) {
      console.error('Cannot join channel: channel name is empty');
      return false;
    }

    try {
      const existingChannel = _includes(this.channels, channel);
      if (existingChannel) {
        return true;
      }

      this.channels = _uniq([...this.channels, channel]);

      if (this.socket?.readyState === WebSocket.OPEN) {
        this.socket.send(
          JSON.stringify({
            type: 'channel_subscribe',
            channel,
          }),
        );
      } else {
        // console.log(
        //   `Channel ${channel} queued for subscription when socket connects`,
        // );
      }

      return true;
    } catch (err) {
      console.error('Error joining channel:', err);
      return false;
    }
  }

  unsubscribeChannel(channel) {
    try {
      if (this.socket?.readyState === WebSocket.OPEN) {
        this.socket.send(
          JSON.stringify({
            type: 'channel_unsubscribe',
            channel,
          }),
        );

        this.channels = _filter(
          this.channels,
          (existingChannel) => existingChannel !== channel,
        );

        this.listeners.delete(channel);

        return true;
      }
      return false;
    } catch (err) {
      console.error(`Error unsubscribing from channel ${channel}:`, err);
      return false;
    }
  }

  joinChannelsOnReconnect() {
    if (this.socket?.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      // console.info(
      //   `RITESH : Rejoining ${this.channels.length} channels after reconnect`,
      // );
      this.channels.forEach((channel) => {
        // console.info(`RITESH Rejoining channel: ${channel}`);
        this.socket.send(
          JSON.stringify({
            type: 'channel_subscribe',
            channel,
          }),
        );
      });
    } catch (err) {
      // console.info('RITESH Error joining channels on reconnect:', err);
    }
  }

  processCachedMessages() {
    if (
      this.socket &&
      this.socket.readyState === WebSocket.OPEN &&
      this.messageQueue.length > 0
    ) {
      this.messageQueue.forEach((message) => {
        this.socket.send(message);
      });
      this.messageQueue = [];
    }
  }

  sendMessage(data) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      this.socket.send(message);
      return true;
    }

    this.messageQueue.push(
      typeof data === 'string' ? data : JSON.stringify(data),
    );
    return false;
  }

  startPingPong() {
    this.joinChannel(this.pingChannel);

    if (this.pingPongTimeout) {
      clearInterval(this.pingPongTimeout);
    }

    if (_isEmpty(this.userId)) {
      clearInterval(this.pingPongTimeout);
      return;
    }

    this.pingPongTimeout = setInterval(() => {
      if (this.socket?.readyState !== WebSocket.OPEN) {
        clearInterval(this.pingPongTimeout);
        return;
      }

      this.sendMessage({
        type: 'ping-pong',
        channel: this.pingChannel,
        data: {
          clientTime: Date.now(),
          currentActivity: getUserCurrentActivity({
            currentUrl: this.currentUrl,
          }),
          userId: _toString(this.userId),
        },
      });
    }, 5000);
  }

  subscribe(channel, callback) {
    if (!channel) {
      console.error('Cannot subscribe: channel name is empty');
      return () => {};
    }

    if (!callback || typeof callback !== 'function') {
      console.error('Cannot subscribe: callback is not a function');
      return () => {};
    }

    if (!this.listeners.has(channel)) {
      this.listeners.set(channel, new Set());
    }

    this.listeners.get(channel).add(callback);

    this.joinChannel(channel);

    return () => {
      const channelListeners = this.listeners.get(channel);
      if (channelListeners) {
        channelListeners.delete(callback);

        if (channelListeners.size === 0) {
          this.unsubscribeChannel(channel);
          this.listeners.delete(channel);
        }
      }
    };
  }

  subscribeToConnection(callback) {
    if (!this.connectionListeners) {
      this.connectionListeners = new Set();
    }

    this.connectionListeners.add(callback);

    callback(this.isConnected);

    return () => {
      if (this.connectionListeners) {
        this.connectionListeners.delete(callback);
      }
    };
  }

  notifyChannelListeners(channel, data) {
    const channelListeners = this.listeners.get(channel);
    if (channelListeners) {
      channelListeners.forEach((callback) => {
        try {
          if (_isFunction(callback)) {
            callback(data);
          }
        } catch (error) {
          console.error(`Error in channel listener for ${channel}:`, error);
        }
      });
    }
  }

  notifyConnectionListeners(isConnected) {
    if (this.connectionListeners) {
      this.connectionListeners.forEach((callback) => {
        try {
          callback(isConnected);
        } catch (error) {
          console.error('Error in connection listener:', error);
        }
      });
    }
  }

  getConnectionState() {
    return this.isConnected && this.socket?.readyState === WebSocket.OPEN;
  }

  getServerOffset() {
    return this.serverOffset;
  }
}

const webSocketManager = new WebSocketManager();

export const getCurrentTime = () =>
  Date.now() + _toNumber(webSocketManager.serverOffset);

export default webSocketManager;
