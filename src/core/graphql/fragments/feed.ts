import { gql } from '@apollo/client';
import { USER_PUBLIC_DETAIL_FRAGMENT } from './userPublicDetail';

export const CONNECTION_REQUEST_FRAGMENT = gql`
  fragment ConnectionRequestFields on ConnectionRequest {
    sentBy
  }
`;

export const FEED_ADDITIONAL_INFO_FRAGMENT = gql`
  fragment FeedAdditionalInfoFields on FeedAdditionalInfo {
    connectionRequest {
      ...ConnectionRequestFields
    }
  }
  ${CONNECTION_REQUEST_FRAGMENT}
`;

export const FEED_FOR_FRIENDS_FRAGMENT = gql`
  fragment FeedForFriendsFields on FeedForFriends {
    title
    body
  }
`;

export const FEED_DATA_FRAGMENT = gql`
  fragment FeedDataFields on FeedData {
    _id
    sentAt
    title
    description
    expirationTime
    sentFor
    lastLikedByUserName
    likesCount
    createdAt
    updatedAt
    additionalInfo {
      ...FeedAdditionalInfoFields
    }
    feedFor<PERSON>riends {
      ...FeedForFriendsFields
    }
  }
  ${FEED_ADDITIONAL_INFO_FRAGMENT}
  ${FEED_FOR_FRIENDS_FRAGMENT}
`;

export const USER_FEED_FRAGMENT = gql`
  fragment UserFeedFields on UserFeed {
    _id
    userId
    feedType
    feedReferenceId
    isLiked
    expirationTime
    imageUrl
    createdAt
    feedData {
      ...FeedDataFields
    }
  }
  ${FEED_DATA_FRAGMENT}
`;


export const FEED_RESPONSE_FRAGMENT = gql`
  fragment FeedResponseFields on FeedResponse {
    feeds {
      ...UserFeedFields
    }
    lastId
    hasMore
    userDetails {
      ...UserPublicDetailFields
    }
    isRead
  }
  ${USER_FEED_FRAGMENT}
  ${USER_PUBLIC_DETAIL_FRAGMENT}
`;
