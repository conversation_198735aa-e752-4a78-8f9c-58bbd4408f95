import { useQuery, gql } from '@apollo/client'
import _map from 'lodash/map'
import {useMemo} from "react";
import _sortBy from "lodash/sortBy";
import _isNil from "lodash/isNil";

const FETCH_DAILY_CHALLENGES_QUERY = gql`
        query GetDailyChallenges {
                getDailyChallenges {
                    _id
                    challengeStatus
                    hasAttempted
                    challengeNumber
                    division
                }
        }
`

const useFetchDailyChallengesQuery = () => {
    
    const { data, loading, error } = useQuery(FETCH_DAILY_CHALLENGES_QUERY, {
        fetchPolicy: 'cache-first',
    })

    const dailyChallengesData = data?.getDailyChallenges;

    const sortedDailyChallenge = useMemo(() => {
        if(_isNil(dailyChallengesData)) return dailyChallengesData;
        const sortedDailyChallenge = [...dailyChallengesData];
        return sortedDailyChallenge.sort?.((a) => {
            return a.hasAttempted ? 1 : -1;
        });
    }, [dailyChallengesData])

    return {
        challenges : sortedDailyChallenge,
        loading,
        error,
    }
}

export default useFetchDailyChallengesQuery