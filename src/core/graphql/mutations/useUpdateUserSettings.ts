import { gql, useMutation } from '@apollo/client'
import { userSettingsTypes } from 'core/types/userSettings'

const UPDATE_USER_SETTINGS = gql`
  mutation UpdateUserSettings($settings: UpdateSettingsInput!) {
    updateUserSettings(settings: $settings) {
      playSound
      hapticFeedback
    }
  }
`

export function useUpdateUserSettings() {
  const [updateSettings, { loading, error }] = useMutation(
    UPDATE_USER_SETTINGS,
    {
      onError: (error) => {
        console.error('Error updating user settings:', error)
      },
    }
  )

  const mutateSettings = async (settings: userSettingsTypes) => {
    try {
      const response = await updateSettings({
        variables: { settings },
      })
      return response.data.updateUserSettings
    } catch (error) {
      throw error
    }
  }

  return {
    updateSettings: mutateSettings,
    loading,
    error,
  }
}
