import { useEffect } from "react";
import initWasm from "wasm/index";
import useUserStore from "store/useUserStore";

const useLoadWasm = () => {
    const { isWasmReady, updateWasmReady } = useUserStore((state) => ({ isWasmReady: state.isWasmReady, updateWasmReady: state.updateWasmReady }));

    useEffect(() => {
        if (isWasmReady) return;
        (async () => {
            await initWasm();
            updateWasmReady(true);
        })()
    }, []);
}

export default useLoadWasm
