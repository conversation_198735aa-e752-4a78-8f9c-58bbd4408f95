import { useCallback } from 'react';
import Share from 'react-native-share';

const useNativeUrlSharing = ({ url }) => {
  const handleShare = useCallback(
    async ({ label = '' } = EMPTY_OBJECT) => {
      const shareContent = {
        message: `${label} ${url}`,
      };

      try {
        await Share.open(shareContent);
      } catch (error) {
        // console.error('Error sharing:', error);
      }
    },
    [url],
  );

  return {
    handleShare,
  };
};

export default useNativeUrlSharing;
