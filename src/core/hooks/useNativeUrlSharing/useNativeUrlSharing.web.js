import { useCallback } from 'react';
import * as Clipboard from 'expo-clipboard';

import { showToast, TOAST_TYPE } from 'molecules/Toast';

const useNativeUrlSharing = ({ url }) => {
  const copyToClipboard = useCallback(
    async ({ label = 'Copied game url to clipboard' } = EMPTY_OBJECT) => {
      await Clipboard.setStringAsync(url);
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description: label,
      });
    },
    [url],
  );

  const handleShare = useCallback(
    async ({
      label = "Let's play some math game on Matiks: 🚀 ",
      clipboardLabel,
    } = EMPTY_OBJECT) => {
      const urlData = {
        title: 'Matiks',
        text: label,
        url,
      };
      try {
        await navigator.share(urlData);
      } catch (err) {
        copyToClipboard({ label: clipboardLabel });
      }
    },
    [copyToClipboard, url],
  );

  return {
    handleShare,
  };
};

export default useNativeUrlSharing;