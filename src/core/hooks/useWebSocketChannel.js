import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import uuid from 'react-native-uuid';
import _toString from 'lodash/toString';
import WebSocketManager from '../services/WebSocketManager';

/**
 * Hook to subscribe to a specific WebSocket channel
 * @param {string} channel - The channel to subscribe to
 * @returns {object} - The latest message from the channel and connection state
 */
export const useWebSocketChannel = (channel) => {
  const [message, setMessage] = useState(null);
  const [isConnected, setIsConnected] = useState(
    WebSocketManager.getConnectionState(),
  );

  const channelRef = useRef(channel);
  channelRef.current = channel;

  useEffect(() => {
    if (!channel) return;

    const connectionUnsubscribe =
      WebSocketManager.subscribeToConnection(setIsConnected);

    const channelUnsubscribe = WebSocketManager.subscribe(channel, (data) => {
      setMessage(data);
    });

    return () => {
      connectionUnsubscribe();
      channelUnsubscribe();
    };
  }, [channel]);

  const sendMessage = useCallback((data) => {
    if (!channelRef.current) return false;

    return WebSocketManager.sendMessage({
      ...data,
      channel: channelRef.current,
    });
  }, []);

  return {
    message,
    isConnected,
    sendMessage,
  };
};

/**
 * Hook to subscribe to a channel and parse event/payload structure
 * @param {string} channel - The channel to subscribe to
 * @returns {object} - The parsed event and payload
 */
export const useChannelMessage = (channel) => {
  const { message, isConnected } = useWebSocketChannel(channel);

  return useMemo(() => {
    const eventWithPayload = {
      event: message?.type,
      payload: message?.event || {},
      eventId: _toString(uuid.v4()),
    };

    if (typeof eventWithPayload.payload === 'object') {
      eventWithPayload.payload.opponentUser =
        eventWithPayload.payload?.user || eventWithPayload.payload?.opponent;
    }

    return {
      ...eventWithPayload,
      isConnected,
    };
  }, [isConnected, message?.event, message?.type]);
};

/**
 * Hook to join a channel without subscribing to messages
 * @param {string} channel - The channel to join
 * @returns {boolean} - Whether the channel was joined successfully
 */
export const useJoinChannel = (channel) => {
  const [joined, setJoined] = useState(false);

  useEffect(() => {
    if (!channel) return;

    const connectionUnsubscribe = WebSocketManager.subscribeToConnection(
      (isConnected) => {
        if (isConnected) {
          const success = WebSocketManager.joinChannel(channel);
          setJoined(success);
        } else {
          setJoined(false);
        }
      },
    );

    return () => {
      connectionUnsubscribe();
      WebSocketManager.unsubscribeChannel(channel);
    };
  }, [channel]);

  return joined;
};

export default useWebSocketChannel;
