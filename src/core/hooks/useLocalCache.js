import {
  getStorageState,
  setStorageItemAsync,
} from 'core/hooks/useStorageState';
import { useCallback } from 'react';

const useLocalCache = (cacheKey) => {
  const getData = useCallback(async () => {
    try {
      const stringifiedData = await getStorageState(cacheKey);
      return JSON.parse(stringifiedData);
    } catch (error) {
      return null;
    }
  }, [cacheKey]);

  const setData = useCallback(
    async (data) => {
      try {
        if (data === null || data === undefined) {
          return await setStorageItemAsync(cacheKey, null);
        }
        const stringifiedData = JSON.stringify(data);
        return await setStorageItemAsync(cacheKey, stringifiedData);
      } catch (e) {
        return e;
      }
    },
    [cacheKey],
  );

  return {
    getData,
    setData,
  };
};

export default useLocalCache;
