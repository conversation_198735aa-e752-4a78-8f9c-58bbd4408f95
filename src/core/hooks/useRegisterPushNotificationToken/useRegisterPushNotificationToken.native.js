import { useCallback } from 'react';

import _isNil from 'lodash/isNil';
import _toString from 'lodash/toString';
import _isEmpty from 'lodash/isEmpty';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { useStorageState } from 'core/hooks/useStorageState';
import useRegisterDeviceToken from '../../graphql/mutations/useRegisterDeviceToken';
import useUnRegisterDeviceToken from '../../graphql/mutations/useUnregisterDeviceToken';

const DEVICE_TOKEN_KEY = 'fcmToken';

const useRegisterPushNotificationTokenNative = () => {
  const { userId } = useSession();
  const [deviceToken, setDeviceToken] = useStorageState(
    `${DEVICE_TOKEN_KEY}-${userId}`,
  );

  const deviceId = DeviceInfo.getDeviceId() || 'default-device-id';
  const platform = Platform.OS;

  const { registerDeviceToken } = useRegisterDeviceToken();
  const { unregisterDeviceToken } = useUnRegisterDeviceToken();

  const handleDeviceTokenRegistration = useCallback(
    async ({ deviceToken: deviceTokenFromArgs } = EMPTY_OBJECT) => {
      const newDeviceToken = _toString(deviceTokenFromArgs);

      if (newDeviceToken === deviceToken || _isEmpty(newDeviceToken)) {
        return;
      }
      registerDeviceToken({
        deviceToken: newDeviceToken,
        deviceId: _toString(deviceId),
        platform: _toString(platform),
      })
        .then((response) => {
          if (response?.data?.registerDeviceToken?.success) {
            setDeviceToken(_toString(newDeviceToken));
          }
        })
        .catch((error) => {
          // console.info('registerDeviceToken error: ', error);
        });
    },
    [registerDeviceToken, deviceToken],
  );

  const handleDeviceTokenUnRegistration = useCallback(() => {
    if (_isNil(deviceToken)) {
      return;
    }
    setDeviceToken(null);
    return unregisterDeviceToken({
      deviceToken,
      deviceId: _toString(deviceId),
    });
  }, [unregisterDeviceToken, deviceToken]);

  return {
    handleDeviceTokenRegistration,
    handleDeviceTokenUnRegistration,
  };
};

export default useRegisterPushNotificationTokenNative;
