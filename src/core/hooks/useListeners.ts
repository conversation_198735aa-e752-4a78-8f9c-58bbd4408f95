import { useEffect, useRef } from 'react';
import _includes from 'lodash/includes';
import { Platform } from 'react-native';

import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useHandleShowSharePledgeOverlay from '@/src/modules/resolution/hooks/useHandleShowSharePledgeOverlay';
import useHandlePushNotifications from 'core/hooks/useHandlePushNotifications';
import useHandleShowDownloadAppPopover from 'core/utils/useHandleShowDownloadAppPopover';
import usePlatformStats from 'core/graphql/queries/usePlatformStats';
import useWebsocketInit from '@/src/listeners/useWebsocketInit';
import useWebsocketStore from '@/src/store/useWebSocketStore';
import useUserListeners from '@/src/listeners/useUserListeners';
import useLoadWasm from './useLoadWasm';


const useListeners = () => {
    useWebsocketInit();
    usePlatformStats();
    useLoadWasm();
    useHandleShowDownloadAppPopover();
    useHandleShowSharePledgeOverlay();
    useHandlePushNotifications();
    useUserListeners();

    const offlineListenerRef = useRef<any>();

    const { connect, isConnected, ws } = useWebsocketStore((state) => ({
        connect: state.connect,
        isConnected: state.isConnected,
        ws: state.ws,
    }));
    const wsRef = useRef(ws);
    wsRef.current = ws;

    const { session } = useSession();

    useEffect(() => {
        if (!session) return;
        connect(session);
    }, [connect, session]);

    useEffect(() => {
        if (Platform.OS !== 'web' || offlineListenerRef.current) return;

        offlineListenerRef.current = window.addEventListener('offline', () => {
            printDebug('You are offline');
            if (wsRef.current) {
                wsRef.current?.close();
            }
        });
        return () => {
            if (!offlineListenerRef.current) return;
            offlineListenerRef.current?.removeEventListener('offline');
        }
    }, [isConnected])

};

export default useListeners;