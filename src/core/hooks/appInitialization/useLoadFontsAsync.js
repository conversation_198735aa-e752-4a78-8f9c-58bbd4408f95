import {useCallback} from "react";
import * as Font from 'expo-font';
import FontAwesome from "@expo/vector-icons/FontAwesome";

const useLoadFontsAsync = () => {
    const initializeFontAsync = useCallback(() => {
        return Font.loadAsync({
            // Load your custom fonts here
            SpaceMono: require('../../../../assets/fonts/SpaceMono-Regular.ttf'),
            'Montserrat-100': require('../../../../assets/fonts/Montserrat-Thin.ttf'),
            'Montserrat-200': require('../../../../assets/fonts/Montserrat-ExtraLight.ttf'),
            'Montserrat-300': require('../../../../assets/fonts/Montserrat-Light.ttf'),
            'Montserrat-400': require('../../../../assets/fonts/Montserrat-Regular.ttf'),
            'Montserrat-500': require('../../../../assets/fonts/Montserrat-Medium.ttf'),
            'Montserrat-600': require('../../../../assets/fonts/Montserrat-SemiBold.ttf'),
            'Montserrat-700': require('../../../../assets/fonts/Montserrat-Bold.ttf'),
            'Montserrat-800': require('../../../../assets/fonts/Montserrat-ExtraBold.ttf'),
            'Montserrat-900': require('../../../../assets/fonts/Montserrat-Black.ttf'),
            'BebasNeue-500': require('../../../../assets/fonts/BebasNeue-Regular.ttf'),
            ...FontAwesome.font,
        });
    }, []);

    return {
        initializeFontAsync
    }
}

export default useLoadFontsAsync;