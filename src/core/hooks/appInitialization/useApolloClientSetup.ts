import { useCallback, useEffect, useState } from 'react';
import {
  getStorageState,
  setStorageItemAsync,
} from 'core/hooks/useStorageState';
import _debounce from 'lodash/debounce';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { ApolloClient } from '@apollo/client';
import { authToken, createApolloClient } from '../../utils/apolloClient';
import { handleAsync } from 'core/utils/asyncUtils';
import { clearCentralStore } from 'store/storage';

let apolloClientRecreationCount = 0;

global.getApolloClient = () => {
  if (!apolloClient) {
    throw new Error('Apollo client not initialized');
  }
  return apolloClient;
};

let apolloClient: ApolloClient<object> | null = null;

global.getApolloClient = () => {
  if (!apolloClient) {
    throw new Error('Apollo client not initialized');
  }
  return apolloClient;
};

// TODO: handle different Apollo Client for Authorized and Un-Authorized routes
const useApolloClientSetup = () => {
  const [clientCreationError, setClientCreationError] = useState<Error | null>(null);

  const [client, setClient] = useState<ApolloClient<object> | null>(null);
  const [apiToken, setApiToken] = useState<string | null>(null);

  const onUnAuthorizationError = useCallback(
    _debounce(async () => {
      // refresh token
      apolloClientRecreationCount++;
      if (apolloClientRecreationCount >= 100) return;

      await setStorageItemAsync('session', null);
      const [_apolloClient, error]: [ApolloClient<object> | null, Error | null] = await handleAsync(createApolloClient, { newToken: null })
      if (error) {
        setClientCreationError(error);
        Analytics.track(ANALYTICS_EVENTS.APOLLO_CLIENT.FAILED_TO_CREATE, {
          error,
          errorMessage: error.message,
        });
        return;
      }
      setClientCreationError(null);
      apolloClient = _apolloClient;
      setClient(apolloClient);

    }, 500),
    [setClient, setClientCreationError],
  );

  const updateApolloClient = useCallback(
    async (newToken: string | null) => {
      setApiToken(newToken);
      clearCentralStore();
      authToken(newToken);
      await apolloClient?.clearStore();
      setClientCreationError(null);
      return apolloClient;
    },
    [setClient, onUnAuthorizationError, setClientCreationError],
  );

  const initializeApolloClient = useCallback(async () => {
    setClientCreationError(null);
    let authToken = await getStorageState('session');

    const [_apolloClient, error]: [ApolloClient<object> | null, Error | null] = await handleAsync(createApolloClient, { newToken: authToken, onUnAuthorizationError })
    if (error) {
      setClientCreationError(error);
      return error;
    }
    const oldApolloClient = apolloClient;
    setClientCreationError(null);
    apolloClient = _apolloClient;
    setClient(apolloClient);
    oldApolloClient?.stop();
    return apolloClient;
  }, [onUnAuthorizationError]);

  useEffect(() => {
    getStorageState('session').then((token) => {
      setApiToken(token ?? null);
    });
  }, []);

  return {
    apiToken,
    apolloClient: client,
    clientCreationError,
    initializeApolloClient,
    updateApolloClient,
  };
};

export default useApolloClientSetup;
