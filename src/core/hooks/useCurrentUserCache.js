import { useCallback } from 'react';
import useLocalCache from 'core/hooks/useLocalCache';
import _omit from 'lodash/omit';
import _isEmpty from 'lodash/isEmpty';

const USER_CACHE_KEY = 'user-cache';

const useCurrentUserCache = () => {
  const { getData, setData } = useLocalCache(USER_CACHE_KEY);

  const updateUserCache = useCallback(
    async ({ user }) => {
      if (user && !_isEmpty(user)) {
        const userDataToWrite = _omit(user, ['isSignup']);
        return await setData(userDataToWrite);
      } else {
        return await setData(null);
      }
    },
    [setData],
  );

  return {
    getUserFromCache: getData,
    updateUserCache,
  };
};

export default useCurrentUserCache;
