import { useCallback, useEffect, useRef } from 'react';
import { Platform } from 'react-native';
import SpInAppUpdates, { IAUUpdateKind } from 'sp-react-native-in-app-updates';
import DeviceInfo from 'react-native-device-info';
import Analytics from 'core/analytics';

let checkedForAppUpdate = false;

const useInAppUpdateChecker = () => {
  // Keep a reference to the inAppUpdates instance
  const inAppUpdatesRef = useRef(null);

  const handleInAppUpdate = useCallback(() => {
    if (__DEV__) {
      console.log('Skipping in-app update check in development mode');
      return;
    }

    // Enable debug mode to get more detailed logs
    const inAppUpdates = new SpInAppUpdates(true);
    inAppUpdatesRef.current = inAppUpdates;

    // Get current app version
    const currentAppVersion = DeviceInfo.getVersion();
    const buildNumber = DeviceInfo.getBuildNumber();

    Analytics.track('in-app-update: Checking for updates', {
      currentAppVersion,
      buildNumber,
      platform: Platform.OS,
    });

    // Pass the current version to the library for comparison
    inAppUpdates
      .checkNeedsUpdate({ curVersion: currentAppVersion })
      .then((result) => {
        // Get current app version
        const currentAppVersion = DeviceInfo.getVersion();
        const buildNumber = DeviceInfo.getBuildNumber();

        // Track the result of the update check
        Analytics.track('in-app-update: Check result', {
          shouldUpdate: result.shouldUpdate,
          storeVersion: result.storeVersion,
          currentAppVersion,
          buildNumber,
          platform: Platform.OS,
          isImmediateUpdateAllowed: result.isImmediateUpdateAllowed,
          isFlexibleUpdateAllowed: result.isFlexibleUpdateAllowed,
        });

        if (result.shouldUpdate) {
          // Check if update is allowed based on platform
          if (
            Platform.OS === 'android' &&
            !result.isImmediateUpdateAllowed &&
            !result.isFlexibleUpdateAllowed
          ) {
            const errorMsg = 'No update types are allowed on Android';
            console.log(errorMsg);
            const currentAppVersion = DeviceInfo.getVersion();
            const buildNumber = DeviceInfo.getBuildNumber();

            Analytics.track('in-app-update: Update not allowed', {
              error: errorMsg,
              currentAppVersion,
              buildNumber,
              platform: Platform.OS,
            });
            return;
          }

          // Determine the appropriate update type for Android
          const androidUpdateType = result.isImmediateUpdateAllowed
            ? IAUUpdateKind.IMMEDIATE
            : result.isFlexibleUpdateAllowed
              ? IAUUpdateKind.FLEXIBLE
              : null;

          if (Platform.OS === 'android' && androidUpdateType === null) {
            const errorMsg = 'No valid update type available for Android';
            const currentAppVersion = DeviceInfo.getVersion();
            const buildNumber = DeviceInfo.getBuildNumber();

            Analytics.track('in-app-update: Invalid update type', {
              error: errorMsg,
              currentAppVersion,
              buildNumber,
              platform: Platform.OS,
            });
            return;
          }

          const updateOptions = Platform.select({
            ios: {
              title: 'Update available',
              message:
                'There is a new version of the app available on the App Store, do you want to update it?',
              buttonUpgradeText: 'Update',
              buttonCancelText: 'Cancel',
            },
            android: {
              updateType: androidUpdateType,
            },
          });

          // Track that we're starting the update process
          const currentAppVersion = DeviceInfo.getVersion();
          const buildNumber = DeviceInfo.getBuildNumber();

          Analytics.track('in-app-update: Starting update', {
            platform: Platform.OS,
            currentAppVersion,
            buildNumber,
            updateType: Platform.OS === 'android' ? androidUpdateType : 'standard',
            storeVersion: result.storeVersion,
          });

          // For Android, add a status update listener to track the update progress
          if (Platform.OS === 'android') {
            inAppUpdates.addStatusUpdateListener((status) => {
              const currentAppVersion = DeviceInfo.getVersion();
              const buildNumber = DeviceInfo.getBuildNumber();

              Analytics.track('in-app-update: Status update', {
                status: status.status,
                bytesDownloaded: status.bytesDownloaded,
                totalBytesToDownload: status.totalBytesToDownload,
                currentAppVersion,
                buildNumber,
                storeVersion: result.storeVersion,
              });
            });
          }

          inAppUpdates
            .startUpdate(updateOptions)
            .then(() => {
              const currentAppVersion = DeviceInfo.getVersion();
              const buildNumber = DeviceInfo.getBuildNumber();

              Analytics.track('in-app-update: Update started successfully', {
                currentAppVersion,
                buildNumber,
                platform: Platform.OS,
                storeVersion: result.storeVersion,
              });
            })
            .catch((startError) => {
              const currentAppVersion = DeviceInfo.getVersion();
              const buildNumber = DeviceInfo.getBuildNumber();

              Analytics.track('in-app-update: Error starting update', {
                error: startError.message || JSON.stringify(startError),
                currentAppVersion,
                buildNumber,
                platform: Platform.OS,
                storeVersion: result.storeVersion,
              });
              console.error('Error starting update:', startError);
            });
        } else {
          const currentAppVersion = DeviceInfo.getVersion();
          const buildNumber = DeviceInfo.getBuildNumber();

          Analytics.track('in-app-update: No update needed', {
            currentAppVersion,
            buildNumber,
            platform: Platform.OS,
            storeVersion: result.storeVersion,
          });
        }
      })
      .catch((error) => {
        // Track the general error
        const currentAppVersion = DeviceInfo.getVersion();
        const buildNumber = DeviceInfo.getBuildNumber();

        Analytics.track('in-app-update: Error while checking in app updates', {
          error: error.message || JSON.stringify(error),
          currentAppVersion,
          buildNumber,
          platform: Platform.OS,
        });
        console.error('In-app update error:', error);

        // Track specific error types for better debugging
        if (error.message) {
          if (error.message.includes('Update type unavailable')) {
            const currentAppVersion = DeviceInfo.getVersion();
            const buildNumber = DeviceInfo.getBuildNumber();

            Analytics.track('in-app-update: Update type unavailable error', {
              error: error.message,
              currentAppVersion,
              buildNumber,
              platform: Platform.OS,
            });
          } else if (error.message.includes('NETWORK_ERROR')) {
            const currentAppVersion = DeviceInfo.getVersion();
            const buildNumber = DeviceInfo.getBuildNumber();

            Analytics.track('in-app-update: Network error', {
              error: error.message,
              currentAppVersion,
              buildNumber,
              platform: Platform.OS,
            });
          } else if (error.message.includes('API_NOT_AVAILABLE')) {
            const currentAppVersion = DeviceInfo.getVersion();
            const buildNumber = DeviceInfo.getBuildNumber();

            Analytics.track('in-app-update: API not available', {
              error: error.message,
              currentAppVersion,
              buildNumber,
              platform: Platform.OS,
            });
          } else if (error.message.includes('PLAY_STORE_NOT_INSTALLED')) {
            const currentAppVersion = DeviceInfo.getVersion();
            const buildNumber = DeviceInfo.getBuildNumber();

            Analytics.track('in-app-update: Play Store not installed', {
              error: error.message,
              currentAppVersion,
              buildNumber,
              platform: Platform.OS,
            });
          }
        }
      });
  }, []);

  useEffect(() => {
    if (checkedForAppUpdate) {
      const currentAppVersion = DeviceInfo.getVersion();
      const buildNumber = DeviceInfo.getBuildNumber();

      Analytics.track('in-app-update: Already checked, skipping', {
        currentAppVersion,
        buildNumber,
        platform: Platform.OS,
      });
      return;
    }

    checkedForAppUpdate = true;
    const currentAppVersion = DeviceInfo.getVersion();
    const buildNumber = DeviceInfo.getBuildNumber();

    Analytics.track('in-app-update: First check initiated', {
      currentAppVersion,
      buildNumber,
      platform: Platform.OS,
    });
    handleInAppUpdate();

    // Cleanup function to remove listeners when component unmounts
    return () => {
      if (Platform.OS === 'android' && inAppUpdatesRef.current) {
        inAppUpdatesRef.current.removeStatusUpdateListener();
      }
    };
  }, [handleInAppUpdate]);

  return null;
};

export default useInAppUpdateChecker;
