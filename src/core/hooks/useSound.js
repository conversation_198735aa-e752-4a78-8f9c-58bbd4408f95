import { useCallback, useEffect, useRef, useState } from 'react';
import { Audio } from 'expo-av';
import _get from 'lodash/get';
import { InteractionManager } from 'react-native';
import { useUserSettings } from '../contexts/UserSettingsContext';

const useSound = ({
  soundFile,
  config = EMPTY_OBJECT,
  playOnMount = false,
}) => {
  const soundRef = useRef(null);
  const isPlayingRef = useRef(false);
  const isMountedRef = useRef(true);
  const [isLoaded, setIsLoaded] = useState(false);
  const { userSettings } = useUserSettings();

  const soundEnabled = _get(userSettings, 'playSound', true);

  const playSound = useCallback(async () => {
    try {
      if (
        !isPlayingRef.current &&
        soundRef.current &&
        soundEnabled &&
        isLoaded
      ) {
        isPlayingRef.current = true;
        // Check if the sound object has the replayAsync method before calling it
        if (soundRef.current?.replayAsync) {
          await soundRef.current.replayAsync();
        }
        isPlayingRef.current = false;
      }
    } catch (error) {
      isPlayingRef.current = false;
      // Only log the error if it's not a "Player does not exist" error during unmount
      if (
        isMountedRef.current ||
        !error.message?.includes('Player does not exist')
      ) {
        console.error('Error playing sound:', error);
      }
    }
  }, [soundEnabled, isLoaded]);

  const loadSound = useCallback(async () => {
    try {
      if (!soundFile) return;

      // Unload any existing sound first
      if (soundRef.current) {
        try {
          soundRef.current.stopAsync()?.catch(() => {});
          await soundRef.current?.unloadAsync();
        } catch (error) {
          // Ignore unload errors
        }
        soundRef.current = null;
      }

      const { sound } = await Audio.Sound.createAsync(soundFile, config);
      if (isMountedRef.current) {
        soundRef.current = sound;
        setIsLoaded(true);

        if (playOnMount) {
          playSound();
        }
      } else {
        // Component unmounted during sound loading, clean up the sound
        try {
          await sound.unloadAsync();
        } catch (error) {
          // Ignore unload errors
        }
      }
    } catch (error) {
      console.error('Error loading sound:', error);
      setIsLoaded(false);
    }
  }, [playSound, config, playOnMount, soundFile]);

  const loadSoundRef = useRef(loadSound);
  loadSoundRef.current = loadSound;

  useEffect(() => {
    isMountedRef.current = true;
    loadSoundRef.current();

    return () => {
      isMountedRef.current = false;
      setIsLoaded(false);

      // Safely unload the sound when component unmounts
      if (soundRef.current) {
        // Use a local variable to avoid the "Player does not exist" error
        const sound = soundRef.current;
        soundRef.current = null;
        sound?.stopAsync()?.catch(() => {});

        InteractionManager.runAfterInteractions(() => {
          try {
            sound.unloadAsync();
          } catch (error) {
            // Ignore any errors during cleanup
          }
        });
      }
    };
  }, [soundFile]);

  return { playSound, isLoaded };
};

export default useSound;
