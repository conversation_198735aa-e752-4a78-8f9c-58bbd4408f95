import * as SecureStore from 'expo-secure-store';
import { useCallback, useEffect, useState } from 'react';
import { Platform } from 'react-native';

export async function setStorageItemAsync(key, value) {
  if (Platform.OS === 'web') {
    try {
      if (value === null) {
        return localStorage.removeItem(key);
      }
      return localStorage.setItem(key, value);
    } catch (e) {
      console.error('Local storage is unavailable:', e);
      return e;
    }
  } else {
    if (value == null) {
      return SecureStore.deleteItemAsync(key);
    }
    return SecureStore.setItemAsync(key, value);
  }
}

export const getStorageState = async (key) => {
  if (Platform.OS === 'web') {
    try {
      if (typeof localStorage !== 'undefined') {
        return localStorage.getItem(key);
      }
    } catch (e) {
      console.error('Local storage is unavailable:', e);
      return null;
    }
  } else {
    return SecureStore.getItemAsync(key);
  }
};

export function useStorageState(key) {
  // Public
  const [state, setState] = useState();

  // Get
  useEffect(() => {
    if (Platform.OS === 'web') {
      try {
        if (typeof localStorage !== 'undefined') {
          setState(localStorage.getItem(key));
        }
      } catch (e) {
        console.error('Local storage is unavailable:', e);
      }
    } else {
      SecureStore.getItemAsync(key).then((value) => {
        setState(value);
      });
    }
  }, [key]);

  // Set
  const setValue = useCallback(
    async (value) => {
      setState(value);
      await setStorageItemAsync(key, value);
    },
    [key],
  );

  return [state, setValue];
}
