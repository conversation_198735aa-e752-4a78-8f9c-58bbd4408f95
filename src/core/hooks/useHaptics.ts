import { useCallback } from 'react';
import userSettingsReader from 'core/readers/userSettingsReader';
import { useUserSettings } from '../contexts/UserSettingsContext';
import Haptics from '../container/Haptics';

export type ImpactHapticStyle = keyof typeof Haptics.ImpactFeedbackStyle;

const useHaptics = () => {
  const { userSettings } = useUserSettings();
  const hapticsEnabled = userSettingsReader.hapticFeedback(userSettings);

  const triggerHaptic = useCallback(
    (style: ImpactHapticStyle = Haptics.ImpactFeedbackStyle.Light) => {
      if (hapticsEnabled) {
        Haptics.impactAsync(style);
      }
    },
    [hapticsEnabled],
  );

  return {
    triggerHaptic,
  };
};

export default useHaptics;
