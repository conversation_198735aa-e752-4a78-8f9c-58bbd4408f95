import * as Application from 'expo-application';
import { useCallback, useEffect, useRef, useState } from 'react';
import Analytics from 'core/analytics';
import _split from 'lodash/split';
import _startsWith from 'lodash/startsWith';

function extractReferrer(referrerString) {
  if (!referrerString || typeof referrerString !== 'string') {
    return null;
  }

  const params = _split(referrerString, '&');

  for (const param of params) {
    if (param.startsWith('referrer=')) {
      return param.substring(9);
    }
  }

  return null;
}

export const useReadReferralCode = () => {
  const [referralCode, setReferralCode] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchReferralCode = useCallback(async () => {
    try {
      const referrerParams = await Application.getInstallReferrerAsync();
      const referrerString = extractReferrer(referrerParams);

      Analytics.track('Install Referrer', { referrerParams });
      if (referrerString && _startsWith(referrerString, 'referralCode_')) {
        const parts = _split(referrerString, 'referralCode_');
        const referrerCode = parts && parts.length > 1 ? parts[1] : null;

        if (referrerCode) {
          setReferralCode(referrerCode);
        }
      }
    } catch (err) {
      setError('Failed to fetch referral code');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchReferralCodeRef = useRef(null);
  fetchReferralCodeRef.current = fetchReferralCode;

  useEffect(() => {
    fetchReferralCodeRef.current();
  }, []);

  return { referralCode, loading, error };
};
