import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import { useCallback } from 'react';
import Analytics from 'core/analytics/Analytics';
import { Linking } from 'react-native';

const useInviteFriendOnMatiks = () => {
  const { user } = useSession();
  const referralCode = userReader.referralCode(user) ?? 'CODE';

  const referralUrl = `https://www.matiks.com/apps?referrer=${encodeURIComponent(`referralCode_${referralCode}`)}`;

  const BASE_REFERRAL_LINK = `Here's your special reward on Matiks⚡! Ready to play?
  1. Download the Matiks App from here: ${referralUrl}
  2. Use my invite code: ${referralCode}
  3. Earn a streak freezer as a reward!
  Let's play math together! 🎯`;

  const { handleShare: shareLink } = useNativeUrlSharing({
    url: BASE_REFERRAL_LINK,
  });

  const clipboardLabel = 'Referral Code Copied Successfully!!!';

  const handleNormalShare = useCallback(
    ({ eventToBeTracked = '' }) => {
      Analytics.track(eventToBeTracked, {
        referralCode,
        method: 'normal share',
      });

      shareLink?.({ clipboardLabel });
    },
    [shareLink, referralCode, clipboardLabel],
  );

  const shareThroughWhatsapp = useCallback(
    ({ eventToBeTracked = '' }) => {
      Analytics.track(eventToBeTracked, {
        referralCode,
        method: 'share via whatsapp',
      });

      const encodedMessage = encodeURIComponent(BASE_REFERRAL_LINK);

      const whatsappUrl = `whatsapp://send?text=${encodedMessage}`;

      Linking.openURL(whatsappUrl).catch(() => {
        shareLink?.({ clipboardLabel });
      });
    },
    [BASE_REFERRAL_LINK, shareLink, referralCode, clipboardLabel],
  );

  return {
    handleNormalShare,
    shareThroughWhatsapp,
  };
};

export default useInviteFriendOnMatiks;
