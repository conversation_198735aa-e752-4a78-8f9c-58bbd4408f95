import { useApolloClient } from '@apollo/client';
import { useCallback } from 'react';

const useUpdateSearchedUserCache = ({ username }) => {
  const apolloClient = useApolloClient();
  const updateUserCache = useCallback(
    (updatedUserFields) => {
      try {
        apolloClient.cache.modify({
          id: apolloClient.cache.identify({
            __typename: 'SearchUserOutput',
            username,
          }),
          fields: {
            getUserByUsername: (existingData) => {
              if (!existingData) return existingData;

              return {
                ...existingData,
                userPublicDetails: {
                  ...existingData.userPublicDetails,
                  ...updatedUserFields,
                },
              };
            },
          },
        });
      } catch (error) {
        // console.info('Error updating cache:', error)
      }
    },
    [username, apolloClient],
  );

  const updateUserAdditionalInfo = useCallback(
    (updatedAdditionalInfo) => {
      try {
        apolloClient.cache.modify({
          id: apolloClient.cache.identify({
            __typename: 'SearchUserOutput',
            username,
          }),
          fields: {
            getUserByUsername: (existingData) => {
              if (!existingData) return existingData;

              return {
                ...existingData,
                ...updatedAdditionalInfo,
              };
            },
          },
        });
      } catch (error) {
        // console.info('Error updating additional info in cache:', error);
      }
    },
    [username, apolloClient],
  );

  return {
    updateUserCache,
    updateUserAdditionalInfo,
  };
};

export default useUpdateSearchedUserCache;
