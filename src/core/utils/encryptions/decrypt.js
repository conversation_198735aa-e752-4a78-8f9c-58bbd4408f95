import CryptoJS from 'crypto-js';

// Function to get the secret key in a more obfuscated way
const getSecretKey = () => {
  // Split the key into parts to make it harder to find with simple string searches
  const part1 = 'S1E9C5R';
  const part2 = '@E@T*K)E';
  const part3 = '(YS1E9C5R';
  const part4 = '^E@T*K)E';

  return [part1, part2, part3, part4].join('');
};

export const decryptJsonData = (encryptedData) => {
  // Get the key using our obfuscated method
  const secretKey = getSecretKey();

  const [ivHex, encryptedHex] = encryptedData.split(':');
  const iv = CryptoJS.enc.Hex.parse(ivHex);
  const encrypted = CryptoJS.enc.Hex.parse(encryptedHex);

  const decrypted = CryptoJS.AES.decrypt(
    { ciphertext: encrypted },
    CryptoJS.enc.Utf8.parse(secretKey),
    { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 },
  );

  // Return the decrypted message as a UTF-8 string
  const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);

  try {
    return JSON.parse(decryptedString); // Return object
  } catch (error) {
    console.error('Error parsing decrypted data:', error);
    return decryptedString; // Return string if parsing fails
  }
};
