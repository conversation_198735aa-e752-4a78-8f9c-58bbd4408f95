import _trim from 'lodash/trim';
import _isNil from 'lodash/isNil';
import _isEqual from 'lodash/isEqual';

/**
 * Trims spaces from both ends of a string and removes leading zeros
 * only when there are non-zero elements following them.
 * Preserves a single zero if the string is just "0".
 *
 * @param {string} str - The string to trim
 * @returns {string} - The trimmed string with leading zeros removed when applicable
 */
const trimSpacesAndLeadingZeros = (str: string): string => {
  if (!str) return '';

  const trimmed = _trim(str);

  if (_isNil(trimmed)) return '';

  if (_isEqual(trimmed, '0')) return '0';

  return trimmed;
};

export default trimSpacesAndLeadingZeros;
