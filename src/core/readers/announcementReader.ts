import _property from 'lodash/property';
import _get from 'lodash/get';
import {
  Announcement,
  AnnouncementType,
  CallToAction,
  CTAActionType,
} from '../types/announcement';

const announcementReader = {
  // Basic fields
  id: _property('id'),
  type: _property('type'),
  title: _property('title'),
  description: _property('description'),
  riveAnimationUrl: _property('riveAnimationUrl'),
  imageUrl: _property('imageUrl'),
  mediaUrl: _property('mediaUrl'),
  priority: _property('priority'),

  // Timestamp fields
  createdAt: _property('createdAt'),
  publishedAt: _property('publishedAt'),
  expiresAt: _property('expiresAt'),

  // CTA related fields
  ctas: _property('ctas'),

  // Helper functions
  getFirstCTA: (announcement: any): CallToAction | undefined =>
    _get(announcement, ['ctas', 0]),

  getPrimaryCTA: (announcement: any): CallToAction | undefined =>
    _get(announcement, 'ctas', []).find(
      (cta: CallToAction) => cta.style === 'primary',
    ),

  hasValidCTAs: (announcement: any): boolean =>
    Array.isArray(_get(announcement, 'ctas')) &&
    _get(announcement, 'ctas', []).length > 0,

  isPublished: (announcement: any): boolean => {
    const publishedAt = new Date(
      _get(announcement, 'publishedAt', ''),
    ).getTime();
    const now = Date.now(getCurrentTime());
    return publishedAt <= now;
  },

  isExpired: (announcement: any): boolean => {
    const expiresAt = new Date(_get(announcement, 'expiresAt', '')).getTime();
    const now = Date.now(getCurrentTime());
    return expiresAt <= now;
  },

  isActive: (announcement: any): boolean =>
    announcementReader.isPublished(announcement) &&
    !announcementReader.isExpired(announcement),

  isFeatureUpdate: (announcement: any): boolean =>
    _get(announcement, 'type') === AnnouncementType.FEATURE_UPDATE,

  isMaintenanceNotice: (announcement: any): boolean =>
    _get(announcement, 'type') === AnnouncementType.MAINTENANCE,

  hasExternalLink: (announcement: any): boolean =>
    _get(announcement, 'ctas', []).some(
      (cta: CallToAction) => cta.actionType === CTAActionType.OPEN_URL,
    ),

  hasInternalNavigation: (announcement: any): boolean =>
    _get(announcement, 'ctas', []).some(
      (cta: CallToAction) => cta.actionType === CTAActionType.NAVIGATE_INTERNAL,
    ),

  // Sort helper
  sortByPriority: (a: any, b: any): number => {
    const priorityA = _get(a, 'priority', 0);
    const priorityB = _get(b, 'priority', 0);
    return priorityB - priorityA;
  },

  getSortedAnnouncementCTAsByPriority: (
    announcement: Announcement,
  ): CallToAction[] =>
    [...announcementReader.ctas(announcement)].sort((a, b) => {
      const styleA = _get(a, 'style', 'default');
      const styleB = _get(b, 'style', 'default');

      if (styleA === 'primary' && styleB !== 'primary') return -1;
      if (styleA !== 'primary' && styleB === 'primary') return 1;
      if (styleA === 'secondary' && styleB === 'default') return -1;
      if (styleA === 'default' && styleB === 'secondary') return 1;
      return 0;
    }),
};

export default announcementReader;
