import React, { createContext, useContext, useEffect, useMemo } from 'react';
import { usePathname } from 'expo-router';
import useUserStore from '@/src/store/useUserStore';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { WS_URL_2 } from '../constants/graphql';
import WebSocketManager from '../services/WebSocketManager';

const WebSocketContext = createContext(null);

const WebSocketProvider = ({ children, url }) => {
  const { userId, session: token } = useSession();
  const currentUrl = usePathname();
  const { isOnline } = useUserStore((state) => ({
    isOnline: state.isOnline,
  }));

  useEffect(() => {
    if (userId && token && isOnline) {
      WebSocketManager.initialize(url, token, userId);
    }
  }, [url, token, userId, isOnline]);

  useEffect(() => {
    WebSocketManager.setCurrentUrl(currentUrl);
  }, [currentUrl]);

  useEffect(() => {
    if (userId && token) {
      WebSocketManager.startPingPong();
    }
  }, [userId, token]);

  const value = useMemo(
    () => ({
      isConnected: WebSocketManager.getConnectionState(),
      lastMessage: EMPTY_OBJECT,
      sendMessage: WebSocketManager.sendMessage.bind(WebSocketManager),
      connect: WebSocketManager.connect.bind(WebSocketManager),
      disconnect: WebSocketManager.disconnect.bind(WebSocketManager),
      joinChannel: WebSocketManager.joinChannel.bind(WebSocketManager),
      unsubscribeChannel:
        WebSocketManager.unsubscribeChannel.bind(WebSocketManager),
      serverOffset: WebSocketManager.getServerOffset(),
    }),
    [],
  );

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const WebsocketContextWrapper = ({ children }) => (
  <WebSocketProvider url={WS_URL_2}>{children}</WebSocketProvider>
);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};
