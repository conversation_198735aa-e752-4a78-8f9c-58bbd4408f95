import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'
import { showToast, TOAST_TYPE } from 'molecules/Toast'
import { userSettingsTypes } from 'core/types/userSettings'
import _isNil from 'lodash/isNil'
import useFetchUserSettings from '../graphql/queries/useFetchUserSettings'
import { useUpdateUserSettings } from '../graphql/mutations/useUpdateUserSettings'

interface UserSettingsContextType {
  userSettings: userSettingsTypes
  setSettings: () => void
  useSettingsQueryMeta: {
    loading: boolean
    error?: Error
  }
  useSettingsMutationMeta: {
    loading: boolean
    error?: Error
  }
}

const defaultContextValue: UserSettingsContextType = {
  userSettings: {
    playSound: true,
    hapticFeedback: true,
  },
  setSettings: () => {},
  useSettingsQueryMeta: {
    loading: false,
    error: undefined,
  },
  useSettingsMutationMeta: {
    loading: false,
    error: undefined,
  },
}

const UserSettingsContext =
  createContext<UserSettingsContextType>(defaultContextValue)

export const UserSettingsProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [settings, setSettingsState] = useState<userSettingsTypes>(
    defaultContextValue.userSettings
  )
  const {
    userSettings,
    loading: queryLoading,
    error: queryError,
  } = useFetchUserSettings()
  const {
    updateSettings,
    loading: mutationLoading,
    error: mutationError,
  } = useUpdateUserSettings()
  const initializationOnComplete = useRef(false)

  useEffect(() => {
    if (!_isNil(userSettings) && !initializationOnComplete.current) {
      const sanitizedUserSettings = {
        playSound: Boolean(userSettings.playSound),
        hapticFeedback: Boolean(userSettings.hapticFeedback),
      }
      setSettingsState(sanitizedUserSettings)
      initializationOnComplete.current = true
    }
  }, [userSettings])

  const setSettings = useCallback(
    async (newSettings: Partial<userSettingsTypes>) => {
      try {
        setSettingsState((prevSettings) => ({
          ...prevSettings,
          ...newSettings,
        }))

        const result = await updateSettings(newSettings)
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: 'Settings updated successfully',
        })
      } catch (error) {
        console.error('Failed to update settings:', error)
        if (userSettings) {
          setSettingsState(userSettings)
        }
      }
    },
    [updateSettings, userSettings]
  )

  const value = React.useMemo(
    () => ({
      userSettings: settings,
      setSettings,
      useSettingsQueryMeta: {
        loading: queryLoading,
        error: queryError,
      },
      useSettingsMutationMeta: {
        loading: mutationLoading,
        error: mutationError,
      },
    }),
    [
      settings,
      setSettings,
      queryLoading,
      queryError,
      mutationLoading,
      mutationError,
    ]
  )

  return (
    <UserSettingsContext.Provider value={value}>
      {children}
    </UserSettingsContext.Provider>
  )
}

export function useUserSettings(): UserSettingsContextType {
  const context = useContext(UserSettingsContext)
  if (!context) {
    throw new Error(
      'useUserSettings must be used within a UserSettingsProvider'
    )
  }
  return context
}
