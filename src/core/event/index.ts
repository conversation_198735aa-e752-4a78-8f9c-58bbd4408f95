import { EventEmitter } from 'events';
import { EventData, EventName } from './types';

export interface EventManagerType {
  on(
    event: EventName,
    namespace: string,
    listener: (data: EventData) => void,
  ): void;
  off(
    event: EventName,
    namespace: string,
    listener: (data: EventData) => void,
  ): void;
  emit(event: EventName, data: EventData): void;
}

type EventFuncInfo = {
  event: EventName;
  namespace: string;
  unsubscribe: () => void;
};

export default class EventManager implements EventManagerType {
  private eventEmitter!: EventEmitter;
  private static instance: EventManager;
  private listeners: Map<EventName, Map<string, (data: any) => void>> =
    new Map();

  constructor() {
    if (EventManager.instance) {
      return EventManager.instance;
    }
    this.eventEmitter = new EventEmitter();
    EventManager.instance = this;
    this.listeners = new Map<EventName, Map<string, (data: any) => void>>();
  }

  on(event: EventName, namespace: string, listener: (data: EventData) => void) {
    const eventFuncInfo: EventFuncInfo = {
      event,
      namespace,
      unsubscribe: () => {
        this.off(event, namespace);
      },
    };
    let eventListeners = this.listeners.get(event);
    if (!eventListeners) {
      eventListeners = new Map<string, (data: any) => void>();
      this.listeners.set(event, eventListeners);
    }
    if (eventListeners.has(namespace)) {
      return eventFuncInfo;
    }
    eventListeners.set(namespace, listener);
    this.eventEmitter.on(event, listener);
    this.listeners.set(event, eventListeners);
    return eventFuncInfo;
  }

  off(event: EventName, namespace: string) {
    const eventListeners = this.listeners.get(event);
    if (!eventListeners || !eventListeners.has(namespace)) {
      return;
    }
    const listener = eventListeners.get(namespace);
    if (!listener) {
      return;
    }
    this.eventEmitter.off(event, listener);
    eventListeners.delete(namespace);
  }

  emit(event: EventName, data: EventData) {
    this.eventEmitter.emit(event, data);
  }

  offMany(eventFuncInfo: (EventFuncInfo | undefined)[]) {
    eventFuncInfo.forEach((eventFuncInfo) => {
      if (!eventFuncInfo) {
        return;
      }
      this.off(eventFuncInfo.event, eventFuncInfo.namespace);
    });
  }
}
