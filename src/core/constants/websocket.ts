import { WEBSOCKET_EVENTS } from "./events";

export const WEBSOCKET_CHANNELS = {
  UserEvents: (userId: string) => `${WEBSOCKET_EVENTS.USER_EVENT}_${userId}`,
  GameEvents: (gameId: string) => `${WEBSOCKET_EVENTS.GAME_EVENT}_${gameId}_V2`,
  PuzzleGameEvents: (gameId: string) =>
    `${WEBSOCKET_EVENTS.PUZZLE_GAME_EVENT}_${gameId}`,
  ShowdownEvents: (showdownId: string) =>
    `${WEBSOCKET_EVENTS.SHOWDOWN_EVENT}_${showdownId}`,
  OnlineUsers: () => `${WEBSOCKET_EVENTS.ONLINE_USERS}`,
  GroupChatEvents: (gameId: string) => `${WEBSOCKET_EVENTS.GROUP_CHAT_EVENT}_${gameId}_V2`,
  ShowdownLeaderboard: (showdownId: string, page: number) => `${WEBSOCKET_EVENTS.SHOWDOWN_EVENT}_${showdownId}_${page}`,
};
