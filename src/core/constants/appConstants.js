import { Platform } from 'react-native';

export const BASE_URL = 'https://www.matiks.in';
// export const BASE_URL = 'http://localhost:8081'
export const INITIAL_ROUTE_KEY = 'initialRoute';

export const SHOWN_NOTIFICATION_PERMISSION = 'shown_notification_permission';
export const NOTIFICATION_CONFIG = 'notification_config';
export const PRACTICE_PRESET_UPDATE_INFO_MODAL_SHOWN =
  'practice_preset_update_info_modal_shown';

export const BLOGS_ROUTE = 'https://blogs.matiks.com/';

export const DISCORD_COMMUNITY_URL = 'https://discord.gg/uAxvM8DZ2S';
export const FEEDBACK_FORM_URL = 'https://forms.gle/3dfF8rzRmCqNZxGB7';
export const PLAYSRORE_LINK =
  'https://play.google.com/store/apps/details?id=com.matiks.app&utm_source=website&utm_medium=redirect&utm_campaign=app_download';
export const APPSTORE_LINK =
  'https://apps.apple.com/in/app/matiks/id6738620563?utm_source=website&utm_medium=redirect&utm_campaign=app_download';
export const CREATOR_PROGRAMME_URL = 'https://docs.google.com/forms/d/1GU7eHpmt3HvuEYuPXZa8nvQ8HVr19nYXdRtnjhvAETA/edit';
export const MOBILE_DRAWER_VISITS_KEY = 'mobileDrawerVisits'
export const NEW_BUTTON_CLICKED_KEY = 'newButtonClicked';

export const getSiteUrl = () => {
  if (Platform.OS === 'web') {
    const { protocol } = window.location; // e.g., 'https:'
    const { hostname } = window.location; // e.g., 'matiks.com'
    return `${protocol}//${hostname}`;
  }
  return BASE_URL;
};
