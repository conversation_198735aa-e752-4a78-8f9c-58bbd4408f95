import { PRESET_CATEGORY } from 'core/constants/presets';

export const QUESTION_CATEGORIES = {
  ADD_AND_SUBSTRACT: 'ADD_AND_SUBSTRACT',
  MULTIPLICATION: 'MULTIPLICATION',
  DIVISION: 'DIVISION',
  ROOT: 'ROOT',
  DMAS: 'DMAS',
  FLASH_ANZAN: 'FLASH_ANZAN',
  EXPONENT: 'EXPONENT',
  MOD: 'MOD',
  LCM: 'LCM',
  HCF: 'HCF',
  PRIME_FACTORIZATION: 'PRIME_FACTORIZATION',
  SUM_OF_SQUARES: 'SUM_OF_SQUARES',
  MULTI_OPERATORS: 'MULTIPLE_OPERATORS',
  FRACTION: 'FRAC',
  MCQ_WORD_PROBLEM: 'MCQ_WORD_PROBLEM',
} as const;

export type QuestionCategoryType = keyof typeof QUESTION_CATEGORIES;

export const ABILITY_QUESTION_CATEGORY = {
  ROOT: 'ROOT',
  EXPONENT: 'EXPONENT',
  MOD: 'MOD',
  LCM: 'LCM',
  HCF: 'HCF',
  FRACTION: 'FRAC',
  PRIME_FACTORIZATION: 'PRIME_FACTORIZATION',
  SUM_OF_SQUARES: 'SUM_OF_SQUARES',
  MULTI_OPERATORS: 'MULTIPLE_OPERATORS',
} as const;

export const PRESET_IDENTIFIER_MAP = {
  ROOT: ABILITY_QUESTION_CATEGORY.ROOT,
  EXPO: ABILITY_QUESTION_CATEGORY.EXPONENT,
  MOD: ABILITY_QUESTION_CATEGORY.MOD,
  LCM: ABILITY_QUESTION_CATEGORY.LCM,
  HCF: ABILITY_QUESTION_CATEGORY.HCF,
  FRAC: ABILITY_QUESTION_CATEGORY.FRACTION,
  MULOP: ABILITY_QUESTION_CATEGORY.MULTI_OPERATORS,
  PF: ABILITY_QUESTION_CATEGORY.PRIME_FACTORIZATION,
  SOS: ABILITY_QUESTION_CATEGORY.SUM_OF_SQUARES,
};

export const PRESEST_VS_QUESTION_CATEGORY = {
  [PRESET_CATEGORY.ADDSUB]: QUESTION_CATEGORIES.DMAS,
  [PRESET_CATEGORY.MULT]: QUESTION_CATEGORIES.DMAS,
  [PRESET_CATEGORY.DIV]: QUESTION_CATEGORIES.DMAS,
  [PRESET_CATEGORY.FLASH_ANZAN]: QUESTION_CATEGORIES.DMAS,
  [PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB]: QUESTION_CATEGORIES.DMAS,
  [PRESET_CATEGORY.ROOT]: QUESTION_CATEGORIES.ROOT,
  [PRESET_CATEGORY.EXPONENT]: QUESTION_CATEGORIES.EXPONENT,
  [PRESET_CATEGORY.MOD]: QUESTION_CATEGORIES.MOD,
  [PRESET_CATEGORY.FRAC]: QUESTION_CATEGORIES.FRACTION,
} as const;
