import PUZZLE_COLORS from "./moduels/puzzle";

export default {
  dark: true,
  colors: {
    primary: '#292929',
    secondary: '#A9F99E',
    cardBackground: '#2b2b2b',
    tertiary: '#3A3A3A',
    secondaryButtonBorder: '#4B4B4B',
    background: '#1E1E1E',
    headerBackground: '#1c1c1c',
    gradientBackground: '#242424',
    success: '#B9FACA',
    error: '#FFC5C5',
    searchPlaceholder: '#666666',
    errorDark: '#f85656',
    errorBackground: '#E56C6C',
    card: '#1E1E1E',
    text: 'rgb(229, 229, 231)',
    textLight: '#FFFFFF',
    streak: '#E29B36',
    streakOrangeColor: '#FFAA48',
    d3ButtonBorderDark: '#787878',
    staticCoins: '#FFF335',
    textDark: '#BABABA',
    textDarkHighLighted: '#dcdcdc',
    warning: '#ffa500',
    placeholder: '#777',
    inputPlaceholder: '#5b5b5b',
    border: 'rgb(39, 39, 41)',
    notification: 'rgb(255, 69, 58)',
    red: '#FF5050',
    lightRed: '#FF9595',
    green: '#388E3C',
    messageUserNameText: '#FF63AF',
    gradientLeft: '#AAF99E',
    gradientRight: '#46ADD5',
    stroke: '#45AED5',
    offWhite: '#D9D9D9',
    contestText: '#4B269C',
    accent: '#00B2A9',
    graphRedBg: '#F9BE9E',
    inGameIndicator: '#FFEB3B',
    skyBlueBg: '#45AED5',
    darkGreen: '#2D542E',
    puzzleFilledGridColor: '#DDDF64',
    puzzleVacantGridColor: '#FEFFC4',
    puzzleCorrectFilledColor: '#63FC8C',
    puzzleWrongFilledColor: '#E56C6C',
    selectedGridBorderColor: '#088926',
    tabButtonGradient: '#63FC8C',
    firstPlaceShadow: '#FFD700',
    firstPlaceBorderColor: '#F9C440',
    thirdPlaceBorderColor: '#F2974A',
    swissTournamentBorderColor: '#FBD43F',
    contestTournamentBorderColor: '#9665FF',
    textDarkWithLowOpacity: '#C6CCC5',
    yellow: '#FBDD55',

    darkBg: '#262626',

    timerColor: '#00D9FF',
    linkColor: '#2980b9',
    headerOpponentBorderColor: '#F53C7F',
    headerUserBorderColor: '#57EACD',
    defeatColor: '#FF6549',
    victoryColor: '#58EACC',
    metricsBackgroundColor: '#4B4B4B',
    secondaryLight: '#A9F99E1A',
    incorrectBackground: '#FF26001A',
    crossMathPuzzleNewTextBg: '#025152',

    GradientCardBlue: '#00D9FF',
    GradientCardGreen: '#A9F99E',
    purple: '#AB5EEA',
    purpleLight: '#AB5EEA05',
    purpleLight2: '#9493D4',
    golden: '#DF9020',
    demotionText: '#FF4A60',
    leagueText: '#E3BDA9',

    orangeGradientTextColors: ['#F9DA9E', '#F05F5F'],
    containerBorderGradientColors: ['#46ADD5', '#AAF99E'],
    placeholderShimmerColors: ['#3A3A3A', '#4a4949', '#595858'],
    dailyChallengePlaceholderShimmerColors: ['#26464b', '#265656', '#395757'],
    congratulaotory: ['#F9DA9E', '#F05F5F'],
    contestMainPageGradient: ['#3D2570', '#1E1E1E'],
    showdownDetailPageGradient: ['#A78A30C2', '#2E251A40'],
    contestLogoBgGradient: ['#EBE1FF', '#7E41FF'],
    leagueBgGradient: ['#2F5D76', '#1E1E1E'],
    showdownLogoGradient: ['#FFDFC2', '#FFA600'],
    ratingFixtureGradientCompact: ['#90D0FF', '#90D0FF'],
    ratingFixtureGradientExpanded: ['#2C4151E5', '#2C4151'],
    headerLogoIconGradient: ['#F2C3A5', '#DF9020'],
    buttonBorderGradient: ['#A9F99E', '#00D9FF'],
    puzzleTabGradient: ['#AB5EEA', '#00D9FF'],
    earnStreakText: ['#F2C3A5', '#DF9020'],
    instagramGradientColor: ['#f9ce34', '#ee2a7b', '#6228d7'],

    ...PUZZLE_COLORS,
    game: {
      share: {
        storyBackgroundColorTop: '#2D542E',
        storyBackgroundColorBottom: '#558156',
      },
    },
  },
};
