import { useEffect } from 'react';
import { GrowthBook, GrowthBookProvider } from '@growthbook/growthbook-react';
import userReader from 'core/readers/userReader';
import { useSession } from 'modules/auth/containers/AuthProvider';

// Create a GrowthBook instance
const gb = new GrowthBook({
  apiHost: process.env.EXPO_PUBLIC_GROWTHBOOK_HOST,
  clientKey: process.env.EXPO_PUBLIC_GROWTHBOOK_CLIENT_KEY,
  // Only required for A/B testing
  // Called every time a user is put into an experiment
  trackingCallback: (experiment, result) => {
    console.info('Experiment Viewed', {
      experimentId: experiment.key,
      variationId: result.key,
    });
  },
});

gb.init();

const WithGrowthBookProvider = (props) => {
  const { children } = props;
  const { user } = useSession();
  useEffect(() => {
    gb.setAttributes({
      ...(user ?? EMPTY_OBJECT),
      id: userReader.id(user),
    });
  }, [user]);

  return <GrowthBookProvider growthbook={gb}>{children}</GrowthBookProvider>;
};

export default WithGrowthBookProvider;
