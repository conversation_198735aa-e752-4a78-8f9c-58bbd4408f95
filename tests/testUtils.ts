import { Response } from '@playwright/test';

export const waitForApiResponse = async (
  res: Response,
  options: {
    urlIncludes: string;
    method: string;
    validateBody: (body: any) => boolean;
  },
): Promise<boolean> => {
  const { urlIncludes, method, validateBody } = options;
  if (res.url().includes(urlIncludes) && res.request().method() === method) {
    try {
      const body = await res.json();
      return validateBody(body);
    } catch (e) {
      return false;
    }
  }
  return false;
};
