import { expect, test } from '@playwright/test';
import { waitForApiResponse } from './testUtils';

test.beforeEach(async ({ page }) => {
  await page.goto('http://localhost:8081');
});

test.describe('Sign up', async () => {
  test('renders correctly', async ({ page }) => {
    await expect(page.getByText('MAKING MATH A SPORT')).toBeVisible();
  });

  test('can guest login', async ({ page }) => {
    let userName = '';
    const [apiResponse] = await Promise.all([
      page.waitForResponse((res) =>
        waitForApiResponse(res, {
          urlIncludes: '/api',
          method: 'POST',
          validateBody: (body) => !!body?.data?.guestUser,
        }),
      ),
      page.getByText('Play as guest').click(),
    ]);

    const responseBody = await apiResponse.json();

    if (responseBody?.data?.guestUser) {
      userName = responseBody?.data?.guestUser?.username;
    }

    await page.locator('#root').getByText('My Profile').click();
    const guestUsername = await page
      .getByText(`${userName}`)
      .nth(1)
      .textContent();
    expect(guestUsername).toBe(userName);
  });
});
