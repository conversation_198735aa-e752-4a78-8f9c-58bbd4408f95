import { test, expect } from '@playwright/test';
import { AUTH_STORAGE_KEY, MOCK_AUTH_TOKEN_USER1 } from './testConstants';
import { waitForApiResponse } from './testUtils';
import { decryptJsonData } from '@/src/core/utils/encryptions/decrypt';
import _map from 'lodash/map';

test.beforeEach(async ({ page }) => {
  await page.addInitScript(
    (tokenData) => {
      window.localStorage.setItem(tokenData.key, tokenData.token);
    },
    { key: AUTH_STORAGE_KEY, token: MOCK_AUTH_TOKEN_USER1 },
  );
  await page.goto('http://localhost:8081/home');
});

test.describe('League Page Tests', () => {
  test('should complete virtual contest successfully', async ({ page }) => {
    test.setTimeout(150000);
    await page.getByText('Compete').click();
    await page.getByText('80 in 8 Contest').click();
    const [apiResponse] = await Promise.all([
      page.waitForResponse((res) =>
        waitForApiResponse(res, {
          urlIncludes: '/api',
          method: 'POST',
          validateBody: (body) => !!body?.data?.getContestById,
        }),
      ),
      page.getByText('virtual contest').first().click(),
    ]);
    const takeVirtualContestButton = page.getByTestId(
      'take-virtual-contest-card',
    );
    await expect(takeVirtualContestButton).toBeVisible();
    await takeVirtualContestButton.click();
    const responseBody = await apiResponse.json();
    if (responseBody?.data?.getContestById) {
      let questions: any = [];
      const { encryptedQuestions } =
        responseBody.data.getContestById ?? EMPTY_OBJECT;
      questions = encryptedQuestions
        ? _map(encryptedQuestions, decryptJsonData)
        : [];
      await page.waitForTimeout(5000);
      for (const question of questions) {
        const { answers } = question.question;
        const textbox = page.getByRole('textbox', { name: 'Enter Answer' });
        const randomTimeout = Math.floor(Math.random() * 1001) + 1000;
        await page.waitForTimeout(randomTimeout);
        if (await textbox.isVisible()) {
          await textbox.fill(answers[0]);
        }
      }
      await expect(
        page.getByText('Congratulations on completing the contest!'),
      ).toBeVisible();
      await expect(page.getByText('total score')).toBeVisible();
      await expect(page.getByText('total time')).toBeVisible();
    }
  });

  test('should complete live contest successfully', async ({ page }) => {
    await page.getByTestId('left-pane-navigator-3').click();
    await page.getByText('80 in 8 Contest').click();
    const [apiResponse] = await Promise.all([
      page.waitForResponse((res) =>
        waitForApiResponse(res, {
          urlIncludes: '/api',
          method: 'POST',
          validateBody: (body) => !!body?.data?.getContestById,
        }),
      ),
      page.getByText('JOIN NOW').first().click(),
    ]);
    const isRegisterButtonVisible = await page.getByText('Register Now').isVisible();
    if (isRegisterButtonVisible) {
        await page.getByText('Register Now').click(); 
    }
    await page.getByTestId('join-now-button').click();
    const responseBody = await apiResponse.json();
    if (responseBody?.data?.getContestById) {
      let questions: any = [];
      const { encryptedQuestions } =
        responseBody.data.getContestById ?? EMPTY_OBJECT;
      questions = encryptedQuestions
        ? _map(encryptedQuestions, decryptJsonData)
        : [];
      await page.waitForTimeout(5000);
      for (const question of questions) {
        const { answers } = question.question;
        const textbox = page.getByRole('textbox', { name: 'Enter Answer' });
        if (await textbox.isVisible()) {
          await textbox.fill(answers[0]);
        }
      }
      await expect(
        page.getByText('Congratulations on completing the contest!'),
      ).toBeVisible();
      await expect(page.getByText('total score')).toBeVisible();
      await expect(page.getByText('total time')).toBeVisible();
    }
  });
});
