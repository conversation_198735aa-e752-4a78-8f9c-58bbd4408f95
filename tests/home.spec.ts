import { expect, test } from '@playwright/test';
import { AUTH_STORAGE_KEY, MOCK_AUTH_TOKEN_USER1 } from './testConstants';

test.beforeEach(async ({ page }) => {
  await page.addInitScript(
    (tokenData) => {
      window.localStorage.setItem(tokenData.key, tokenData.token);
    },
    { key: AUTH_STORAGE_KEY, token: MOCK_AUTH_TOKEN_USER1 },
  );
  await page.goto('http://localhost:8081/home');
});

test.describe('Home Page Tests (Authenticated)', () => {
  test('should able to update profile', async ({ page }) => {
    const username = `testuser${(Math.random() * 1000).toFixed(0)}`;
    await page.getByTestId('user-profile-tooltip').click();
    await page.getByRole('tooltip').getByText('My Profile').click();
    await page.getByTestId('edit-profile').click();
    await page.getByPlaceholder('Enter User Name').fill(username);
    await page.getByTestId('country-dropdown').click();
    await page.getByPlaceholder('search country').fill('India');
    await page.getByTestId('India').locator('div').nth(1).click();
    await page.getByText('Save').click();
    await page.getByTestId('confirm-button').click();
    await page.waitForTimeout(100);
    const usernameText = await page.getByTestId('user-username').textContent();
    expect(usernameText).toBe(`@${username}`);
  });

  test('should logout', async ({ page }) => {
    await page.getByTestId('user-profile-tooltip').click();
    await page.getByRole('tooltip').getByText('Logout').click();
    await expect(page).toHaveURL('http://localhost:8081');
    const token = await page.evaluate((key) => window.localStorage.getItem(key), AUTH_STORAGE_KEY);
    expect(token).toBeNull();
  });
});
