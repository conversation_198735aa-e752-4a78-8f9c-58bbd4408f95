import { test, expect } from '@playwright/test';
import { MOCK_AUTH_TOKEN_USER1, AUTH_STORAGE_KEY } from './testConstants';
import _map from 'lodash/map';
import { decryptJsonData } from '@/src/core/utils/encryptions/decrypt';
import { waitForApiResponse } from './testUtils';
import { GAME_TYPES } from '@/src/core/constants/gameTypes';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import {
  ABILITY_QUESTION_CATEGORY,
  PRESET_IDENTIFIER_MAP,
} from '@/src/core/constants/questionCategories';

test.beforeEach(async ({ page }) => {
  await page.addInitScript(
    (tokenData) => {
      window.localStorage.setItem(tokenData.key, tokenData.token);
    },
    { key: AUTH_STORAGE_KEY, token: MOCK_AUTH_TOKEN_USER1 },
  );
  await page.goto('http://localhost:8081/home');
});

test.describe('Games Page Tests', () => {
  test('should complete 1 min duel successfully', async ({ page }) => {
    test.setTimeout(120000);
    await page.getByText('1 MIN DUEL').click();
    const [apiResponse] = await Promise.all([
      page.waitForResponse((res) =>
        waitForApiResponse(res, {
          urlIncludes: '/api',
          method: 'POST',
          validateBody: (body) => !!body?.data?.game,
        }),
      ),
      page.waitForTimeout(12000),
    ]);
    const responseBody = await apiResponse.json();
    let questions: any = [];
    if (responseBody?.data?.game) {
      const { encryptedQuestions } = responseBody.data.game ?? {};
      questions = encryptedQuestions
        ? _map(encryptedQuestions, decryptJsonData)
        : [];
    }
    await page.waitForTimeout(6000);
    for (const question of questions) {
      const { answers } = question.question;
      const textbox = page.getByRole('textbox', { name: 'Enter Answer' });
      if (await textbox.isVisible()) {
        await textbox.fill(answers[0]);
      }
    }
    await page.waitForTimeout(60000);
    const closeModalButton = page.getByTestId('close-modal-button');
    if (await closeModalButton.isVisible()) {
      await closeModalButton.click();
    }
    await page.getByTestId('close-popover').click();
    await page.getByTestId('web-back-button').click();
    await expect(page).toHaveURL('http://localhost:8081/home');
  });

  test('should complete ability duel successfully', async ({ page }) => {
    test.setTimeout(150000);
    await page
      .getByTestId(`${GAME_TYPES.ABILITY_DUELS}-start-game-button`)
      .click();
    const [apiResponse] = await Promise.all([
      page.waitForResponse((res) =>
        waitForApiResponse(res, {
          urlIncludes: '/api',
          method: 'POST',
          validateBody: (body) => !!body?.data?.game,
        }),
      ),
      // page.waitForTimeout(12000),
    ]);
    const responseBody = await apiResponse.json();
    if (responseBody?.data?.game) {
      const { encryptedQuestions } = responseBody.data.game ?? {};
      const questions = encryptedQuestions
        ? _map(encryptedQuestions, decryptJsonData)
        : [];
      await page.waitForTimeout(6000);
      for (const question of questions) {
        const category =
          !_isNil(question.question.tags) && !_isEmpty(question.question.tags)
            ? question.question.tags[0]
            : PRESET_IDENTIFIER_MAP[
                question.question.presetIdentifier.split('_')[0]
              ];
        const { answers } = question.question;
        const textbox = page.getByRole('textbox', { name: 'Enter Answer' });
        if (await textbox.isVisible()) {
          if (category === ABILITY_QUESTION_CATEGORY.SUM_OF_SQUARES) {
            const answer = answers[0].split('+').map((item: string) => {
              return item.replace('²', '').trim();
            });
            const answerText = answer.join(' ');
            await textbox.fill(answerText);
          } else {
            await textbox.fill(answers[0]);
          }
        }
      }
      await page.getByTestId('close-popover').click();
      await page.getByTestId('web-back-button').click();
      await expect(page).toHaveURL('http://localhost:8081/home');
    }
  });

  test('on cancel should redirect to home', async ({ page }) => {
    await page.getByText('1 MIN DUEL').click();
    await page.getByText('CANCEL').click();
    await expect(page).toHaveURL('http://localhost:8081/home');
  });
});
