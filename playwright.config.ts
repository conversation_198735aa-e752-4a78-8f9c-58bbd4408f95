import { defineConfig, devices } from '@playwright/test'

/**
 globalSetup: require.resolve('./global-setup'), // Add global setup file
 * https://github.com/motdotla/dotenv
 */
// import dotenv from 'dotenv';
// import path from 'path';
// dotenv.config({ path: path.resolve(__dirname, '.env') });

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  timeout: 30000, // Set a reasonable global timeout (30 seconds)
  retries: process.env.CI ? 2 : 1, // Add 1 retry even in dev to handle flaky tests
  workers: process.env.CI ? '50%' : undefined, // Use 50% of available CPU cores in CI
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html'], // Keep HTML reporter
    ['junit', { outputFile: 'test-results/junit-results.xml' }], // Add JUnit for CI integration
    ['list'], // Add CLI reporting
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    video: 'retain-on-failure', // Save videos for failed tests
    storageState: 'tests/storage-state.json',
    screenshot: 'only-on-failure', // Take screenshots only on failures
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:8081/',
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    // Mobile browsers
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'yarn expo start',
    url: 'http://localhost:8081',
    reuseExistingServer: !process.env.CI,
  },
});
