const { getDefaultConfig } = require("expo/metro-config");
const { withTamagui } = require('@tamagui/metro-plugin');

// Determine if we're in production mode
const isProduction = process.env.NODE_ENV === 'production';

const config = getDefaultConfig(__dirname, {
  isCSSEnabled: true,
});

// Add .riv to asset extensions
config.resolver.assetExts.push("riv");

// Configure minification for production
if (isProduction) {
  // Use Terser for minification in production
  config.transformer.minifierPath = 'metro-minify-terser';
  config.transformer.minifierConfig = {
    // Terser options
    compress: {
      // Remove console.log in production
      drop_console: true,
      // More aggressive optimizations
      passes: 2,
    },
    mangle: {
      // Mangle variable names
      toplevel: true,
    },
    output: {
      // Remove comments and whitespace
      comments: false,
      // Beautify output (false = uglify)
      beautify: false,
    },
  };
}

module.exports = withTamagui(config, {
  components: ['tamagui'],
  config: './tamagui.config.ts',
  outputCSS: './tamagui-web.css',
})
