import React from 'react';
import FeaturesStoryView from 'modules/onboarding/pages/FeaturesStoryView';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { Redirect } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';

const FeaturesPreviewContainer = () => {
  const { session, user } = useSession();

  if (session && !_isEmpty(user)) {
    return <Redirect href="/" />;
  }

  return <FeaturesStoryView />;
};

export default React.memo(FeaturesPreviewContainer);
