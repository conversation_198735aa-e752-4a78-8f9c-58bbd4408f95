import React, { useEffect } from 'react';

import RatingFixture from 'modules/onboarding/pages/RatingFixture';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAMES } from 'core/constants/pageNames';

const Onboarding = () => {
  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.DISCOVER_RATING);
    Analytics.track(ANALYTICS_EVENTS.ONBOARDING.VIEWED_DISCOVER_RATING);
  }, []);

  return <RatingFixture />;
};

export default Onboarding;
