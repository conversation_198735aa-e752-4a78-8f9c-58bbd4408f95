import React from 'react';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { Stack, Tabs } from 'expo-router';
import { Platform, View } from 'react-native';
import { BottomTabBar } from '@react-navigation/bottom-tabs';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Dark from 'core/constants/themes/dark';
import LeftPaneNavigator from '@/src/navigator/LeftPaneNavigator';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';

export const WebLayout = () => (
  <View style={{ flex: 1, flexDirection: 'row' }}>
    <LeftPaneNavigator />
    <Stack screenOptions={{ headerShown: false }} />
  </View>
);

export const TabLayout = () => (
  <SafeAreaProvider>
    <Tabs
      initialRouteName="home"
      screenOptions={{
        tabBarActiveTintColor: Dark.colors.secondary,
        animation: 'shift',
        tabBarStyle: {
          minHeight: APP_LAYOUT_CONSTANTS.BOTTOM_TAB_BAR_HEIGHT,
          height: APP_LAYOUT_CONSTANTS.BOTTOM_TAB_BAR_HEIGHT,
          maxHeight: APP_LAYOUT_CONSTANTS.BOTTOM_TAB_BAR_MAX_HEIGHT,
          paddingBottom: 10,
          paddingTop: 10,
          marginBottom: Platform.OS === 'ios' ? -10 : 0,
          backgroundColor:
            Platform.OS === 'ios'
              ? Dark.colors.background
              : Dark.colors.primary,
        },
        tabBarBackground: () => (
          <View style={{ backgroundColor: Dark.colors.primary }} />
        ),
        headerShown: false,
        tabBarLabelPosition: 'below-icon',
      }}
      tabBar={(props) => <BottomTabBar {...props} />}
    >
      <Tabs.Screen
        name="home"
        options={{
          href: '/home',
          title: 'Arena',
          tabBarIcon: ({ color }) => (
            <MaterialIcons name="stadium" size={20} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="puzzle-home"
        options={{
          href: '/puzzle-home',
          title: 'Puzzles',
          tabBarIcon: ({ color }) => (
            <FontAwesome6 name="puzzle-piece" size={18} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="contests"
        options={{
          href: '/contests',
          title: 'Compete',
          tabBarIcon: ({ color }) => (
            <FontAwesome6 name="trophy" size={18} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="nets"
        options={{
          href: '/nets',
          title: 'Nets',
          tabBarIcon: ({ color }) => (
            <FontAwesome5 name="dumbbell" size={18} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="options"
        options={{
          href: '/options',
          title: 'More',
          tabBarIcon: ({ color }) => (
            <MaterialIcons size={20} name="menu" color={color} />
          ),
        }}
      />
    </Tabs>
  </SafeAreaProvider>
);

export default function () {
  const { isMobile } = useMediaQuery();

  return isMobile ? <TabLayout /> : <WebLayout />;
}
