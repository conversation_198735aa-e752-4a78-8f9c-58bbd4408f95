/* eslint-disable react/function-component-definition */
import { Stack } from 'expo-router';
import React from 'react';

import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { View } from 'react-native';
import LeftPaneNavigator from '@/src/navigator/LeftPaneNavigator';

const StackLayout = () => <Stack screenOptions={{ headerShown: false }} />;

const ExpandedScreenLeftPaneNavigator = () => (
  <View style={{ flex: 1, flexDirection: 'row' }}>
    <LeftPaneNavigator />
    <Stack screenOptions={{ headerShown: false }} />
  </View>
);

export default function HomeLayout() {
  const { isMobile } = useMediaQuery();

  return isMobile ? <StackLayout /> : <ExpandedScreenLeftPaneNavigator />;
}
