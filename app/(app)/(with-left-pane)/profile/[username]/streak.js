import Streak from 'modules/profile/pages/Streak';
import { useEffect } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import userReader from 'core/readers/userReader';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { PAGE_NAMES } from 'core/constants/pageNames';

export default function () {
  const { user } = useSession();

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.VIEWED_STREAK_PAGE, {
      currentStreak: userReader.currentStreak(user),
      longestStreak: userReader.longestStreak(user),
    });
    webengage?.screen?.(PAGE_NAMES.STREAK_DETAIL_PAGE);
  }, []);

  return <Streak />;
}
