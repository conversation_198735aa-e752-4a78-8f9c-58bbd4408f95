import React from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { Redirect } from 'expo-router';
import { showRightPane } from 'molecules/RightPane/RightPane';
import OnlineUsersPage from '../../../src/modules/home/<USER>/OnlineUsersPage';

const OnlineUsers = () => {
  const { isMobile: isCompactMode } = useMediaQuery();

  if (!isCompactMode) {
    showRightPane({
      content: <OnlineUsersPage />,
    });
    return <Redirect href="/home" />;
  }

  return <OnlineUsersPage />;
};

export default React.memo(OnlineUsers);
