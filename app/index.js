import 'atoms/gesture-handler';
import { Redirect } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { getStorageState } from 'core/hooks/useStorageState';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _isEmpty from 'lodash/isEmpty';
import _startsWith from 'lodash/startsWith';
import useUserStore from 'store/useUserStore';
import OfflineScreen from 'shared/OfflineScreen';
import LandingPage from 'modules/unauth/pages/LandingPage';
import SplashScreen from 'modules/unauth/pages/SplashScreen';
import Loading from 'atoms/Loading';
import { INITIAL_ROUTE_KEY } from 'core/constants/appConstants';
import { PAGE_NAMES } from 'core/constants/pageNames';
import useNetworkStatus from 'core/hooks/useNetworkStatus';

const App = () => {
  const { session, isLoading, user } = useSession();
  const { isMobile: isCompactDevice } = useMediaQuery();
  const { isOnline, toggleMode } = useUserStore((state) => ({
    isOnline: state.isOnline,
    toggleMode: state.toggleMode,
  }));
  const { isMatiksReachable } = useNetworkStatus();

  const [redirectTo, setRedirectTo] = useState();

  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.PUBLIC_HOME_PAGE);
    getStorageState(INITIAL_ROUTE_KEY).then((initialRoute) => {
      if (_startsWith(initialRoute, '/game/')) {
        setRedirectTo(initialRoute);
      }
    });
  }, []);

  useEffect(() => {
    if (isOnline && !isMatiksReachable) {
      toggleMode?.(false);
    }
  }, [isOnline, isMatiksReachable]);

  if (!isOnline) {
    return <OfflineScreen />;
  }

  if (isLoading) {
    return <Loading />;
  }

  if (session && !_isEmpty(user)) {
    const { isSignup = false, isGuest } = user;
    if (redirectTo) {
      return <Redirect href={redirectTo} />;
    }
    if (isSignup && !isGuest) {
      return <Redirect href="/onboarding/referral" />;
    }
    return <Redirect href="/home" />;
  }

  if (isCompactDevice) {
    return <SplashScreen />;
  }

  return <LandingPage />;
};

export default React.memo(App);
