{"buildCommand": "expo export -p web", "outputDirectory": "dist", "devCommand": "expo", "cleanUrls": true, "framework": null, "rewrites": [{"source": "/:path*", "destination": "/"}], "headers": [{"source": "/rive/(.*).riv", "headers": [{"key": "Content-Type", "value": "application/octet-stream"}]}, {"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://widgets.in.webengage.com https://cdn.matiks.com https://cdn.jsdelivr.net https://notify.bugsnag.com https://accounts.google.com https://apis.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https:; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://matiks.com https://www.matiks.com https://server.matiks.com https://cdn.matiks.com https://dev.matiks.com https://dev.server.matiks.com https://widgets.in.webengage.com https://api.mixpanel.com https://cdn.jsdelivr.net https://accounts.google.com https://oauth2.googleapis.com https://notify.bugsnag.com https://sessions.bugsnag.com wss://matiks.com wss://server.matiks.com wss://dev.matiks.com wss://dev.server.matiks.com ws://localhost:4000 http://localhost:4000 https://localhost:4000; frame-src 'self' https://accounts.google.com; frame-ancestors 'self'; object-src 'none'; base-uri 'self'; form-action 'self'"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Strict-Transport-Security", "value": "max-age=********; includeSubDomains; preload"}]}]}